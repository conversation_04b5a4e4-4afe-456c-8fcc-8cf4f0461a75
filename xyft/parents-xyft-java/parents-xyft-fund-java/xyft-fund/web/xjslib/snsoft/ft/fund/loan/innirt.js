Xjs.loadedXjs.push("snsoft/ft/fund/loan/innirt");
Xjs.namespace("snsoft.ft.loan.innirt.invoker");snsoft.ft.loan.innirt.invoker.LoanInnerIrtWorkBenchInvoker=function(parameter){snsoft.ft.loan.innirt.invoker.LoanInnerIrtWorkBenchInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.loan.innirt.invoker.LoanInnerIrtWorkBenchInvoker,snsoft.ext.cmd.CommandInvoker,{beforeCheck:function(event){var rows=this.table.getSelectedRowNumbers(),params={},innirticodes=[];if(rows==null||rows.length==0){throw new Error(Xjs.ResBundle.getResVal("FT.00000048"));}var btn_name=event.cfg.cmd,ispushinv_comf=btn_name=="pushInvoicePlat"?"N":(btn_name=="sendPipCancel"?"Y":"NULL");for(var i=0;i<rows.length;i++){var r=rows[i],ispushinv_comt=this.dataSet.getValue("ispushinv",r);if(ispushinv_comf!=ispushinv_comt){if(ispushinv_comf=="N"){throw new Error(Xjs.ResBundle.getResVal("FT-LOAN.00000001"));}else if(ispushinv_comf=="Y"){throw new Error(Xjs.ResBundle.getResVal("FT-LOAN.00000002"));}}innirticodes.push(this.mainDataSet.getValue("innirticode",rows[i]));}params.btn_name=btn_name;params.innirticodes=innirticodes;return params;},check:function(event){this.mainTable.refreshTable();return null;}});Xjs.namespace("snsoft.ft.loan.innirt.lis");snsoft.ft.loan.innirt.lis.LoanInnerIrtLoadInitJSListener=function(params){snsoft.ft.loan.innirt.lis.LoanInnerIrtLoadInitJSListener.superclass.constructor.call(this,params);};Xjs.extend(snsoft.ft.loan.innirt.lis.LoanInnerIrtLoadInitJSListener,snsoft.plat.bas.busi.SystemFunctionListener,{initComponent:function(table,values){snsoft.ft.loan.innirt.lis.LoanInnerIrtLoadInitJSListener.superclass.initComponent.call(this,table,values);if(this.mainTable==null){this.mainTable=table;this.mainDataSet=this.mainTable.dataSet;if(this.innirtBas==null){this.innirtBas=this.getTable(table,"ft_loan_innirt_bas");this.innirtBasDataSet=this.innirtBas.getDataSet();}if(this.loanInnirtg==null){this.loanInnirtg=this.getTable(table,"ft_loan_innirtg");this.loanInnirtgDataSet=this.loanInnirtg.getDataSet();}}},onDataLoad:function(dataSet,e){snsoft.ft.loan.innirt.lis.LoanInnerIrtLoadInitJSListener.superclass.onDataLoad.call(this,dataSet,e);},dataSetFieldPosted:function(dataSet,e){snsoft.ft.loan.innirt.lis.LoanInnerIrtLoadInitJSListener.superclass.dataSetRowNavigating.call(this,dataSet,e);if(e.columnName=="corpbcode"){var corpbcode=this.mainDataSet.getValue("corpbcode"),service=Xjs.RInvoke.newBean("FT-LOAN.LoanInnerIrtUIService"),sfcode=service.getCorpbcodeSfcode(corpbcode);this.mainDataSet.setValue("sfcode",sfcode);}}});Xjs.RInvoke.beansDef["FT-LOAN.LoanInnerIrtUIService"]={getCorpbcodeSfcode:{}};