Xjs.loadedXjs.push("snsoft/ft/fund/cps/vou");
Xjs.namespace("snsoft.ft.cps.vou.invoker");snsoft.ft.cps.vou.invoker.GenMonthEndStockRedVouInvoker=function(parameter){snsoft.ft.cps.vou.invoker.GenMonthEndStockRedVouInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.cps.vou.invoker.GenMonthEndStockRedVouInvoker,snsoft.plat.bas.sheet.cmd.red.CommandRed,{beforeCheck:function(event){var row=this.mainDataSet.getRowCount();for(var i=0;i<row;i++){if(this.mainDataSet.getValue("vidflag",i)!=null&&this.mainDataSet.getValue("vidflag",i)=="0"){throw Xjs.ResBundle.getResVal("FT-CPS.00000014");}if(this.mainDataSet.getValue("redflag",i)!=null&&this.mainDataSet.getValue("redflag",i)!="0"){throw Xjs.ResBundle.getResVal("FT-CPS.00000015");}}var vmarkicodes=Xjs.util.DataSetUtils.getColumnStrValuesAllRow_DataSet_String(this.mainDataSet,"vmarkicode");return {vmarkicode:this.mainDataSet.get("vmarkicode"),tcaptime:this.mainDataSet.get("tcaptime"),vmarkicodes:vmarkicodes};},afterInvoke:function(event){this.mainTable.refreshTable();}});snsoft.ft.cps.vou.invoker.GenMonthEndStockVoucher=function(parameter){snsoft.ft.cps.vou.invoker.GenMonthEndStockVoucher.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.cps.vou.invoker.GenMonthEndStockVoucher,snsoft.ext.cmd.CommandInvoker,{beforeCheck:function(event){var queryParamPanel=this.mainTable.queryParam,InOutParam={};InOutParam.corpbcode=queryParamPanel.getItemByName("corpbcode").getValue();InOutParam.bcode=queryParamPanel.getItemByName("bcode").getValue();var cpsInOutResult=Xjs.RInvoke.rsvInvoke("FT-CPS.CpsMonthEndStockUIService.loadCpsInOutDataByParam",InOutParam);for(var i=0;cpsInOutResult!=null&&i<cpsInOutResult.length;i++){var cpsInOut=cpsInOutResult[i];if("0"==cpsInOut.vidflag){throw new Error(Xjs.ResBundle.getResVal("FT-CPS.00000011"));}}return null;}});snsoft.ft.cps.vou.invoker.GetReValuationMonth=function(parameter){snsoft.ft.cps.vou.invoker.GetReValuationMonth.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.cps.vou.invoker.GetReValuationMonth,snsoft.ext.cmd.CommandInvoker,{check:function(event){return this.newDialogWithFunc(event,(e)=>{var dialogPane=Xjs.ui.UIUtil.loadDialog("FT-CPS.CpsMonthEndStockDlg",0,null,null,null,null);dialogPane.title=this.cfg.title;dialogPane.setWidth("auto");dialogPane.setHeight("auto");dialogPane.showModal();return dialogPane;},(e,d)=>{var queryParamPanel=this.mainTable.queryParam,queryParam={};queryParam.year=queryParamPanel.getItemByName("year").getValue();queryParam.month=queryParamPanel.getItemByName("month").getValue();queryParam.corpbcode=queryParamPanel.getItemByName("corpbcode").getValue();queryParam.bcode=queryParamPanel.getItemByName("bcode").getValue();var result=Xjs.RInvoke.rsvInvoke("FT-CPS.CpsMonthEndStockUIService.loadDataByParam",queryParam);for(var i=0;result!=null&&i<result.length;i++){var object=result[i];if("2"!=object.redflag&&"5"!=object.redflag&&"0"!=object.vidflag){throw new Error(Xjs.ResBundle.getResVal("FT-CPS.00000012"));}}var InOutParam={};InOutParam.corpbcode=queryParamPanel.getItemByName("corpbcode").getValue();InOutParam.bcode=queryParamPanel.getItemByName("bcode").getValue();var cpsInOutResult=Xjs.RInvoke.rsvInvoke("FT-CPS.CpsMonthEndStockUIService.loadCpsInOutDataByParam",InOutParam);for(var i=0;cpsInOutResult!=null&&i<cpsInOutResult.length;i++){var cpsInOut=cpsInOutResult[i];if("0"==cpsInOut.vidflag){throw new Error(Xjs.ResBundle.getResVal("FT-CPS.00000011"));}}var table=Xjs.util.TableUtils.getTable_$Panel_$String(d,"chooseRevalueMonth");table.postPending();table.checkNonBlankForSubmit();var rows=this.mainTable.getSelectedRowNumbers(),innercodes=[];for(var i=0;i<rows.length;i++){innercodes.push(this.mainTable.getValue("mericode",rows[i]));}var reValParams={};reValParams.year=table.getValue("year");reValParams.month=table.getValue("month");reValParams.innercodes=innercodes;event.checkData.params=reValParams;});},invoke:function(event){return event.checkData.params;}});Xjs.namespace("snsoft.ft.cps.vou.lis");snsoft.ft.cps.vou.lis.handleMonthAndYearListener=function(params){snsoft.ft.cps.vou.lis.handleMonthAndYearListener.superclass.constructor.call(this,params);};Xjs.extend(snsoft.ft.cps.vou.lis.handleMonthAndYearListener,snsoft.plat.bas.busi.SystemFunctionListener,{initComponent:function(table,values){snsoft.ft.cps.vou.lis.handleMonthAndYearListener.superclass.initComponent.call(this,table,values);},dataLoaded:function(dataSet,e){snsoft.ft.cps.vou.lis.handleMonthAndYearListener.superclass.dataLoaded.call(this,dataSet,e);dataSet.insertRow(1);var month=Xjs.util.DateUtils.nowDay().getMonth();if(month==0){dataSet.setValue("month",12);dataSet.setValue("year",Xjs.util.DateUtils.nowDay().getFullYear()-1);}else {dataSet.setValue("month",month);dataSet.setValue("year",Xjs.util.DateUtils.nowDay().getFullYear());}}});