Xjs.loadedXjs.push("snsoft/ft/fund/forex/bank");
Xjs.namespace("snsoft.ft.forex.bank.invoker");snsoft.ft.forex.bank.invoker.CreateForexBvalRedVfldInvoker=function(parameter){snsoft.ft.forex.bank.invoker.CreateForexBvalRedVfldInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.forex.bank.invoker.CreateForexBvalRedVfldInvoker,snsoft.ext.cmd.CommandInvoker,{check:function(event){var rows=this.table.getSelectedRowNumbers();if(!rows||rows.length==0){throw new Error(Xjs.ResBundle.getResVal("FT.********"));}else if(rows.length>1){throw new Error(Xjs.ResBundle.getResVal("FT-FOREX.********"));}var vmarkicode=this.dataSet.getValue("vmarkicode",rows[0]);return this.newDialogWithFunc(event,(e)=>{var dialogPane=Xjs.ui.UIUtil.loadDialog("FT-FOREX.ForexBvalEntryRedDlg",0,null,{width:325,height:120},null,null);dialogPane.title=this.cfg.title;dialogPane.showModal();return dialogPane;},(e,d)=>{var tables=Xjs.util.TableUtils.getAllTablesFromComponent(d,false),param={};if(tables&&tables.length>0){for(var i=0,len=tables.length;i<len;i++){var t=tables[i];t.postPending();if(!t.dataSet.isChanged(false))t.dataSet.setRowChanged(true);t.checkNonBlankForSubmit();if("forexBvalRedDlg"==t.name){param.tcaptime=t.getDataSet().getValue("tcaptime");param.vmarkicode=vmarkicode;}}event.checkData.param=param;}});},invoke:function(event){var invoker=this;this.cfg.progFunc=(pm)=>{invoker.beforeDoProgress(pm);};return event.checkData.param;},beforeDoProgress:function(pm){pm.title=this.cfg.title;pm.options=1|2|8;pm.errShowTopLayer=true;},afterInvoke:function(event){this.dataSet.refresh();this.mainDataSet.refresh();this.mainTable.refreshTableIfOK();}});snsoft.ft.forex.bank.invoker.GetBankValuationInvoker=function(parameter){snsoft.ft.forex.bank.invoker.GetBankValuationInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.forex.bank.invoker.GetBankValuationInvoker,snsoft.ext.cmd.CommandInvoker,{check:function(event){return {type:2,showConfirm:new Xjs.FuncCall((fcall,ie)=>{var funcCall=new Xjs.FuncCall(this.confirmDeal,this,[fcall,ie]),dialogPane=Xjs.ui.UIUtil.loadDialog("FT-FOREX.ForexBvalEntryDlg");dialogPane.title=event.cfg.title;dialogPane.addListener("onOk",funcCall);dialogPane.showModal();},this,[event])};},confirmDeal:function(dialog,button,command,funcCall,event){if("ok"==command){var tables=Xjs.util.TableUtils.getAllTablesFromComponent(dialog,false);if(tables&&tables.length>0){var param={};for(var i=0,len=tables.length;i<len;i++){var t=tables[i];t.postPending();if(!t.dataSet.isChanged(false))t.dataSet.setRowChanged(true);t.checkNonBlankForSubmit();if("forexBvalDlg"==t.name){param.corpbcode=t.getDataSet().getValue("corpbcode");param.tcaptime=t.getDataSet().getValue("tcaptime");}}var result=Xjs.RInvoke.rsvInvoke("FT-FOREX.ForexBvalUIService.loadMarkByParam",param),exist=false,srcicodes=[];for(var i=0;result!=null&&i<result.length;i++){var object=result[i];if("2"!=object.redflag&&"5"!=object.redflag&&"0"!=object.vidflag){throw new Error(Xjs.ResBundle.getResVal("FT-FOREX.********"));}if("0"==object.vidflag)exist=true;srcicodes[i]=object.srcicode;}if(exist){param.srcicodes=srcicodes;var onOk=this.confirmGetBankValuation.createDelegate(this,[event,param,funcCall],true);Xjs.ui.UIUtil.showConfirmDialog("确定",this.getResVal("FT-FOREX.********"),onOk,null);}else {event.checkData.param=param;funcCall.call(param,"ok");}}}},confirmGetBankValuation:function(dialog,command,event,param,funcCall){if("ok"==command){event.checkData.param=param;funcCall.call(param,"ok");}},invoke:function(event){return event.checkData.param;},afterInvoke:function(event){this.dataSet.refresh();this.mainDataSet.refresh();this.mainTable.refreshTableIfOK();}});