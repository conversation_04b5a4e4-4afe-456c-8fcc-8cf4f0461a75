Xjs.loadedXjs.push("snsoft/ft/fund/lc/reg");
/*snsoft/ft/lc/reg/invoker/LcRegSyncSalesAmountInvoker.java*/
Xjs.namespace("snsoft.ft.lc.reg.invoker");
snsoft.ft.lc.reg.invoker.LcRegSyncSalesAmountInvoker=function(parameter){
    snsoft.ft.lc.reg.invoker.LcRegSyncSalesAmountInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.lc.reg.invoker.LcRegSyncSalesAmountInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.lc.reg.invoker.LcRegSyncSalesAmountInvoker",
    /*snsoft.ft.lc.reg.invoker.LcRegSyncSalesAmountInvoker.invoke*/
    invoke:function(event)
    {
        var salordssicodeList = [];
        for(var i=0;i < this.dataSet.getRows().length;i++)
        {
            salordssicodeList[i] = this.dataSet.getValue("lcreggicode",i) + "," + this.dataSet.getValue("salordssicode",i);
        }
        return {salordssicodeList:salordssicodeList,lcregicode:this.mainDataSet.getValue("lcregicode"),modifydate:this.mainDataSet.get("modifydate")};
    },
    /*snsoft.ft.lc.reg.invoker.LcRegSyncSalesAmountInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        this.dataSet.refresh();
        this.mainTable.refreshTableIfOK();
    }
});
/*snsoft/ft/lc/reg/lis/LcRegJSListener.java*/
Xjs.namespace("snsoft.ft.lc.reg.lis");
snsoft.ft.lc.reg.lis.LcRegJSListener=function(params){
    snsoft.ft.lc.reg.lis.LcRegJSListener.superclass.constructor.call(this,params);
};
Xjs.extend(snsoft.ft.lc.reg.lis.LcRegJSListener,snsoft.plat.bas.busi.SystemFunctionListener,{
  _js$className_:"snsoft.ft.lc.reg.lis.LcRegJSListener",
    /*snsoft.ft.lc.reg.lis.LcRegJSListener.initComponent*/
    initComponent:function(table,values)
    {
        snsoft.ft.lc.reg.lis.LcRegJSListener.superclass.initComponent.call(this,table,values);
        if(!this.letterOfCreditInfTwoTable)
        {
            this.letterOfCreditInfTwoTable = this.getTable(table,"letterOfCreditInf_2");
        }
    },
    /*snsoft.ft.lc.reg.lis.LcRegJSListener.dataSetRowNavigated*/
    dataSetRowNavigated:function(dataSet,e)
    {
        var overshiprate = this.letterOfCreditInfTwoTable.getColumn("overshiprate");
        if(dataSet.get("isosrate") == "Y")
        {
            overshiprate.nonBlankOnSubmit = true;
            overshiprate.setReadonly(false);
            overshiprate.ignoreTblRdonly = true;
        } else if(dataSet.get("isosrate") == "N")
        {
            dataSet.setValue("overshiprate",null);
            overshiprate.nonBlankOnSubmit = false;
            overshiprate.setReadonly(true);
            overshiprate.ignoreTblRdonly = false;
        } else if(!dataSet.get("isosrate") || dataSet.get("isosrate")===undefined)
        {
            dataSet.setValue("overshiprate",null);
            overshiprate.nonBlankOnSubmit = false;
            overshiprate.setReadonly(true);
        }
        if("S01"==dataSet.get("vsntype"))
        {
            overshiprate.setReadonly(true);
        }
        this.letterOfCreditInfTwoTable.render(2);
    },
    /*snsoft.ft.lc.reg.lis.LcRegJSListener.dataLoaded*/
    dataLoaded:function(dataSet,event)
    {
        snsoft.ft.lc.reg.lis.LcRegJSListener.superclass.dataLoaded.call(this,dataSet,event);
    },
    /*snsoft.ft.lc.reg.lis.LcRegJSListener.dataSetFieldPosting*/
    dataSetFieldPosting:function(dataSet,event)
    {
        snsoft.ft.lc.reg.lis.LcRegJSListener.superclass.dataSetFieldPosting.call(this,dataSet,event);
        if(event.columnName == "overshiprate" && event.value)
        {
            var overshiprateVal = event.value;
            if(overshiprateVal < 0 || overshiprateVal > 1)
            {
                throw new Error(Xjs.ResBundle.getResVal("FT-LC.00000045"));
            }
        }
    },
    /*snsoft.ft.lc.reg.lis.LcRegJSListener.dataSetFieldPosted*/
    dataSetFieldPosted:function(dataSet,event)
    {
        var overshiprate = this.letterOfCreditInfTwoTable.getColumn("overshiprate");
        if(event.columnName == "isosrate" && dataSet.get("isosrate") == "Y")
        {
            overshiprate.nonBlankOnSubmit = true;
            overshiprate.setReadonly(false);
            overshiprate.ignoreTblRdonly = true;
        } else if(event.columnName == "isosrate" && dataSet.get("isosrate") == "N")
        {
            dataSet.setValue("overshiprate",null);
            overshiprate.nonBlankOnSubmit = false;
            overshiprate.setReadonly(true);
            overshiprate.ignoreTblRdonly = false;
        } else if(event.columnName == "isosrate" && !dataSet.get("isosrate"))
        {
            dataSet.setValue("overshiprate",null);
            overshiprate.nonBlankOnSubmit = false;
            overshiprate.setReadonly(true);
            overshiprate.ignoreTblRdonly = false;
        }
        if(event.columnName == "isosrate" && "S01"==dataSet.get("vsntype"))
        {
            overshiprate.setReadonly(true);
        }
        this.letterOfCreditInfTwoTable.render(2);
    }
});
