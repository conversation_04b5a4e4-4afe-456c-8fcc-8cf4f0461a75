Xjs.loadedXjs.push("snsoft/ft/fund/rdoc/dist");
/*snsoft/ft/rdoc/dist/invoker/DistAppDetailJSInvoker.java*/
Xjs.namespace("snsoft.ft.rdoc.dist.invoker");
snsoft.ft.rdoc.dist.invoker.DistAppDetailJSInvoker=function(parameter){
    snsoft.ft.rdoc.dist.invoker.DistAppDetailJSInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.rdoc.dist.invoker.DistAppDetailJSInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.rdoc.dist.invoker.DistAppDetailJSInvoker",
    /*snsoft.ft.rdoc.dist.invoker.DistAppDetailJSInvoker.beforeCheck*/
    beforeCheck:function(event)
    {
        var rows = this.table.getSelectedRowNumbers();
        if(!rows || rows.length == 0)
        {
            throw new Error(Xjs.ResBundle.getResVal("FT.00000048"));
        }
        for(var i=0;i < rows.length;i++)
        {
            var diststatus = this.dataSet.getValue("diststatus",rows[i]);
            if(!(diststatus == "10"))
            {
                throw new Error(Xjs.ResBundle.getResVal("FT-RDOC.00000020"));
            }
            if(diststatus == "30")
            {
                throw new Error(Xjs.ResBundle.getResVal("FT-RDOC.00000021"));
            }
        }
        return null;
    },
    /*snsoft.ft.rdoc.dist.invoker.DistAppDetailJSInvoker.check*/
    check:function(event)
    {
        return {type:1,title:this.getTitle(),prompt:Xjs.ResBundle.getResVal("FT-RDOC.10000001"),perform:new Xjs.FuncCall(this._resolve,this,[event])};
    },
    /*snsoft.ft.rdoc.dist.invoker.DistAppDetailJSInvoker._resolve*/
    _resolve:function(event)
    {
        event.checkData.flag = "yes";
    },
    /*snsoft.ft.rdoc.dist.invoker.DistAppDetailJSInvoker.invoke*/
    invoke:function(event)
    {
        var flag = event.checkData.flag;
        if(!flag || "no"==flag)
        {
            var param = {},
                rows = this.table.getSelectedRowNumbers(),
                gicodes = [];
            for(var i=0;i < rows.length;i++)
            {
                var gicode = {};
                gicode.rdocdistgicode = this.dataSet.getValue("rdocdistgicode",rows[i]);
                gicodes.push(gicode);
            }
            param.gicodes = gicodes;
            var mains = [],
                main = {};
            main.rdocdisticode = this.mainDataSet.getValue("rdocdisticode");
            main.sheetcode = this.mainDataSet.getValue("sheetcode");
            main.modifydate = this.mainDataSet.getValue("modifydate");
            mains.push(main);
            param.rdocdisticode = this.mainDataSet.getValue("rdocdisticode");
            param.mains = mains;
            return param;
        }
        return null;
    },
    /*snsoft.ft.rdoc.dist.invoker.DistAppDetailJSInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        this.mainTable.refreshTable();
    }
});
