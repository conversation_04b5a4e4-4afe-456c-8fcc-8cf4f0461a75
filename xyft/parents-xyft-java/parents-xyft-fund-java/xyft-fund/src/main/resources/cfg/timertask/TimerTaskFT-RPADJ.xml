<?xml version="1.0" encoding="UTF-8"?>
<timertask-list xmlns="http://www.snsoft.com.cn/schema/TimerTask"
                xsi:schemaLocation="http://www.snsoft.com.cn/schema/TimerTask http://www.snsoft.com.cn/schema/TimerTask.xsd"
                xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <timertask id="FT-RPADJ.001" name="预付(货款)自动冲销定时任务" work-time="0:0" interval="0" auto-start="false" thread="true" task-type="java">
        <task><![CDATA[
			snsoft.ft.rpadj.advoff.timer.AutoRecPayLrpGenAdvOffTimer.new?taskid=FT-RPADJ.001&advofftype=10
		]]></task>
        <remark><![CDATA[
            可配置参数：
                 bcode:部门；按照路径向下匹配；
                 corpbcode:公司,按照路径向下匹配；
                 days:查询数据最近天数；
            其他参数类需配置：
            	{'env.USERCUICODE':'XXX(商户)','env.USERCODE':'XXX(用户)'}
        ]]></remark>
    </timertask>
    <timertask id="FT-RPADJ.002" name="预付(费用)自动冲销定时任务" work-time="0:0" interval="0" auto-start="false" thread="true" task-type="java">
        <task><![CDATA[
        	snsoft.ft.rpadj.advoff.timer.AutoRecPayLrpGenAdvOffTimer.new?taskid=FT-RPADJ.002&advofftype=20
		]]></task>
        <remark><![CDATA[
            可配置参数：
                 bcode:部门；按照路径向下匹配；
                 corpbcode:公司,按照路径向下匹配；
                 days:查询数据最近天数；
            其他参数类需配置：
                {'env.USERCUICODE':'XXX(商户)','env.USERCODE':'XXX(用户)'}
        ]]></remark>
    </timertask>
    <timertask id="FT-RPADJ.003" name="预收(货款)自动冲销定时任务" work-time="0:0" interval="0" auto-start="false" thread="true" task-type="java">
        <task><![CDATA[
			snsoft.ft.rpadj.advoff.timer.AutoRecPayLrpGenAdvOffTimer.new?taskid=FT-RPADJ.003&advofftype=30
		]]></task>
        <remark><![CDATA[
            可配置参数：
                 bcode:部门；按照路径向下匹配；
                 corpbcode:公司,按照路径向下匹配；
                 days:查询数据最近天数；
            其他参数类需配置：
                {'env.USERCUICODE':'XXX(商户)','env.USERCODE':'XXX(用户)'}
        ]]></remark>
    </timertask>
</timertask-list>