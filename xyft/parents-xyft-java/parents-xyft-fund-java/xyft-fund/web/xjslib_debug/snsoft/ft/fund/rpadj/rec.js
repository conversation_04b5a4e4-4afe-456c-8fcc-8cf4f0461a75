Xjs.loadedXjs.push("snsoft/ft/fund/rpadj/rec");
/*snsoft/ft/rpadj/rec/invoker/AddRecLrpParamByAdjRecOffInvoker.java*/
Xjs.namespace("snsoft.ft.rpadj.rec.invoker");
snsoft.ft.rpadj.rec.invoker.AddRecLrpParamByAdjRecOffInvoker=function(parameter){
    snsoft.ft.rpadj.rec.invoker.AddRecLrpParamByAdjRecOffInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.rpadj.rec.invoker.AddRecLrpParamByAdjRecOffInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.rpadj.rec.invoker.AddRecLrpParamByAdjRecOffInvoker",
    /*snsoft.ft.rpadj.rec.invoker.AddRecLrpParamByAdjRecOffInvoker.check*/
    check:function(event)
    {
        var initParams = {},
            refreshParams = {},
            readonlyParams = [],
            offgDataset = Xjs.util.TableUtils.getTable_$Panel_$String(this.mainTable,"ft_rpadj_offg").dataSet;
        initParams.corpbcode = this.mainDataSet.getValue("corpbcode");
        readonlyParams.push("corpbcode");
        initParams.ccode = this.mainDataSet.getValue("ccode");
        readonlyParams.push("ccode");
        initParams.fcode = offgDataset.getValue("fcode");
        readonlyParams.push("fcode");
        if(this.isBatchAdj)
        {
            initParams.fcyingflag = "Y";
            readonlyParams.push("fcyingflag");
        }
        refreshParams.sheetcode = offgDataset.getValue("srcsheetcode");
        refreshParams.limitsheetcode = this.sheetcode;
        refreshParams.lrpflag = 1 + 4;
        refreshParams.ismatchorc = "Y";
        event.checkData = {initParams:initParams,readonlyParams:readonlyParams,refreshParams:refreshParams};
        return null;
    }
});
/*snsoft/ft/rpadj/rec/invoker/AddRecLrpParamByAdvRecOffInvoker.java*/
snsoft.ft.rpadj.rec.invoker.AddRecLrpParamByAdvRecOffInvoker=function(parameter){
    snsoft.ft.rpadj.rec.invoker.AddRecLrpParamByAdvRecOffInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.rpadj.rec.invoker.AddRecLrpParamByAdvRecOffInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.rpadj.rec.invoker.AddRecLrpParamByAdvRecOffInvoker",
    /*snsoft.ft.rpadj.rec.invoker.AddRecLrpParamByAdvRecOffInvoker.check*/
    check:function(event)
    {
        var initParams = {},
            refreshParams = {},
            readonlyParams = [],
            offgDataset = Xjs.util.TableUtils.getTable_$Panel_$String(this.mainTable,"ft_rpadj_offg").dataSet;
        initParams.bcode = this.mainDataSet.getValue("bcode");
        readonlyParams.push("bcode");
        initParams.corpbcode = this.mainDataSet.getValue("corpbcode");
        readonlyParams.push("corpbcode");
        initParams.ccode = this.mainDataSet.getValue("ccode");
        readonlyParams.push("ccode");
        initParams.fcyingflag = "Y";
        readonlyParams.push("fcyingflag");
        refreshParams.sheetcode = offgDataset.getValue("srcsheetcode");
        refreshParams.limitsheetcode = this.sheetcode;
        refreshParams.lrpflag = 2 + 4;
        refreshParams.ismatchorc = "Y";
        refreshParams.offgicode = offgDataset.getValue("offgicode");
        event.checkData = {initParams:initParams,readonlyParams:readonlyParams,refreshParams:refreshParams};
        return null;
    }
});
/*snsoft/ft/rpadj/rec/invoker/AdjRecOffBatchDataCopyInvoker.java*/
snsoft.ft.rpadj.rec.invoker.AdjRecOffBatchDataCopyInvoker=function(parameter){
    snsoft.ft.rpadj.rec.invoker.AdjRecOffBatchDataCopyInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.rpadj.rec.invoker.AdjRecOffBatchDataCopyInvoker,snsoft.ft.comm.cmdreg.SheetDataCopyInvoker,{
  _js$className_:"snsoft.ft.rpadj.rec.invoker.AdjRecOffBatchDataCopyInvoker",
    /*snsoft.ft.rpadj.rec.invoker.AdjRecOffBatchDataCopyInvoker.fireOKCreate*/
    fireOKCreate:function(e,d,copyparams)
    {
        var table = snsoft.ft.utils.FTUtils.getTableFromPanel(d),
            salshipcoders = this.getConfirmSalshipcoders(table);
        if(salshipcoders && salshipcoders.length > 0)
        {
            var msg = Xjs.ResBundle.getResVal("FT-RPADJ.10000035",salshipcoders.join("、")),
                confirmDlg = Xjs.ui.UIUtil.showConfirmDialog(null,msg,new Xjs.FuncCall(this.onConfirmOK,this,[e,d,copyparams]),null,0,null);
            confirmDlg.showModal();
            throw {dummy:true};
        } else 
        {
            snsoft.ft.rpadj.rec.invoker.AdjRecOffBatchDataCopyInvoker.superclass.fireOKCreate.call(this,e,d,copyparams);
        }
    },
    /*snsoft.ft.rpadj.rec.invoker.AdjRecOffBatchDataCopyInvoker.onConfirmOK*/
    onConfirmOK:function(dlg,cmd,e,d,copyparams)
    {
        if("ok" == cmd)
        {
            snsoft.ft.rpadj.rec.invoker.AdjRecOffBatchDataCopyInvoker.superclass.fireOKCreate.call(this,e,d,copyparams);
            d.closeModal();
        }
    },
    /*snsoft.ft.rpadj.rec.invoker.AdjRecOffBatchDataCopyInvoker.getConfirmSalshipcoders*/
    getConfirmSalshipcoders:function(table)
    {
        var selrow = table.getSelectedRowNumbers(table.rowMutiSelectable ? 0 : 1),
            salshipcoders = [];
        for(var i=0;i < selrow.length;i++)
        {
            var row = selrow[i],
                noselect = table.dataSet.getValue("noselect",row);
            if("Y"==noselect)
            {
                salshipcoders.push(table.dataSet.getValue("salshipcoder",row));
            }
        }
        return salshipcoders;
    }
});
/*snsoft/ft/rpadj/rec/invoker/AdjRecOffSingleDataCopyInvoker.java*/
snsoft.ft.rpadj.rec.invoker.AdjRecOffSingleDataCopyInvoker=function(parameter){
    snsoft.ft.rpadj.rec.invoker.AdjRecOffSingleDataCopyInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.rpadj.rec.invoker.AdjRecOffSingleDataCopyInvoker,snsoft.ft.comm.cmdreg.SheetDataCopyInvoker,{
  _js$className_:"snsoft.ft.rpadj.rec.invoker.AdjRecOffSingleDataCopyInvoker",
    /*snsoft.ft.rpadj.rec.invoker.AdjRecOffSingleDataCopyInvoker.resetOnShowing*/
    resetOnShowing:function(dlg,dlgtbl,sheetparams)
    {
        snsoft.ft.rpadj.rec.invoker.AdjRecOffSingleDataCopyInvoker.superclass.resetOnShowing.call(this,dlg,dlgtbl,sheetparams);
        var listener = dlgtbl.getListener(snsoft.ft.comm.busi.FieldExceedJSListener);
        if(listener)
        {
            listener.maxIncZero = true;
            listener.minIncZero = true;
        }
    }
});
/*snsoft/ft/rpadj/rec/invoker/AdvRecOffEditCcodeConfirmInvoker.java*/
snsoft.ft.rpadj.rec.invoker.AdvRecOffEditCcodeConfirmInvoker=function(parameter){
    snsoft.ft.rpadj.rec.invoker.AdvRecOffEditCcodeConfirmInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.rpadj.rec.invoker.AdvRecOffEditCcodeConfirmInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.rpadj.rec.invoker.AdvRecOffEditCcodeConfirmInvoker",
    /*snsoft.ft.rpadj.rec.invoker.AdvRecOffEditCcodeConfirmInvoker.check*/
    check:function(event)
    {
        return this.newDialogWithFunc(event,(e)=>{
            this.mainTable.openPopupEdit("editCcodeDlg");
            var editCcodeDlg = this.mainTable.getPopupEditDialog("editCcodeDlg");
            return editCcodeDlg;
        },(e,d)=>{
            var param = {};
            param.officode = this.mainDataSet.get("officode");
            param.bcode = d.getItemByName("bcode").getValue();
            param.ccode = d.getItemByName("ccode").getValue();
            param.corpbcode = d.getItemByName("corpbcode").getValue();
            param.reprpflag = d.getItemByName("reprpflag").getValue();
            event.checkData.param = param;
        });
    },
    /*snsoft.ft.rpadj.rec.invoker.AdvRecOffEditCcodeConfirmInvoker.invoke*/
    invoke:function(event)
    {
        return event.checkData.param;
    },
    /*snsoft.ft.rpadj.rec.invoker.AdvRecOffEditCcodeConfirmInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        this.mainTable.refreshTable();
    }
});
/*snsoft/ft/rpadj/rec/invoker/AdvRecOffEditCcodeInvoker.java*/
snsoft.ft.rpadj.rec.invoker.AdvRecOffEditCcodeInvoker=function(parameter){
    snsoft.ft.rpadj.rec.invoker.AdvRecOffEditCcodeInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.rpadj.rec.invoker.AdvRecOffEditCcodeInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.rpadj.rec.invoker.AdvRecOffEditCcodeInvoker",
    /*snsoft.ft.rpadj.rec.invoker.AdvRecOffEditCcodeInvoker.check*/
    check:function(event)
    {
        var gTable = Xjs.util.TableUtils.getTable_$Panel_$String(this.mainTable,"ft_rpadj_offg"),
            gsTable = Xjs.util.TableUtils.getTable_$Panel_$String(this.mainTable,"ft_rpadj_offgs"),
            gTableSize = gTable.getDataSet().getRows().length,
            gsTableSize = gsTable.getDataSet().getRows().length;
        if(gTableSize != 0 || gsTableSize != 0)
        {
            return {type:0,title:this.getTitle(),prompt:this.getResVal("FT-RPADJ.10000017")};
        }
        return null;
    }
});
/*snsoft/ft/rpadj/rec/lis/AdvRecOffgsJSListener.java*/
Xjs.namespace("snsoft.ft.rpadj.rec.lis");
snsoft.ft.rpadj.rec.lis.AdvRecOffgsJSListener=function(params){
    snsoft.ft.rpadj.rec.lis.AdvRecOffgsJSListener.superclass.constructor.call(this,params);
};
Xjs.extend(snsoft.ft.rpadj.rec.lis.AdvRecOffgsJSListener,snsoft.plat.bas.busi.SystemFunctionListener,{
  _js$className_:"snsoft.ft.rpadj.rec.lis.AdvRecOffgsJSListener",
    /*snsoft.ft.rpadj.rec.lis.AdvRecOffgsJSListener.initComponent*/
    initComponent:function(table,values)
    {
        snsoft.ft.rpadj.rec.lis.AdvRecOffgsJSListener.superclass.initComponent.call(this,table,values);
        if(this.gstable == null)
        {
            this.gstable = table;
            this.mainTable = table.getRootTable();
            this.gtable = Xjs.util.TableUtils.getTable_$Panel_$String(this.mainTable,"ft_rpadj_offg");
        }
    },
    /*snsoft.ft.rpadj.rec.lis.AdvRecOffgsJSListener.dataSetFieldPosted*/
    dataSetFieldPosted:function(dataSet,event)
    {
        snsoft.ft.rpadj.rec.lis.AdvRecOffgsJSListener.superclass.dataSetFieldPosted.call(this,dataSet,event);
        if(event.columnName == "fcy")
        {
            var gfcode = this.gtable.getDataSet().getValue("fcode"),
                gsfcode = dataSet.getValue("fcode");
            if(gfcode == gsfcode)
            {
                dataSet.setValue("rpfcy",dataSet.getValue("fcy"));
            }
        }
        if(event.columnName == "rpfcy" || event.columnName == "fcy")
        {
            var fzeratescale = this.gstable.getColumn("fzerate").maxDecimals,
                payfcy = dataSet.getValue("rpfcy"),
                fcy = dataSet.getValue("fcy");
            dataSet.setValue("fzerate",snsoft.ft.utils.FTUtils.round(snsoft.ft.utils.FTUtils.div(payfcy,fcy),fzeratescale));
        }
    },
    /*snsoft.ft.rpadj.rec.lis.AdvRecOffgsJSListener.dataSetRowNavigated*/
    dataSetRowNavigated:function(dataSet,e)
    {
        snsoft.ft.rpadj.rec.lis.AdvRecOffgsJSListener.superclass.dataSetRowNavigated.call(this,dataSet,e);
        var gfcode = this.gtable.getDataSet().getValue("fcode"),
            gsfcode = dataSet.getValue("fcode"),
            isReadonly = gfcode == gsfcode;
        this.gstable.getColumn("rpfcy").setReadonly(isReadonly);
        this.gstable.render(2);
    }
});
