Xjs.loadedXjs.push("snsoft/ft/fund/rpadj/advoff");
Xjs.namespace("snsoft.ft.rpadj.advoff.lis");snsoft.ft.rpadj.advoff.lis.LrpAdvOffJSListener=function(parameter){Xjs.apply(this,parameter);};Xjs.extend(snsoft.ft.rpadj.advoff.lis.LrpAdvOffJSListener,Xjs.ui.DefaultDialogPaneListener,{itemValueChanged:function(dialog,e){snsoft.ft.rpadj.advoff.lis.LrpAdvOffJSListener.superclass.itemValueChanged.call(this,dialog,e);if("advofftype"==e.item.name)this.doCtrl(dialog,e);},doCtrl:function(dialog,e){var advofftype=dialog.getItemValue(e.item.name),invcoderComp=dialog.getItemByName(snsoft.ft.rpadj.advoff.lis.LrpAdvOffJSListener.invcoderCol),pacodeComp=dialog.getItemByName(snsoft.ft.rpadj.advoff.lis.LrpAdvOffJSListener.pacodeCol),isprjadvoffComp=dialog.getItemByName(snsoft.ft.rpadj.advoff.lis.LrpAdvOffJSListener.isprjadvoffCol),isordadvoffComp=dialog.getItemByName(snsoft.ft.rpadj.advoff.lis.LrpAdvOffJSListener.isordadvoffCol),islogitoadvoffComp=dialog.getItemByName(snsoft.ft.rpadj.advoff.lis.LrpAdvOffJSListener.islogitoadvoffCol),isssordvoffComp=dialog.getItemByName(snsoft.ft.rpadj.advoff.lis.LrpAdvOffJSListener.isssordvoffCol);if(advofftype==snsoft.ft.rpadj.advoff.lis.LrpAdvOffJSListener.AdvOffType_30){invcoderComp.setReadonly(true);dialog.setItemValue(snsoft.ft.rpadj.advoff.lis.LrpAdvOffJSListener.invcoderCol,null);pacodeComp.setReadonly(true);dialog.setItemValue(snsoft.ft.rpadj.advoff.lis.LrpAdvOffJSListener.pacodeCol,null);}else {invcoderComp.setReadonly(false);pacodeComp.setReadonly(false);}if(advofftype==snsoft.ft.rpadj.advoff.lis.LrpAdvOffJSListener.AdvOffType_10){isprjadvoffComp.setReadonly(true);dialog.setItemValue(snsoft.ft.rpadj.advoff.lis.LrpAdvOffJSListener.isprjadvoffCol,"Y");isordadvoffComp.setReadonly(true);dialog.setItemValue(snsoft.ft.rpadj.advoff.lis.LrpAdvOffJSListener.isordadvoffCol,"Y");islogitoadvoffComp.setReadonly(true);dialog.setItemValue(snsoft.ft.rpadj.advoff.lis.LrpAdvOffJSListener.islogitoadvoffCol,"N");isssordvoffComp.setReadonly(true);dialog.setItemValue(snsoft.ft.rpadj.advoff.lis.LrpAdvOffJSListener.isssordvoffCol,null);isssordvoffComp.nonBlank=false;}else if(advofftype==snsoft.ft.rpadj.advoff.lis.LrpAdvOffJSListener.AdvOffType_30){isprjadvoffComp.setReadonly(false);dialog.setItemValue(snsoft.ft.rpadj.advoff.lis.LrpAdvOffJSListener.isprjadvoffCol,"Y");isordadvoffComp.setReadonly(true);dialog.setItemValue(snsoft.ft.rpadj.advoff.lis.LrpAdvOffJSListener.isordadvoffCol,"Y");islogitoadvoffComp.setReadonly(true);dialog.setItemValue(snsoft.ft.rpadj.advoff.lis.LrpAdvOffJSListener.islogitoadvoffCol,"N");isssordvoffComp.setReadonly(false);dialog.setItemValue(snsoft.ft.rpadj.advoff.lis.LrpAdvOffJSListener.isssordvoffCol,"Y");isssordvoffComp.nonBlank=true;}else {isprjadvoffComp.setReadonly(false);dialog.setItemValue(snsoft.ft.rpadj.advoff.lis.LrpAdvOffJSListener.isprjadvoffCol,null);isordadvoffComp.setReadonly(false);dialog.setItemValue(snsoft.ft.rpadj.advoff.lis.LrpAdvOffJSListener.isordadvoffCol,null);islogitoadvoffComp.setReadonly(false);dialog.setItemValue(snsoft.ft.rpadj.advoff.lis.LrpAdvOffJSListener.islogitoadvoffCol,null);isssordvoffComp.setReadonly(true);dialog.setItemValue(snsoft.ft.rpadj.advoff.lis.LrpAdvOffJSListener.isssordvoffCol,null);isssordvoffComp.nonBlank=false;}isssordvoffComp.setLabelClassByRdonlyAndNonblank();}});Xjs.apply(snsoft.ft.rpadj.advoff.lis.LrpAdvOffJSListener,{AdvOffType_10:"10",AdvOffType_20:"20",AdvOffType_30:"30",invcoderCol:"invcoder",pacodeCol:"pacode",isprjadvoffCol:"isprjadvoff",isordadvoffCol:"isordadvoff",islogitoadvoffCol:"islogitoadvoff",isssordvoffCol:"isssordvoff"});