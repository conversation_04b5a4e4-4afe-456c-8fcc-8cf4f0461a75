Xjs.loadedXjs.push("snsoft/ft/fund/forex/exrae");
Xjs.namespace("snsoft.ft.forex.exrae.invoker");snsoft.ft.forex.exrae.invoker.ForexRevCreateRedVouInvoker=function(parameter){snsoft.ft.forex.exrae.invoker.ForexRevCreateRedVouInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.forex.exrae.invoker.ForexRevCreateRedVouInvoker,snsoft.plat.bas.sheet.cmd.red.CommandRed,{beforeCheck:function(event){var rows=this.mainTable.getSelectedRowNumbers();if(rows==null||rows.length==0){throw Xjs.ResBundle.getResVal("FT.00000048");}var gTable=Xjs.util.TableUtils.getTable_$Panel_$String(this.mainTable,"ft_forex_exraeg"),gDataSet=gTable.dataSet;for(var i=0;i<gDataSet.getRowCount();i++){var vidflag=gDataSet.getValue("vidflag",rows[i]);if(vidflag!=1){throw Xjs.ResBundle.getResVal("SNA-ACC.00000096");}}return null;},check:function(event){return this.newDialogWithFunc(event,(e)=>{var dlg=Xjs.ui.UIUtil.loadDialog("FT-FOREX.ForexRevWorkBenchRedVDlg",0,null,{width:325,height:120},null,null);dlg.title=this.cfg.title;dlg.showModal();return dlg;},(e,d)=>{var t=d.getItemByName("forexExraeRedVDlg");t.postPending();t.checkCurRowDataNonBlank(t.dataSet);event.checkData.tcaptime=t.dataSet.getValue("tcaptime");event.checkData.showdlg=d;});},invoke:function(event){var p=snsoft.ft.forex.exrae.invoker.ForexRevCreateRedVouInvoker.superclass.invoke.call(this,event),rows=this.mainTable.getSelectedRowNumbers(),exraeicode=Xjs.util.DataSetUtils.getColumnStrValues_DataSet_int(this.mainDataSet,"exraeicode",rows),sheetcode=Xjs.util.DataSetUtils.getColumnStrValues_DataSet_int(this.mainDataSet,"sheetcode",rows);p.exraeicode=exraeicode;p.sheetcode=sheetcode;p.tcaptime=event.checkData.tcaptime;return p;}});snsoft.ft.forex.exrae.invoker.ForexRevCreateVfldInvoker=function(parameter){snsoft.ft.forex.exrae.invoker.ForexRevCreateVfldInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.forex.exrae.invoker.ForexRevCreateVfldInvoker,snsoft.ext.cmd.com.biz.SaveInvoker,{beforeCheck:function(event){var sels=this.table.getSelectedRowNumbers();if(sels.length==0){throw new Error(Xjs.ResBundle.getResVal("FT.00000048"));}return null;},invoke:function(event){var invokeParam=this.buildInvokeParam(null);invokeParam.mains.push(Xjs.util.DataSetUtils.getValuesTo(this.dataSet,0,null));return invokeParam;}});snsoft.ft.forex.exrae.invoker.ForexRevWorkBenchCancelInvoker=function(parameter){snsoft.ft.forex.exrae.invoker.ForexRevWorkBenchCancelInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.forex.exrae.invoker.ForexRevWorkBenchCancelInvoker,snsoft.ft.comm.cmdreg.CmdCommInvoker,{beforeCheck:function(event){var rows=this.table.getSelectedRowNumbers();if(rows.length==0){throw Xjs.ResBundle.getResVal("FT.00000048");}var srctype=this.dataSet.getValue("srctype",rows[0]);if(srctype=="10"){throw Xjs.ResBundle.getResVal("FT-FOREX.00000038");}var redflag=this.dataSet.getValue("redflag",rows[0]);if(redflag!=0){throw Xjs.ResBundle.getResVal("FT-FOREX.00000039");}return null;},check:function(event){return {type:0,title:this.getTitle(),prompt:this.getResVal("FT-FOREX.10000004")};},invoke:function(event){var invokeParam=this.buildInvokeParam(null);invokeParam.exraeicode=this.table.getValue("exraeicode");invokeParam.exraegicode=this.table.getValue("exraegicode");invokeParam.vidflag=this.table.getValue("vidflag");invokeParam.vid=this.table.getValue("vid");return invokeParam;},afterInvoke:function(event){var rowAt=this.dataSet.getRow();this.table.refreshTable();this.table.gotoRow(rowAt);}});snsoft.ft.forex.exrae.invoker.ForexRevWorkBenchRelaInvoker=function(params){snsoft.ft.forex.exrae.invoker.ForexRevWorkBenchRelaInvoker.superclass.constructor.call(this,params);};Xjs.extend(snsoft.ft.forex.exrae.invoker.ForexRevWorkBenchRelaInvoker,snsoft.ft.comm.cmdreg.CmdCommInvoker,{beforeCheck:function(event){var srctype=this.dataSet.getValue("srctype",0);if(srctype=="10"){throw Xjs.ResBundle.getResVal("FT-FOREX.00000037");}return null;}});Xjs.namespace("snsoft.ft.forex.exrae.lis");snsoft.ft.forex.exrae.lis.ForexRevWorkBenchJSListener=function(params){snsoft.ft.forex.exrae.lis.ForexRevWorkBenchJSListener.superclass.constructor.call(this,params);};Xjs.extend(snsoft.ft.forex.exrae.lis.ForexRevWorkBenchJSListener,snsoft.plat.bas.busi.SystemFunctionListener,{});