## 单据类型
title_FT-FOREX.ForexExpObj=外汇敞口对象
title_FT-FOREX.ForexExpHedgeApp=外汇敞口对冲申请
title_FT-FOREX.ForexExcRelCas=外汇交易释放重新关联申请
title_FT-FOREX.ForexExcApp=外汇交易申请单
title_FT-FOREX.ForexMoveApp=外汇移仓申请
title_FT-FOREX.ForexExtendApp=外汇展期申请
title_FT-FOREX.ForexCloseApp=外汇平仓申请
title_FT-FOREX.ForexDelyApp=外汇交割申请
title_FT-FOREX.ForexBval=银行估值表
title_FT-FOREX.ForexSpotExcSettApp=即期结汇申请单
title_FT-FOREX.ForexBvalVMark=银行估值表凭证
title_FT-FOREX.ForexExraeVMark=外汇交易复核入账凭证

## 入口
title_FT-FOREX.ForexExpObjEntry=外汇敞口对象-入口
title_FT-FOREX.ForexExpHedgeAppEntry=外汇敞口对冲申请-入口
title_FT-FOREX.ForexExcRelCasEntry=外汇交易释放重新关联申请-入口
title_FT-FOREX.ForexMoveAppEntry=外汇移仓申请-入口
title_FT-FOREX.ForexExtendAppEntry=外汇展期申请-入口
title_FT-FOREX.ForexExcAppEntry=外汇交易申请单-入口
title_FT-FOREX.ForexCloseAppEntry=外汇平仓申请单-入口
title_FT-FOREX.ForexSpotExcSettAppEntry=即期结汇申请单-入口
title_FT-FOREX.ForexDelyAppEntry=外汇交割申请单-入口
title_FT-FOREX.ForexBvalEntry=银行估值表

## 详情
title_FT-FOREX.ForexExpObjDetail=外汇敞口对象
title_FT-FOREX.ForexExpHedgeAppDetail=外汇敞口对冲申请
title_FT-FOREX.ForexExcAppDetail=外汇交易申请单
title_FT-FOREX.ForexMoveAppDetail=外汇移仓申请单
title_FT-FOREX.ForexExtendAppDetail=外汇展期申请单
title_FT-FOREX.ForexCloseAppDetail=外汇平仓申请单
title_FT-FOREX.ForexSpotExcSettAppDetail=即期结汇申请单
title_FT-FOREX.ForexDelyAppDetail=外汇交割申请单

## 工作台
title_FT-FOREX.ForexExpObjWorkBench=待外汇交易工作台
title_FT-FOREX.ForexExcWorkbench=外汇成交待处理工作台
title_FT-FOREX.ForexExcRelCasDetail=外汇交易释放重新关联申请单
title_FT-FOREX.ForexSpotExcSettAppWorkBench=待结汇工作台
title_FT-FOREX.ForexRevWorkBench=外汇交易复核入账工作台

## group 分组
FT.title.grp.ForexBvalInfo=银行估值信息
FT.title.grp.ForexBvalDetail=分摊明细

## 标签页
title.F.tab.objSetts=收付款方式
title.F.tab.objTends=意向编号
title.F.tab.exphdag=外汇敞口信息
title.F.tab.exphdagsal=销售外汇敞口信息
title.F.tab.exphdagpur=采购外汇敞口信息
title.F.tab.exphdags=匹配关系
title.F.tab.exphdagssal=销售敞口信息
title.F.tab.exphdagspur=采购敞口信息
FT.title.grp.ForexExag=外汇敞口明细
FT.title.grp.ForexExat=成交信息
FT.title.grp.ForexExats=成交明细
FT.title.grp.ForexTransInfo=交易申请信息
FT.title.grp.ForexTransPend=挂单
FT.title.grp.ForexSpotExcSettAppg=结汇资金明细

FT.title.grp.ForexExcRelCasBasInf=${FT.title.grp.bas}
FT.title.grp.releaseInfo=释放信息
FT.title.grp.reassociationInfo=重新关联信息
FT.title.grp.MoveWarehouseInf=移仓信息
FT.title.grp.ClosingPositionInf=平仓明细
FT.title.grp.ExtendAppDetailInfo=展期明细
FT.title.grp.ForeignExchangeTransInf=外汇交易明细
FT.title.grp.ForexDeliveryInfo=交割明细
FT.title.grp.ForexEcapInfo=业务信息
FT.title.grp.ForexTransDura=交易存续期
FT.title.grp.ForexEca=交割
FT.title.grp.ForexEcaExt=展期
FT.title.grp.ForexEcaPos=平仓
FT.title.grp.ForexExraeInfo=业务信息

## 按钮
FT.cmd_addSalExphdag=追加销售外汇敞口
FT.cmd_addPurExphdag=追加采购外汇敞口
FT.cmd_matchrelation=生成匹配关系
FT.cmd_createForexExcApp=生成外汇交易申请单
FT.cmd_createForexSpotExcSettApp=生成即期结汇申请单
FT.cmd_addSesag=追加待结汇数据
FT.cmd_addSesags=追加外汇敞口
FT.cmd_generateExchange=生成模拟结汇申请
FT.cmd_openExchange=打开模拟结汇申请

FT.cmd_createForexExrc=生成外汇交易释放重新关联申请
FT.cmd_addTransactionInfo=追加成交信息
FT.cmd_addForexExpInfo=追加外汇敞口
FT.cmd_pushFundBatch=批量推送资金
FT.cmd_addClosingPositionInfo=创建平仓申请
FT.cmd_addForeignExchangeTransInf=创建外汇交易申请
FT.cmd_addSpotExchange=即期结售汇
FT.cmd_addForwardForeignExchange=远期结售汇
FT.cmd_bulkPushFunds=批量推送资金
FT.cmd_genForexExtend=生成外汇展期申请
FT.cmd_addForexEcagInfo=追加成交信息
FT.cmd_addForexEcapInfo=追加待结汇数据
FT.cmd_getBankValuation=获取银行估值
FT.cmd_related=关联
FT.cmd_cancelRelated=取消关联
