package snsoft.ft.acs.fund.rev.docu.dlyd.service.impl;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Git分支差异分析器
 * 用于对比origin/phase2和origin/master分支中特定作者的提交差异
 */
public class GitBranchDiffAnalyzer {
    
    private static final String DEFAULT_AUTHOR = "池启苹";
    private static final String BRANCH1 = "origin/phase2";
    private static final String BRANCH2 = "origin/master";

    public static void main(String[] args) {
        GitBranchDiffAnalyzer analyzer = new GitBranchDiffAnalyzer();
        try {
            if (args.length > 0 && args[0].equals("--list-authors")) {
                analyzer.listAllAuthors();
                return;
            }

            String targetAuthor = args.length > 0 ? args[0] : DEFAULT_AUTHOR;
            analyzer.analyzeBranchDifferences(targetAuthor);
        } catch (Exception e) {
            System.err.println("分析过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 列出所有作者
     */
    public void listAllAuthors() throws IOException, InterruptedException {
        System.out.println("正在获取所有作者列表...");

        String[] command = {"git", "log", "--all", "--pretty=format:%an", "--no-merges"};
        Process process = new ProcessBuilder(command).start();

        Set<String> authors = new TreeSet<>();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream(), "UTF-8"))) {
            String line;
            while ((line = reader.readLine()) != null) {
                if (!line.trim().isEmpty()) {
                    authors.add(line.trim());
                }
            }
        }
        process.waitFor();

        System.out.println("找到以下作者 (" + authors.size() + " 个):");
        System.out.println(repeatString("=", 50));
        for (String author : authors) {
            System.out.println(author);
        }
        System.out.println(repeatString("=", 50));
        System.out.println("使用方法: java GitBranchDiffAnalyzer \"作者名\"");
    }

    /**
     * 分析分支差异的主方法
     */
    public void analyzeBranchDifferences(String targetAuthor) throws IOException, InterruptedException {
        String outputFile = "author_diff_report_" + targetAuthor.replaceAll("[^\\w\\u4e00-\\u9fa5]", "_") + "_" +
            new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date()) + ".txt";

        System.out.println("开始分析 " + BRANCH1 + " 和 " + BRANCH2 + " 分支中作者 '" + targetAuthor + "' 的提交差异...");

        // 1. 获取两个分支中目标作者的所有提交
        Set<String> phase2Commits = getAuthorCommits(BRANCH1, targetAuthor);
        Set<String> masterCommits = getAuthorCommits(BRANCH2, targetAuthor);

        System.out.println("在 " + BRANCH1 + " 中找到 " + phase2Commits.size() + " 个提交");
        System.out.println("在 " + BRANCH2 + " 中找到 " + masterCommits.size() + " 个提交");

        // 2. 找出差异提交
        Set<String> onlyInPhase2 = new HashSet<>(phase2Commits);
        onlyInPhase2.removeAll(masterCommits);

        Set<String> onlyInMaster = new HashSet<>(masterCommits);
        onlyInMaster.removeAll(phase2Commits);

        // 3. 生成详细报告
        generateDetailedReport(onlyInPhase2, onlyInMaster, targetAuthor, outputFile);

        System.out.println("分析完成！报告已保存到: " + outputFile);
    }
    
    /**
     * 获取指定分支中目标作者的所有提交哈希
     */
    private Set<String> getAuthorCommits(String branch, String author) throws IOException, InterruptedException {
        Set<String> commits = new HashSet<>();

        // 使用git log获取指定作者的提交
        String[] command = {
            "git", "log", branch,
            "--author=" + author,
            "--pretty=format:%H",
            "--no-merges"
        };

        Process process = new ProcessBuilder(command).start();

        try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream(), "UTF-8"))) {
            String line;
            while ((line = reader.readLine()) != null) {
                if (!line.trim().isEmpty()) {
                    commits.add(line.trim());
                }
            }
        }

        process.waitFor();
        return commits;
    }
    
    /**
     * 生成详细的差异报告
     */
    private void generateDetailedReport(Set<String> onlyInPhase2, Set<String> onlyInMaster, String targetAuthor, String outputFile)
            throws IOException, InterruptedException {

        try (PrintWriter writer = new PrintWriter(new OutputStreamWriter(new FileOutputStream(outputFile), StandardCharsets.UTF_8))) {
            writer.println(repeatString("=", 80));
            writer.println("Git分支差异分析报告");
            writer.println("作者: " + targetAuthor);
            writer.println("分支对比: " + BRANCH1 + " vs " + BRANCH2);
            writer.println("生成时间: " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
            writer.println(repeatString("=", 80));
            writer.println();
            
            // 报告仅在phase2分支中的提交
            if (!onlyInPhase2.isEmpty()) {
                writer.println("仅在 " + BRANCH1 + " 分支中的提交 (" + onlyInPhase2.size() + " 个):");
                writer.println(repeatString("-", 60));
                for (String commit : onlyInPhase2) {
                    writeCommitDetails(writer, commit, BRANCH1);
                    writer.println();
                }
            }

            // 报告仅在master分支中的提交
            if (!onlyInMaster.isEmpty()) {
                writer.println("仅在 " + BRANCH2 + " 分支中的提交 (" + onlyInMaster.size() + " 个):");
                writer.println(repeatString("-", 60));
                for (String commit : onlyInMaster) {
                    writeCommitDetails(writer, commit, BRANCH2);
                    writer.println();
                }
            }
            
            // 如果没有差异
            if (onlyInPhase2.isEmpty() && onlyInMaster.isEmpty()) {
                writer.println("未发现作者 '" + targetAuthor + "' 在两个分支间的提交差异。");
            }
            
            writer.println(repeatString("=", 80));
            writer.println("报告结束");
        }
    }

    /**
     * 重复字符串的辅助方法（兼容老版本Java）
     */
    private static String repeatString(String str, int count) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }
    
    /**
     * 写入提交的详细信息
     */
    private void writeCommitDetails(PrintWriter writer, String commitHash, String branch) 
            throws IOException, InterruptedException {
        
        // 获取提交的基本信息
        String[] infoCommand = {
            "git", "show", "--no-patch", "--pretty=format:%H%n%an%n%ae%n%ad%n%s", 
            "--date=format:%Y-%m-%d %H:%M:%S", commitHash
        };
        
        Process infoProcess = new ProcessBuilder(infoCommand).start();
        List<String> infoLines = new ArrayList<>();
        
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(infoProcess.getInputStream(), "UTF-8"))) {
            String line;
            while ((line = reader.readLine()) != null) {
                infoLines.add(line);
            }
        }
        infoProcess.waitFor();
        
        if (infoLines.size() >= 5) {
            writer.println("提交哈希: " + infoLines.get(0));
            writer.println("作者: " + infoLines.get(1));
            writer.println("邮箱: " + infoLines.get(2));
            writer.println("提交时间: " + infoLines.get(3));
            writer.println("提交信息: " + infoLines.get(4));
        }
        
        // 获取修改的文件列表
        String[] filesCommand = {
            "git", "show", "--name-status", "--pretty=format:", commitHash
        };
        
        Process filesProcess = new ProcessBuilder(filesCommand).start();
        writer.println("修改的文件:");
        
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(filesProcess.getInputStream(), "UTF-8"))) {
            String line;
            while ((line = reader.readLine()) != null) {
                if (!line.trim().isEmpty()) {
                    writer.println("  " + line);
                }
            }
        }
        filesProcess.waitFor();
        
        // 获取代码差异（限制输出长度）
        String[] diffCommand = {
            "git", "show", "--pretty=format:", commitHash
        };
        
        Process diffProcess = new ProcessBuilder(diffCommand).start();
        writer.println("代码差异:");
        
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(diffProcess.getInputStream(), "UTF-8"))) {
            String line;
            int lineCount = 0;
            final int MAX_DIFF_LINES = 100; // 限制差异输出行数
            
            while ((line = reader.readLine()) != null && lineCount < MAX_DIFF_LINES) {
                if (!line.trim().isEmpty()) {
                    writer.println("  " + line);
                    lineCount++;
                }
            }
            
            if (lineCount >= MAX_DIFF_LINES) {
                writer.println("  ... (差异内容过长，已截断)");
            }
        }
        diffProcess.waitFor();
    }
}
