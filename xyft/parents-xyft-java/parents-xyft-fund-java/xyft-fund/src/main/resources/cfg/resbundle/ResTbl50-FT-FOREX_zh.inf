#【外汇敞口对象】
ft_forex_expo=外汇敞口对象
ft_forex_expo.expoicode=外汇敞口对象内码
ft_forex_expo.expocode=外汇敞口编号
ft_forex_expo.expoicodev=外汇敞口对象原版本内码
ft_forex_expo.srcvsntype=来源单据版本修改类型
ft_forex_expo.curratifydate=生效时间
ft_forex_expo.purordicode=采购合同内码
ft_forex_expo.salordicode=销售合同内码
ft_forex_expo.intordcode=意向编号
ft_forex_expo.intordicode=意向编号内码
ft_forex_expo.intordsheetcode=意向单据类型
ft_forex_expo.divedsheetcode=被拆分单据类型
ft_forex_expo.divedcode=被拆分单据号
ft_forex_expo.divedicode=被拆分单据内码
ft_forex_expo.expdate=敞口日期
ft_forex_expo.isocbpl=是否我司承担损益
ft_forex_expo.busiprop=业务性质
ft_forex_expo.ordprop=合同性质
ft_forex_expo.extype=变动类型
ft_forex_expo.forexinttype=外汇意向类型
ft_forex_expo.ccrate=成本测算汇率

#【外汇敞口对象】〖收付款方式〗
ft_forex_expost=收付款方式
ft_forex_expost.exposticode=外汇敞口对象收付款方式内码
ft_forex_expost.expoicode=外汇敞口对象内码
ft_forex_expost.exposticodev=外汇敞口对象收付款方式原版本内码
ft_forex_expost.expoicodev=外汇敞口对象原版本内码
ft_forex_expost.divedgicode=被拆分单据子表内码
ft_forex_expost.paymode=收付款方式
ft_forex_expost.potablefcy=可转嫁金额
ft_forex_expost.initfcy=初始原币金额
ft_forex_expost.rdate=预计收付款日期
ft_forex_expost.prjcode=业务编号
ft_forex_expost.prjicode=业务内码

#【外汇敞口对象】〖意向编号〗
ft_forex_expotd=意向编号
ft_forex_expotd.expotdicode=外汇敞口对象意向编号内码
ft_forex_expotd.expoicode=外汇敞口对象内码
ft_forex_expotd.expotdicodev=外汇敞口对象意向编号原版本内码
ft_forex_expotd.expoicodev=外汇敞口对象原版本内码
ft_forex_expotd.intordsheetcode=意向单据类型
ft_forex_expotd.intordcode=意向编号
ft_forex_expotd.intordicode=意向编号内码

#【待外汇交易工作台】
ft_forex_expost_cb=待外汇交易工作台
ft_forex_expost_cb.salordicode=销售合同内码
ft_forex_expost_cb.purordicode=采购合同内码
ft_forex_expost_cb.intordcode=意向编号
ft_forex_expost_cb.intordicode=意向编号内码
ft_forex_expost_cb.prjcode=业务编号
ft_forex_expost_cb.prjicode=业务内码
ft_forex_expost_cb.paymode=收付款方式
ft_forex_expost_cb.fcy=计划执行金额
ft_forex_expost_cb.fcyed=已执行金额
ft_forex_expost_cb.fcying=待执行金额
ft_forex_expost_cb.fcying=待执行金额
ft_forex_expost_cb.isocbpl=是否我司承担损益
ft_forex_expost_cb.ordprop=合同性质
ft_forex_expost_cb.exposticode=外汇敞口对象收付款方式内码
ft_forex_expost_cb.rdate=预计收付款日期
ft_forex_expost_cb.ccrate=成本测算汇率
ft_forex_expost_cb.busiprop=业务性质
ft_forex_expost_cb.corflag=核销标识
ft_forex_expost_cb.expoicode=外汇敞口对象内码
ft_forex_expost_cb.rdatefm=预计收付款日期从
ft_forex_expost_cb.fcyingflag=只查待执行金额>0
ft_forex_expost_cb.predatefm=创建日期从

#【外汇敞口对冲申请】
ft_forex_exphda=外汇敞口对冲申请
ft_forex_exphda.exphdaicode=对冲申请单内码
ft_forex_exphda.exphdacode=对冲申请单号
ft_forex_exphda.adate=申请日期
ft_forex_exphda.fcode=对冲币种
ft_forex_exphda.fcy=对冲金额

#【外汇敞口对冲申请】〖外汇敞口信息〗
ft_forex_exphdag=外汇敞口信息
ft_forex_exphdag.exphdagicode=外汇敞口信息内码
ft_forex_exphdag.exphdaicode=对冲申请单内码
ft_forex_exphdag.exposticode=外汇敞口对象收付款方式内码
ft_forex_exphdag.ordprop=合同性质
ft_forex_exphdag.pflag=匹配标识
ft_forex_exphdag.purordicode=采购合同内码
ft_forex_exphdag.salordicode=销售合同内码
ft_forex_exphdag.purprjicode=采购业务内码
ft_forex_exphdag.purprjcode=采购业务编号
ft_forex_exphdag.salprjicode=销售业务内码
ft_forex_exphdag.salprjcode=销售业务编号
ft_forex_exphdag.paymode=收付款方式
ft_forex_exphdag.paymode1=收款方式
ft_forex_exphdag.paymode2=付款方式
ft_forex_exphdag.fcying=待执行金额
ft_forex_exphdag.fcy=对冲金额
ft_forex_exphdag.rdate=预计收付款日期
ft_forex_exphdag.rdate1=预计收款日期
ft_forex_exphdag.rdate2=预计付款日期
ft_forex_exphdag.isocbpl=是否我司承担损益

#【外汇敞口对冲申请】〖匹配关系〗
ft_forex_exphdags=匹配关系
ft_forex_exphdags.exphdagsicode=匹配关系内码
ft_forex_exphdags.exphdagicode=外汇敞口信息内码
ft_forex_exphdags.exphdaicode=对冲申请单内码
ft_forex_exphdags.exphdaicode=对冲申请单内码
ft_forex_exphdags.purordicode=采购合同内码
ft_forex_exphdags.purprjicode=采购业务内码
ft_forex_exphdags.purprjcode=采购业务编号
ft_forex_exphdags.paymode=付款方式
ft_forex_exphdags.fcy=对冲金额
ft_forex_exphdags.rdate=预计付款日期

#【外汇交易申请单】
ft_forex_exa=外汇交易申请单
ft_forex_exa.exaicode=交易申请内码
ft_forex_exa.exacode=交易申请单号
ft_forex_exa.deptbcode=事业部
ft_forex_exa.fxprodtype=产品类型
ft_forex_exa.fcode=交易币种
ft_forex_exa.fxbusisrctype=来源方式
ft_forex_exa.isassignbank=是否指定银行
ft_forex_exa.abankcode=申请银行
ft_forex_exa.optccode=操作平台
ft_forex_exa.exdate=申请交易日期
ft_forex_exa.exdatefm=申请交易日期从
ft_forex_exa.issimulate=是否模拟
ft_forex_exa.optcorpbcode=操作公司
ft_forex_exa.isneedpfund=是否需要推资金
ft_forex_exa.isautopfund=是否自动推资金
ft_forex_exa.buyfcode=买入币种
ft_forex_exa.salefcode=卖出币种
ft_forex_exa.fcy=交易金额
ft_forex_exa.lockratefm=目标锁定汇率从
ft_forex_exa.lockrateto=目标锁定汇率到
ft_forex_exa.exduedate=交易到期日
ft_forex_exa.exduedatefm=交易到期日从
ft_forex_exa.exduedateto=交易到期日到
ft_forex_exa.ispo=是否挂单
ft_forex_exa.polockrate=目标挂单锁定汇率
ft_forex_exa.istrans=直至成交
ft_forex_exa.poduedate=挂单截止日期
ft_forex_exa.opocdc=买入/卖出
ft_forex_exa.duedate=期权到期日
ft_forex_exa.opendc=开仓方向
ft_forex_exa.openlot=开仓手数
ft_forex_exa.futures=期货合约号
ft_forex_exa.nearbuyfcode=近端买入币种
ft_forex_exa.nearsalefcode=近端卖出币种
ft_forex_exa.nearlockratefm=近端目标锁定汇率从
ft_forex_exa.nearlockrateto=近端目标锁定汇率到
ft_forex_exa.nearexduedate=近端交易到期日
ft_forex_exa.farbuyfcode=远端买入币种
ft_forex_exa.farsalefcode=远端卖出币种
ft_forex_exa.farlockratefm=远端目标锁定汇率从
ft_forex_exa.farlockrateto=远端目标锁定汇率到
ft_forex_exa.farexduedate=远端交易到期日
ft_forex_exa.nearpolockrate=近端目标挂单锁定汇率
ft_forex_exa.farpolockrate=远端目标挂单锁定汇率
ft_forex_exa.sppoint=掉期点

# 【外汇交易申请单】〖外汇敞口明细〗
ft_forex_exag.exagicode=外汇交易申请单外汇敞口明细内码
ft_forex_exag.exaicode=交易申请内码
ft_forex_exag.exagicoder=外汇交易申请单外汇敞口明细红冲核销内码
ft_forex_exag.exrcicode=外汇交易释放重新关联申请内码
ft_forex_exag.exposticode=外汇敞口对象收付款方式内码
ft_forex_exag.expoicode=外汇敞口对象内码
ft_forex_exag.swapextype=近端/远端
ft_forex_exag.salordicode=销售合同号内码
ft_forex_exag.salprjicode=销售业务内码
ft_forex_exag.salprjcode=销售业务编号
ft_forex_exag.purordicode=采购合同内码
ft_forex_exag.purprjicode=采购业务内码
ft_forex_exag.purprjcode=采购业务编号
ft_forex_exag.intordcode=意向编号
ft_forex_exag.intordicode=意向编号内码
ft_forex_exag.busiprop=业务性质
ft_forex_exag.ordprop=合同性质
ft_forex_exag.paymode=收付款方式
ft_forex_exag.fcying=待执行金额
ft_forex_exag.fcy=申请交易金额
ft_forex_exag.rdate=预计收付款日期
ft_forex_exag.hedgrate=套保比例
ft_forex_exag.hedgfcy=套保金额
ft_forex_exag.expfcy=敞口余额
ft_forex_exag.isocbpl=是否我司承担损益
ft_forex_exag.ccrate=成本测算汇率

# 【外汇交易申请单】〖成交信息〗
ft_forex_exat.exaticode=外汇交易申请单成交信息内码
ft_forex_exat.exaicode=交易申请内码
ft_forex_exat.exaticoder=外汇交易申请单成交信息红冲核销内码
ft_forex_exat.exaticodeo=原外汇交易申请单成交信息内码
ft_forex_exat.fxprodtype=产品类型
ft_forex_exat.exnumber=交易序号
ft_forex_exat.exnumberrelnum=交易序号关联号
ft_forex_exat.intervalnum=区间宝编号
ft_forex_exat.exdate=交易日期
ft_forex_exat.exbankcode=交易银行
ft_forex_exat.exaccount=交易账号
ft_forex_exat.exfcy=交易金额
ft_forex_exat.issimulate=是否模拟
ft_forex_exat.headbcode=本部部门
ft_forex_exat.farbuyfcode=远端|买入币种
ft_forex_exat.farsalefcode=远端|卖出币种
ft_forex_exat.farrate=远端|汇率
ft_forex_exat.farbuyfcy=远端|买入金额
ft_forex_exat.farsalefcy=远端|卖出金额
ft_forex_exat.farduedate=远端|到期日
ft_forex_exat.duestartdate=到期起始日
ft_forex_exat.nearbuyfcode=近端|买入币种
ft_forex_exat.nearsalefcode=近端|卖出币种
ft_forex_exat.nearrate=近端|汇率
ft_forex_exat.nearbuyfcy=近端|买入金额
ft_forex_exat.nearsalefcy=近端|卖出金额
ft_forex_exat.nearduedate=近端|到期日
ft_forex_exat.sppoint=掉期点
ft_forex_exat.opprice=期权|执行价格
ft_forex_exat.opunionrate=期权|综合汇率
ft_forex_exat.oppremfcode=期权|期权费币种
ft_forex_exat.oppremfcy=期权|期权费金额
ft_forex_exat.opduedate=期权|到期日
ft_forex_exat.opocdc=期权/货方向
ft_forex_exat.delytype=交割类型
ft_forex_exat.futcorpbcode=期货公司
ft_forex_exat.dyopenlot=期货|开仓手数
ft_forex_exat.optccode=期货|操作平台

#【外汇交易申请单】〖成交明细〗
ft_forex_exats.exatsicode=外汇交易申请单成交明细内码
ft_forex_exats.exaticode=外汇交易申请单成交信息内码
ft_forex_exats.exagicode=外汇交易申请单外汇敞口明细内码
ft_forex_exats.exaicode=交易申请内码
ft_forex_exats.exposticode=外汇敞口对象收付款方式内码
ft_forex_exats.expoicode=外汇敞口对象内码
ft_forex_exats.exatsicoder=外汇交易申请单成交明细核销内码
ft_forex_exats.swapextype=近端/远端
ft_forex_exats.salordicode=销售合同号内码
ft_forex_exats.salprjicode=销售业务内码
ft_forex_exats.salprjcode=销售业务编号
ft_forex_exats.purordicode=采购合同内码
ft_forex_exats.purprjicode=采购业务内码
ft_forex_exats.purprjcode=采购业务编号
ft_forex_exats.intordcode=意向编号
ft_forex_exats.intordicode=意向编号内码
ft_forex_exats.paymode=收付款方式
ft_forex_exats.fcy=交易金额
ft_forex_exats.rdate=预计收付日期
ft_forex_exats.isocbpl=是否我司承担损益
ft_forex_exats.ccrate=成本测算汇率
ft_forex_exats.ordprop=合同性质
ft_forex_exats.exrate=交易汇率

#【外汇交易申请成交信息返回接口】
ForexTradeInfoReq.forexData=外汇交易申请成交信息返回接口数据
ForexTradeInfoReqData.srcicode=来源单据内码
ForexTradeInfoReqData.exnumber=交易序号
ForexTradeInfoReqData.intervalnum=区间宝编号
ForexTradeInfoReqData.fxprodtype=产品类型
ForexTradeInfoReqData.exdate=交易日期
ForexTradeInfoReqData.exbankcode=交易银行
ForexTradeInfoReqData.exaccount=交易账号
ForexTradeInfoReqData.exfcy=交易金额
ForexTradeInfoReqData.issimulate=是否模拟
ForexTradeInfoReqData.headbcode=本部部门
ForexTradeInfoReqData.farbuyfcode=远端|买入币种
ForexTradeInfoReqData.farrate=远端|汇率
ForexTradeInfoReqData.farbuyfcy=远端|买入金额
ForexTradeInfoReqData.farsalefcode=远端|卖出币种
ForexTradeInfoReqData.farsalefcy=远端|卖出金额
ForexTradeInfoReqData.farduedate=远端|到期日
ForexTradeInfoReqData.duestartdate=到期起始日
ForexTradeInfoReqData.nearbuyfcode=近端|买入币种
ForexTradeInfoReqData.nearrate=近端|汇率
ForexTradeInfoReqData.nearbuyfcy=近端|买入金额
ForexTradeInfoReqData.nearsalefcode=近端|卖出币种
ForexTradeInfoReqData.nearsalefcy=近端|卖出金额
ForexTradeInfoReqData.nearduedate=近端|到期日
ForexTradeInfoReqData.sppoint=掉期点
ForexTradeInfoReqData.opprice=期权|执行价格
ForexTradeInfoReqData.opunionrate=期权|综合汇率
ForexTradeInfoReqData.opocdc=期权/货方向
ForexTradeInfoReqData.oppremfcode=期权|期权费币种
ForexTradeInfoReqData.oppremfcy=期权|期权费金额
ForexTradeInfoReqData.opduedate=期权|到期日
ForexTradeInfoReqData.delytype=交割类型
ForexTradeInfoReqData.futcorpbcode=期货公司
ForexTradeInfoReqData.dyopenlot=期货|开仓手数
ForexTradeInfoReqData.remark=备注
ForexTradeInfoReqData.vprepare=创建人
ForexTradeInfoReqData.predate=创建时间
ForexTradeInfoReqData.forexDetails=外汇交易申请成交信息返回接口数据
ForexTradeInfoReqDetailData.exposticode=外汇敞口对象收付款方式内码
ForexTradeInfoReqDetailData.fcy=交易金额 */

#【外汇交易申请成交信息撤回接口】
ForexTradeRejectInfoReq.forexTradeRejectInfo=外汇交易申请成交信息撤回接口数据
ForexTradeRejectInfoReqData.srccode=来源单据号
ForexTradeRejectInfoReqData.srcicode=来源单据内码
ForexTradeRejectInfoReqData.srcsheetcode=来源单据类型
ForexTradeRejectInfoReqData.exnumber=交易序号
ForexTradeRejectInfoReqData.modifier=修改人
ForexTradeRejectInfoReqData.modifydate=修改时间

#【外汇交易单据驳回接口】
ForexRejectedReq.forexTradeRejectInfo=外汇交易单据驳回接口数据
ForexRejectedReqData.srcicode=来源单据内码
ForexRejectedReqData.srcsheetcode=来源单据类型
ForexRejectedReqData.exatype=是否部分退回
ForexRejectedReqData.fcy=交易金额

#【即期结汇申请单】
ft_forex_sesa=即期结汇申请单
ft_forex_sesa.sesaicode=结汇申请内码
ft_forex_sesa.sesacode=结汇申请单号
ft_forex_sesa.deptbcode=事业部
ft_forex_sesa.tgbicode=同审单内码
ft_forex_sesa.tgbcode=同审单号
ft_forex_sesa.rdate=预计结汇日期
ft_forex_sesa.rdatefm=预计结汇日期从
ft_forex_sesa.esratefm=预计结汇汇率从
ft_forex_sesa.esrateto=预计结汇汇率到
ft_forex_sesa.isexcsetted=是否已结汇
ft_forex_sesa.bankcode=结汇银行
ft_forex_sesa.bankcode=结汇银行
ft_forex_sesa.actiontype=结汇行为
ft_forex_sesa.esbcode=结汇部门
ft_forex_sesa.issimulate=是否模拟
ft_forex_sesa.iscasexp=是否需要关联敞口
ft_forex_sesa.fcy=结汇金额
ft_forex_sesa.isneedpfund=是否需要推资金
ft_forex_sesa.isautopfund=是否自动推送资金

#【即期结汇申请单】〖结汇资金明细〗
ft_forex_sesag.sesagicode=即期结汇申请单结汇资金明细内码
ft_forex_sesag.sesaicode=结汇申请内码
ft_forex_sesag.sesagicoder=即期结汇申请单结汇资金明细核销内码
ft_forex_sesag.cpscode=资金池记录编号
ft_forex_sesag.fcying=待执行金额
ft_forex_sesag.fcy=结汇金额
ft_forex_sesag.salprjicode=销售业务内码
ft_forex_sesag.salordicode=销售合同内码

# 【即期结汇申请单】〖外汇敞口明细〗
ft_forex_sesags.sesagsicode=即期结汇申请单结汇资金明细外汇敞口明细内码
ft_forex_sesags.sesaicode=结汇申请内码
ft_forex_sesags.sesagicode=即期结汇申请单结汇资金明细内码
ft_forex_sesags.exposticode=外汇敞口对象收付款方式内码
ft_forex_sesags.salordicode=销售合同内码
ft_forex_sesags.salprjicode=销售业务内码
ft_forex_sesags.paymode=收付款方式
ft_forex_sesags.busiprop=业务性质

# 外汇成交待处理工作台 视图
ft_forex_exats_cb_view.exnumber=交易序号
ft_forex_exats_cb_view.exacode=交易申请单号
ft_forex_exats_cb_view.salordcode=销售合同号
ft_forex_exats_cb_view.purordcode=${purordcode}
ft_forex_exats_cb_view.intordcode=意向编号
ft_forex_exats_cb_view.fxprodtype=产品类型
ft_forex_exats_cb_view.issimulate=是否模拟
ft_forex_exats_cb_view.exdatefm=交易日期从
ft_forex_exats_cb_view.corpbcode=${corpbcode}
ft_forex_exats_cb_view.predatefm=${predatefm}
ft_forex_exats_cb_view.swapextype=近端/远端
ft_forex_exats_cb_view.exnumberrelnum=交易序号关联号
ft_forex_exats_cb_view.intervalnum=区间宝编号
ft_forex_exats_cb_view.ordprop=合同性质
ft_forex_exats_cb_view.exdate=交易日期
ft_forex_exats_cb_view.exbankcode=交易银行
ft_forex_exats_cb_view.exaccount=交易账号
ft_forex_exats_cb_view.headbcode=本部部门
ft_forex_exats_cb_view.busimode=${busimode}
ft_forex_exats_cb_view.tradetype=${tradetype}
ft_forex_exats_cb_view.paymode=收付款方式
ft_forex_exats_cb_view.fcode=交易币种
ft_forex_exats_cb_view.fcy=计划执行金额
ft_forex_exats_cb_view.fcyed=已执行金额
ft_forex_exats_cb_view.fcying=待执行金额
ft_forex_exats_cb_view.isocbpl=是否我司承担损益
ft_forex_exats_cb_view.vprepare=${vprepare}
ft_forex_exats_cb_view.hedgrate=套保比例
ft_forex_exats_cb_view.ccrate=成本测算汇率
ft_forex_exats_cb_view.modifydate=${modifydate}
ft_forex_exats_cb_view.exduedatefm=交易到期日从
ft_forex_exats_cb_view.exduedate=交易到期日
ft_forex_exats_cb_view.exatsicode=成交明细内码
ft_forex_exats_cb_view.exatsicoder=明细成交核销内码
ft_forex_exats_cb_view.nearduedate=近端|到期日
ft_forex_exats_cb_view.opprice=执行价格
ft_forex_exats_cb_view.opunionrate=综合汇率
ft_forex_exats_cb_view.copygensrc=拷贝新建单据界面

# 外汇交易释放重新关联申请 主表
ft_forex_exrc.exrcicode=内码
ft_forex_exrc.sheetcode=单据类型
ft_forex_exrc=外汇交易释放重新关联申请
ft_forex_exrc.exrccode=重新关联申请单号
ft_forex_exrc.fxprodtype=产品类型
ft_forex_exrc.salordcode=销售合同号
ft_forex_exrc.purordcode=${purordcode}
ft_forex_exrc.intordcode=意向编号
ft_forex_exrc.exacode=交易申请单号
ft_forex_exrc.wcode=${wcode}
ft_forex_exrc.corpbcode=${corpbcode}
ft_forex_exrc.predatefm=${predatefm}
ft_forex_exrc.adate=申请日期
ft_forex_exrc.exdate=申请交易日期
ft_forex_exrc.ordprop=合同性质
ft_forex_exrc.fcode=交易币种
ft_forex_exrc.remark=${remark}
ft_forex_exrc.vprepare=${vprepare}
ft_forex_exrc.predate=${predate}
ft_forex_exrc.exnumber=交易序号
ft_forex_exrc.intervalnum=区间宝编号
ft_forex_exrc.changemsg=变更原因
ft_forex_exrc.swapextype=近端/远端
ft_forex_exrc.deptbcode=${bubcode}

# 外汇交易释放重新关联申请 释放信息
ft_forex_exrel.exrcicode=主表内码
ft_forex_exrel.exrelicode=内码
ft_forex_exrel.salordcode=销售合同号
ft_forex_exrel.purordcode=${purordcode}
ft_forex_exrel.intordcode=意向编号
ft_forex_exrel.busimode=${busimode}
ft_forex_exrel.tradetype=贸易方式
ft_forex_exrel.paymode=收付款方式
ft_forex_exrel.fcying=交易金额
ft_forex_exrel.fcy=变更金额
ft_forex_exrel.hedgrate=套保比例
ft_forex_exrel.isocbpl=是否我司承担损益
ft_forex_exrel.ccrate=成本测算汇率
ft_forex_exrel.wcode=${wcode}
ft_forex_exrel.bcode=${bcode}
ft_forex_exrel.salprjcode=销售业务编号
ft_forex_exrel.purprjcode=采购业务编号
ft_forex_exrel.salordicode=销售合同号内码
ft_forex_exrel.purordicode=采购合同号内码
ft_forex_exrel.intordicode=意向编号内码
ft_forex_exrel.ordprop=合同性质
ft_forex_exrel.rdate=预计收付日期


#外汇交易明细
ft_forex_exag.exacode=交易申请单号
ft_forex_exag.fxprodtype=产品类型
ft_forex_exag.fcode=${fcode}
ft_forex_exag.fcy=交易金额
ft_forex_exag.bcode=${bcode}

# 外汇交易释放重新关联申请 重新关联信息
ft_forex_excas.exrcicode=主表内码
ft_forex_excas.exrelicode=内码
ft_forex_excas.salordcode=销售合同号
ft_forex_excas.purordcode=${purordcode}
ft_forex_excas.intordcode=意向编号
ft_forex_excas.busimode=${busimode}
ft_forex_excas.tradetype=贸易类型
ft_forex_excas.paymode=收付款方式
ft_forex_excas.fcy=交易金额
ft_forex_excas.fcying=${fcy}
ft_forex_excas.hedgrate=套保比例
ft_forex_excas.isocbpl=是否我司承担损益
ft_forex_excas.ccrate=成本测算汇率
ft_forex_excas.wcode=${wcode}
ft_forex_excas.bcode=${bcode}
ft_forex_excas.hedgfcy=套保金额
ft_forex_excas.expfcy=敞口余额
ft_forex_excas.ordprop=合同性质
ft_forex_excas.exposticode=敞口对象收付款方式内码
ft_forex_excas.rdate=预计收付日期

# 外汇移仓申请
ft_forex_moa=外汇移仓申请
ft_forex_moa.limitap.data=外汇移仓申请入参
ft_forex_moa.moacode=外汇移仓申请单号
ft_forex_moa.adatefm=${adatefm}
ft_forex_moa.bcode=${bcode}
ft_forex_moa.wcode=${wcode}
ft_forex_moa.vprepare=${vprepare}
ft_forex_moa.predatefm=${predatefm}
ft_forex_moa.predateto=${dateto}
ft_forex_moa.adate=${adate}
ft_forex_moa.corpbcode=${corpbcode}
ft_forex_moa.vprepare=${vprepare}
ft_forex_moa.predate=${predate}
ft_forex_moa.mpreason=移仓原因
ft_forex_moa.remark=${remark}
ft_forex_moa.isneedpfund=是否需要推资金
ft_forex_moa.isautopfund=是否自动推资金
#平仓明细
ft_forex_eca_pc.ecacode=平仓申请单号
ft_forex_eca_pc.fxprodtype=产品类型
ft_forex_eca_pc.fcode=${fcode}
ft_forex_eca_pc.fcy=平仓金额
ft_forex_eca_pc.bcode=${bcode}

# 展期/平仓申请主表 主表
ft_forex_eca=展期/平仓申请主表
ft_forex_eca.ecaicode=展期/平仓申请内码
ft_forex_eca.ecacode=展期申请单号
ft_forex_eca.ecacode1=平仓申请单号
ft_forex_eca.ecacode2=交割申请单号
ft_forex_eca.wfcode=审批编码
ft_forex_eca.wfuid=审批节点
ft_forex_eca.ratifydate=${ratifydate}
ft_forex_eca.submitdate=${submitdate}
ft_forex_eca.performdate=审核时间
ft_forex_eca.vprepare=${vprepare}
ft_forex_eca.predate=${predate}
ft_forex_eca.modifier=${modifier}
ft_forex_eca.modifydate=${modifydate}
ft_forex_eca.adate=${adate}
ft_forex_eca.fxprodtype=产品类型
ft_forex_eca.sfcode=${sfcode}
ft_forex_eca.fcode=交易币种
ft_forex_eca.issimulate=是否模拟
ft_forex_eca.aecdate1=申请平仓日期
ft_forex_eca.aecdatefm1=申请平仓日期从
ft_forex_eca.aecdate2=申请交割日期
ft_forex_eca.aecdatefm2=申请交割日期从
ft_forex_eca.eclot=展期手数
ft_forex_eca.eclot1=平仓手数
ft_forex_eca.eclot2=交割手数
ft_forex_eca.fcy=展期金额
ft_forex_eca.fcy1=平仓金额
ft_forex_eca.fcy2=交割金额
ft_forex_eca.purchtype=购汇类型
ft_forex_eca.duratype=存续期类型
ft_forex_eca.duedate=申请交易到期日
ft_forex_eca.duedate1=期权到期日
ft_forex_eca.duedatefm=申请交易到期日从
ft_forex_eca.duedateto=到
ft_forex_eca.exduedatefm=交易到期日从
ft_forex_eca.exduedateto=交易到期日到
ft_forex_eca.ecreason=展期原因
ft_forex_eca.ecreason1=平仓原因
ft_forex_eca.ecreason2=提前交割原因
ft_forex_eca.exnumber=交易序号
ft_forex_eca.exaicode=交易申请内码
ft_forex_eca.exacode=交易申请单号
ft_forex_eca.intervalnum=区间宝编号
ft_forex_eca.remark=${remark}
ft_forex_eca.fxbusisrctype=来源方式
ft_forex_eca.isneedpfund=是否需要推资金
ft_forex_eca.isautopfund=是否自动推资金
ft_forex_eca.srcsheetcode=${srcsheetcode}
ft_forex_eca.srcicode=${srcicode}
ft_forex_eca.srccode=${srccode}


# 展期/平仓申请子表 子表
ft_forex_ecag=展期/平仓申请明细
ft_forex_ecag.ecagicode=展期/平仓申请明细内码
ft_forex_ecag.ecaicode=展期/平仓申请内码
ft_forex_ecag.exatsicoder=明细成交核销内码
ft_forex_ecag.exposticode=敞口对象收付款方式内码
ft_forex_ecag.expoicode=外汇敞口对象内码
ft_forex_ecag.idx=${idx}
ft_forex_ecag.swapextype=近端/远端
ft_forex_ecag.salordcode=销售合同号
ft_forex_ecag.salordicode=销售合同内码
ft_forex_ecag.salprjicode=销售合同业务内码
ft_forex_ecag.salprjcode=销售合同业务编号
ft_forex_ecag.purordcode=采购合同号
ft_forex_ecag.purordicode=采购合同内码
ft_forex_ecag.purprjicode=采购业务内码
ft_forex_ecag.purprjcode=采购业务编号
ft_forex_ecag.intordcode=意向编号
ft_forex_ecag.intordicode=意向编号内码
ft_forex_ecag.ordprop=合同性质
ft_forex_ecag.busimode=经营方式
ft_forex_ecag.tradetype=贸易方式
ft_forex_ecag.paymode=收付款方式
ft_forex_ecag.fcode=${fcode}
ft_forex_ecag.fcying=交易金额
ft_forex_ecag.fcy1=展期金额
ft_forex_ecag.fcy2=交割金额
ft_forex_ecag.fcy=平仓金额
ft_forex_ecag.rdate=预计收付日期
ft_forex_ecag.isocbpl=是否我司承担损益
ft_forex_ecag.bcode=${bcode}
ft_forex_ecag.wcode=${wcode}

# 交割申请业务信息
ft_forex_ecap.salordcode=销售合同号
ft_forex_ecap.salordicode=销售合同号内码
ft_forex_ecap.salprjicode=销售合同业务内码
ft_forex_ecap.salprjcode=销售业务编号
ft_forex_ecap.purordcode=采购合同号
ft_forex_ecap.purordicode=采购合同内码
ft_forex_ecap.purprjicode=采购业务内码
ft_forex_ecap.purprjcode=采购业务编号
ft_forex_ecap.purshipicoder=到货单内码
ft_forex_ecap.purshipcoder=到货单号
ft_forex_ecap.fcode=币种
ft_forex_ecap.fcy=交割金额
ft_forex_ecap.srcsheetcode=来源单据类型
ft_forex_ecap.srccode=来源单据号
ft_forex_ecap.srcicode=来源单据内码

# 展期/平仓成交信息
ft_forex_ecat=展期/平仓申请成交信息表
ft_forex_ecat.ecaticode=展期/平仓申请成交信息内码
ft_forex_ecat.ecaicode=展期/平仓申请内码
ft_forex_ecat.exnumber=交易序号
ft_forex_ecat.srcexnumber=原交易序号
ft_forex_ecat.intervalnum=区间宝编号
ft_forex_ecat.issimulate=是否模拟
ft_forex_ecat.headbcode=本部部门
ft_forex_ecat.extendtype=展期类型
ft_forex_ecat.fcode=展期币种
ft_forex_ecat.fcode1=平仓币种
ft_forex_ecat.fcode2=交割交易币种
ft_forex_ecat.fcy=平仓金额
ft_forex_ecat.fcy1=展期金额
ft_forex_ecat.fcy2=交割交易金额
ft_forex_ecat.exdate=平仓交易日期
ft_forex_ecat.exdate1=展期交易日期
ft_forex_ecat.exdate2=交割交易日期
ft_forex_ecat.duedate=展期交易到期日期
ft_forex_ecat.opduedate=展期期权|到期日
ft_forex_ecat.exrate=平仓交易汇率
ft_forex_ecat.exrate1=展期交易汇率
ft_forex_ecat.exrate2=交割交易汇率
ft_forex_ecat.premiumtype=平仓期权费率类型
ft_forex_ecat.premiumtype1=展期期权费率类型
ft_forex_ecat.premiumtype2=交割期权费率类型
ft_forex_ecat.premium=平仓期权费率
ft_forex_ecat.premium1=展期期权费率
ft_forex_ecat.premium2=交割期权费率
ft_forex_ecat.premfcode=平仓期权费币种
ft_forex_ecat.premfcode1=展期期权费币种
ft_forex_ecat.premfcode2=交割期权费币种
ft_forex_ecat.premfcy=平仓期权费金额
ft_forex_ecat.premfcy1=展期期权费金额
ft_forex_ecat.premfcy2=交割期权费金额
ft_forex_ecat.profitfcy=损益金额
ft_forex_ecat.futcorpbcode=期货公司
ft_forex_ecat.remark=${remark}




# 银行估值表
ft_forex_bval=银行估值表
ft_forex_bval.cuicode=${cuicode}
ft_forex_bval.sheetcode=${sheetcode}
ft_forex_bval.vFlag#1=凭证
ft_forex_bval.srcsyscode=来源系统唯一ID
ft_forex_bval.srcicode=来源单据内码
ft_forex_bval.viddesc=凭证号
ft_forex_bval.batchvid=凭证大号
ft_forex_bval.corpbcode=${corpbcode}
ft_forex_bval.year=${year}
ft_forex_bval.month=${month}
ft_forex_bval.tcaptime=${tcaptime}
ft_forex_bval.profitfcy=损益原币金额
ft_forex_bval.profitfcode=损益币种
ft_forex_bval.redflag#1=${bered}
ft_forex_bval.redflag#2=${red}
ft_forex_bval.redflag=红冲
ft_forex_bval.ischndom#Y=中国境内
ft_forex_bval.keptbcode=${keptbcode}
ft_forex_bval.vprepare=${vprepare}
ft_forex_bval.predate=${predate}
ft_forex_bval.modifier=${modifier}
ft_forex_bval.modifydate=${modifydate}
ft_forex_bval.fxprodtype=产品类型
ft_forex_bval.salordcode=销售合同号
ft_forex_bval.purordcode=采购合同号
ft_forex_bval.intordcode=意向编号
ft_forex_bval.exnumber=交易序号
ft_forex_bval.exacode=交易申请单号
ft_forex_bval.rptdaytype=1-月末浮动盈亏
ft_forex_bval.vidflag=制证选项
#银行估值信息
ft_forex_bvalg.cuicode=${cuicode}
ft_forex_bvalg.bvalicode=银行估值信息表内码
ft_forex_bvalg.bcode=${bcode}
ft_forex_bvalg.exacode=交易申请单号
ft_forex_bvalg.exdc=交易方向
ft_forex_bvalg.fxprodtype=产品类型
ft_forex_bvalg.exdate=交易日期
ft_forex_bvalg.fcode=交易币种
ft_forex_bvalg.exrate=交易汇率
ft_forex_bvalg.fcy=交易金额
ft_forex_bvalg.profitfcy=损益原币金额
ft_forex_bvalg.exbankcode=交易银行
ft_forex_bvalg.futcorpbcode=期货公司
ft_forex_bvalg.exnumber=交易序号
ft_forex_bvalg.exnumberrelnum=交易序号关联号
ft_forex_bvalg.ftdc=浮动方向
ft_forex_bvalg.remark=${remark}
ft_forex_bvalg.srcsyscode=来源系统唯一ID
#分摊明细
ft_forex_bvals.salprjcode=销售业务编号
ft_forex_bvals.purprjcode=采购业务编号
ft_forex_bvals.salordcode=销售合同号
ft_forex_bvals.purordcode=采购合同号
ft_forex_bvals.intordcode=意向编号
ft_forex_bvals.fcy=交易金额
ft_forex_bvals.fserate=即期汇率
ft_forex_bvals.profitfcy=损益|原币金额
ft_forex_bvals.profitscy=损益|本位币金额
ft_forex_bvals.scerate=折人民币汇率
ft_forex_bvals.suerate=折美元汇率
ft_forex_bvals.profitzcny=损益|人民币金额
ft_forex_bvals.profitzusd=损益|美元金额
ft_forex_bvals.taxrate=税率
ft_forex_bvals.taxfcy=递延所得税|原币金额
ft_forex_bvals.taxscy=递延所得税|本位币金额
ft_forex_bvals.taxzcny=递延所得税|人民币金额
ft_forex_bvals.taxzusd=递延所得税|美元金额
ft_forex_bvals.taxfcerate=递延所得税|折人民币汇率
ft_forex_bvals.taxsuerate=递延所得税|折美元汇率


# 外汇交易复核入账 主表
ft_forex_exrae=外汇交易复核入账主表
ft_forex_exrae.exraeicode=内码
ft_forex_exrae.ischndom#Y=中国境内
ft_forex_exrae.fxprodtype=产品类型
ft_forex_exrae.issimulate=是否模拟
ft_forex_exrae.headbcode=本部部门
ft_forex_exrae.srcsheetcode=${srcsheetcode}
ft_forex_exrae.srcicode=${srcicode}
ft_forex_exrae.srccode=${srccode}
ft_forex_exrae.exnumber=交易序号
ft_forex_exrae.exnumberrelnum=交易序号关联号
ft_forex_exrae.intervalnum=区间宝编号
ft_forex_exrae.exdc=交易方向
ft_forex_exrae.fcy=交易金额
ft_forex_exrae.exrate=交易汇率
ft_forex_exrae.outcode=外部单号
ft_forex_exrae.swapextype=近端/远端
ft_forex_exrae.vprepare=${vprepare}
ft_forex_exrae.predate=${predate}
ft_forex_exrae.modifier=${modifier}
ft_forex_exrae.modifydate=${modifydate}
ft_forex_exrae.sppoint=掉期点
ft_forex_exrae.futcorpbcode=期货公司
ft_forex_exrae.recbankno=收款|网银流水号
ft_forex_exrae.recbankcode=收款|银行
ft_forex_exrae.recbankcode1=收款银行
ft_forex_exrae.recbankacccode=收款|银行账号
ft_forex_exrae.recdate=收款|日期
ft_forex_exrae.recfcode=收款|币种
ft_forex_exrae.recfserate=收款|汇率
ft_forex_exrae.recfcy=收款|原币金额
ft_forex_exrae.recscy=收款|本位币金额
ft_forex_exrae.inprofcode=投资收益|币种
ft_forex_exrae.inprofcy=投资收益|原币金额
ft_forex_exrae.inpronatscy=投资收益|不含税本位币金额
ft_forex_exrae.taxrate=税率
ft_forex_exrae.duetaxscy=应交税费本位币金额
ft_forex_exrae.paybankno=支出|网银流水号
ft_forex_exrae.paybankcode=支出|银行
ft_forex_exrae.paybankcode1=支出银行
ft_forex_exrae.paybankacccode=支出|银行账号
ft_forex_exrae.paydate=支出|日期
ft_forex_exrae.payfcode=支出|币种
ft_forex_exrae.payfserate=支出|汇率
ft_forex_exrae.payfcy=支出|原币金额
ft_forex_exrae.payscy=支出|本位币金额
ft_forex_exrae.lossscy=汇兑损益
ft_forex_exrae.tcaptime=事务处理日期
ft_forex_exrae.tcaptimefm=事务处理日期从
ft_forex_exrae.vidflag=制证选项

# 外汇交易复核入账资金来源分摊表
ft_fund_fsrcg_cb_view=外汇交易复核入账资金来源分摊表
ft_fund_fsrcg_cb_view.srcsheetcode=${srcsheetcode}
ft_fund_fsrcg_cb_view.srcicode=${srcicode}
ft_fund_fsrcg_cb_view.srccode=${srccode}
ft_fund_fsrcg_cb_view.fsrcgicode=资金来源分摊内码
ft_fund_fsrcg_cb_view.bcode=${bcode}
ft_fund_fsrcg_cb_view.wcode=${wcode}
ft_fund_fsrcg_cb_view.fcode=币种
ft_fund_fsrcg_cb_view.salordcode=销售合同号
ft_fund_fsrcg_cb_view.salordicode=销售合同号内码
ft_fund_fsrcg_cb_view.salprjicode=销售业务内码
ft_fund_fsrcg_cb_view.salprjcode=销售业务编号
ft_fund_fsrcg_cb_view.purordcode=采购合同号
ft_fund_fsrcg_cb_view.purordicode=采购合同内码
ft_fund_fsrcg_cb_view.purprjicode=采购业务内码
ft_fund_fsrcg_cb_view.purprjcode=采购业务编号
ft_fund_fsrcg_cb_view.purshipicoder=到货单内码
ft_fund_fsrcg_cb_view.purshipcoder=到货单号
ft_fund_fsrcg_cb_view.fcy=申请购汇金额
ft_fund_fsrcg_cb_view.fcyed=已匹配购汇金额
ft_fund_fsrcg_cb_view.fcying=待匹配购汇金额
ft_fund_fsrcg_cb_view.matchfcy=本次匹配购汇金额

# 外汇交易复核入账分摊表
ft_forex_exraeg=外汇交易复核入账分摊表
ft_forex_exraeg.exraegicode=分摊内码
ft_forex_exraeg.exraeicode=主表内码
ft_forex_exraeg.vidflag#1=凭证
ft_forex_exraeg.vno=凭证号
ft_forex_exraeg.salordcode=销售合同号
ft_forex_exraeg.salordicode=销售合同号内码
ft_forex_exraeg.salprjicode=销售业务内码
ft_forex_exraeg.salprjcode=销售业务编号
ft_forex_exraeg.purordcode=采购合同号
ft_forex_exraeg.purordicode=采购合同内码
ft_forex_exraeg.purprjicode=采购业务内码
ft_forex_exraeg.purprjcode=采购业务编号
ft_forex_exraeg.intordcode=意向编号
ft_forex_exraeg.intordicode=意向编号内码
ft_forex_exraeg.purshipicoder=到货单内码
ft_forex_exraeg.purshipcoder=到货单号
ft_forex_exraeg.srcsheetcode=${srcsheetcode}
ft_forex_exraeg.srcicode=${srcicode}
ft_forex_exraeg.srccode=${srccode}
ft_forex_exraeg.fcy=关联金额
ft_forex_exraeg.lossscy=汇兑损益
ft_forex_exraeg.inprofcy=投资收益|原币金额
ft_forex_exraeg.inproscy=投资收益|本位币金额
ft_forex_exraeg.keptbcode=${keptbcode}


# 外汇交易复核入账回传接口
ft_forex.exrae.exraeInfos=接口参数[exraeInfos]
ft_forex.exrae.srcsyscode=来源系统唯一ID[srcsyscode]
ft_forex.exrae.bcode=业务员部门[bcode]
ft_forex.exrae.corpbcode=公司[corpbcode]
ft_forex.exrae.fxprodtype=外汇产品类型[fxprodtype]
ft_forex.exrae.fcode=币种[fcode]
ft_forex.exrae.issimulate=是否模拟[issimulate]
ft_forex.exrae.headbcode=本部部门[headbcode]
ft_forex.exrae.srcsheetcode=来源单据类型[srcsheetcode]
ft_forex.exrae.srcicode=来源单据内码[srcicode]
ft_forex.exrae.srccode=来源单据号[srccode]
ft_forex.exrae.handsrccode=来源单据号手工录入[handsrccode]
ft_forex.exrae.exnumber=交易序号[exnumber]
ft_forex.exrae.exnumberrelnum=交易序号关联号[exnumberrelnum]
ft_forex.exrae.intervalnum=区间宝编号[intervalnum]
ft_forex.exrae.exrate=交易汇率[exrate]
ft_forex.exrae.fcy=交易金额[fcy]
ft_forex.exrae.exdc=交易方向[exdc]
ft_forex.exrae.outcode=外部单号[outcode]
ft_forex.exrae.swapextype=近端/远端[swapextype]
ft_forex.exrae.sppoint=掉期点[sppoint]
ft_forex.exrae.futcorpbcode=期货公司[futcorpbcode]
ft_forex.exrae.recbankno=收款|网银流水号[recbankno]
ft_forex.exrae.recbankcode=收款|银行[recbankcode]
ft_forex.exrae.recbankacccode=收款|银行账号[recbankacccode]
ft_forex.exrae.recdate=收款|日期[recdate]
ft_forex.exrae.recfcode=收款|币种[recfcode]
ft_forex.exrae.recfcy=收款|原币金额[recfcy]
ft_forex.exrae.paybankno=支出|网银流水号[paybankno]
ft_forex.exrae.paybankcode=支出|银行[paybankcode]
ft_forex.exrae.paybankacccode=支出|银行账号[paybankacccode]
ft_forex.exrae.paydate=支出|日期[paydate]
ft_forex.exrae.payfcode=支出|币种[payfcode]
ft_forex.exrae.payfcy=支出|原币金额[payfcy]
ft_forex.exrae.remark=备注[remark]

ft_forex.exrae.exraeBackParams=接口参数[exraeBackParams]
