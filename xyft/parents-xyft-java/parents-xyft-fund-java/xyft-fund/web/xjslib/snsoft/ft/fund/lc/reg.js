Xjs.loadedXjs.push("snsoft/ft/fund/lc/reg");
Xjs.namespace("snsoft.ft.lc.reg.invoker");snsoft.ft.lc.reg.invoker.LcRegSyncSalesAmountInvoker=function(parameter){snsoft.ft.lc.reg.invoker.LcRegSyncSalesAmountInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.lc.reg.invoker.LcRegSyncSalesAmountInvoker,snsoft.ext.cmd.CommandInvoker,{invoke:function(event){var salordssicodeList=[];for(var i=0;i<this.dataSet.getRows().length;i++){salordssicodeList[i]=this.dataSet.getValue("lcreggicode",i)+","+this.dataSet.getValue("salordssicode",i);}return {salordssicodeList:salordssicodeList,lcregicode:this.mainDataSet.getValue("lcregicode"),modifydate:this.mainDataSet.get("modifydate")};},afterInvoke:function(event){this.dataSet.refresh();this.mainTable.refreshTableIfOK();}});Xjs.namespace("snsoft.ft.lc.reg.lis");snsoft.ft.lc.reg.lis.LcRegJSListener=function(params){snsoft.ft.lc.reg.lis.LcRegJSListener.superclass.constructor.call(this,params);};Xjs.extend(snsoft.ft.lc.reg.lis.LcRegJSListener,snsoft.plat.bas.busi.SystemFunctionListener,{initComponent:function(table,values){snsoft.ft.lc.reg.lis.LcRegJSListener.superclass.initComponent.call(this,table,values);if(!this.letterOfCreditInfTwoTable)this.letterOfCreditInfTwoTable=this.getTable(table,"letterOfCreditInf_2");},dataSetRowNavigated:function(dataSet,e){var overshiprate=this.letterOfCreditInfTwoTable.getColumn("overshiprate");if(dataSet.get("isosrate")=="Y"){overshiprate.nonBlankOnSubmit=true;overshiprate.setReadonly(false);overshiprate.ignoreTblRdonly=true;}else if(dataSet.get("isosrate")=="N"){dataSet.setValue("overshiprate",null);overshiprate.nonBlankOnSubmit=false;overshiprate.setReadonly(true);overshiprate.ignoreTblRdonly=false;}else if(!dataSet.get("isosrate")||dataSet.get("isosrate")===undefined){dataSet.setValue("overshiprate",null);overshiprate.nonBlankOnSubmit=false;overshiprate.setReadonly(true);}if("S01"==dataSet.get("vsntype"))overshiprate.setReadonly(true);this.letterOfCreditInfTwoTable.render(2);},dataLoaded:function(dataSet,event){snsoft.ft.lc.reg.lis.LcRegJSListener.superclass.dataLoaded.call(this,dataSet,event);},dataSetFieldPosting:function(dataSet,event){snsoft.ft.lc.reg.lis.LcRegJSListener.superclass.dataSetFieldPosting.call(this,dataSet,event);if(event.columnName=="overshiprate"&&event.value){var overshiprateVal=event.value;if(overshiprateVal<0||overshiprateVal>1){throw new Error(Xjs.ResBundle.getResVal("FT-LC.00000045"));}}},dataSetFieldPosted:function(dataSet,event){var overshiprate=this.letterOfCreditInfTwoTable.getColumn("overshiprate");if(event.columnName=="isosrate"&&dataSet.get("isosrate")=="Y"){overshiprate.nonBlankOnSubmit=true;overshiprate.setReadonly(false);overshiprate.ignoreTblRdonly=true;}else if(event.columnName=="isosrate"&&dataSet.get("isosrate")=="N"){dataSet.setValue("overshiprate",null);overshiprate.nonBlankOnSubmit=false;overshiprate.setReadonly(true);overshiprate.ignoreTblRdonly=false;}else if(event.columnName=="isosrate"&&!dataSet.get("isosrate")){dataSet.setValue("overshiprate",null);overshiprate.nonBlankOnSubmit=false;overshiprate.setReadonly(true);overshiprate.ignoreTblRdonly=false;}if(event.columnName=="isosrate"&&"S01"==dataSet.get("vsntype"))overshiprate.setReadonly(true);this.letterOfCreditInfTwoTable.render(2);}});
