<?xml version="1.0" encoding="UTF-8"?>
<timertask-list xmlns="http://www.snsoft.com.cn/schema/TimerTask"
	xsi:schemaLocation="http://www.snsoft.com.cn/schema/TimerTask http://www.snsoft.com.cn/schema/TimerTask.xsd"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<timertask id="FT-REC.001" name="议付交单自动认款定时任务" work-time="0:0" interval="0" auto-start="false" thread="true" task-type="bean">
		<task>FT-REC.RecDlydAutoClaimTimer</task>
		<remark><![CDATA[
            其他参数类需配置：
            	{'env.USERCUICODE':'XXX(商户)','env.USERCODE':'XXX(用户)'}
        ]]></remark>
	</timertask>
	<timertask id="FT-REC.002" name="内部交易自动认款定时任务" work-time="0:0" interval="0" auto-start="false" thread="true" task-type="bean">
		<task>FT-REC.RecIntrAutoClaimTimer</task>
		<remark><![CDATA[
            其他参数类需配置：
            	{'env.USERCUICODE':'XXX(商户)','env.USERCODE':'XXX(用户)'}
        ]]></remark>
	</timertask>
</timertask-list>