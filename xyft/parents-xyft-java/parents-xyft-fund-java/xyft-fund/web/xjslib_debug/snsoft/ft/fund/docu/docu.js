Xjs.loadedXjs.push("snsoft/ft/fund/docu/docu");
/*snsoft/ft/docu/accord/invoker/AccOrdHisInvoker.java*/
Xjs.namespace("snsoft.ft.docu.accord.invoker");
snsoft.ft.docu.accord.invoker.AccOrdHisInvoker=function(parameter){
    snsoft.ft.docu.accord.invoker.AccOrdHisInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.docu.accord.invoker.AccOrdHisInvoker,snsoft.ext.cmd.com.biz.DialogInvoker,{
  _js$className_:"snsoft.ft.docu.accord.invoker.AccOrdHisInvoker",
    /*snsoft.ft.docu.accord.invoker.AccOrdHisInvoker.onLoad*/
    onLoad:function(root,event)
    {
        var purshipicoder = this.dataSet.getValue("purshipicoder"),
            dlgTable = Xjs.ui.Component.getItemByName(root,"qryHisDocu");
        dlgTable.dataSet.setRefreshParameter("purshipicoder",purshipicoder);
        this.dlg.buttons = Xjs.ui.Panel.newOkButtons(["close:" + Xjs.ResBundle.getResVal("Dlg.Close")],null);
    }
});
/*snsoft/ft/docu/accord/invoker/AccOrdRegDeleteInvoker.java*/
snsoft.ft.docu.accord.invoker.AccOrdRegDeleteInvoker=function(parameter){
    snsoft.ft.docu.accord.invoker.AccOrdRegDeleteInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.docu.accord.invoker.AccOrdRegDeleteInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.docu.accord.invoker.AccOrdRegDeleteInvoker",
    /*snsoft.ft.docu.accord.invoker.AccOrdRegDeleteInvoker.beforeCheck*/
    beforeCheck:function(event)
    {
        var isreferable = this.mainDataSet.getValue("isreferable");
        if(isreferable == "Y")
        {
            throw new Error(Xjs.ResBundle.getResVal("FT-DOCU.00000014"));
        }
        return null;
    }
});
/*snsoft/ft/docu/accord/invoker/AddFundsrcCheckInvoker.java*/
snsoft.ft.docu.accord.invoker.AddFundsrcCheckInvoker=function(parameter){
    snsoft.ft.docu.accord.invoker.AddFundsrcCheckInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.docu.accord.invoker.AddFundsrcCheckInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.docu.accord.invoker.AddFundsrcCheckInvoker",
    /*snsoft.ft.docu.accord.invoker.AddFundsrcCheckInvoker.beforeInvoke*/
    beforeInvoke:function(event)
    {
        var fcode = this.mainDataSet.getValue("fcode"),
            sfcode = this.mainDataSet.getValue("sfcode");
        if(fcode == sfcode)
        {
            var showdlg = this.getShowdlg(event);
            if(!showdlg)
            {
                return null;
            }
            var showdlgValue = showdlg.getValue(),
                showValues = Array.isArray(showdlgValue) ? showdlgValue : showdlgValue.split(",");
            for(var i=0,len=showValues.length;i < len;i++)
            {
                if(!(showValues[i] == "50" || showValues[i] == "60"))
                {
                    throw new Error(this.getResVal("FT-DOCU.00000038"));
                }
            }
        }
        return null;
    },
    /*snsoft.ft.docu.accord.invoker.AddFundsrcCheckInvoker.getShowdlg*/
    getShowdlg:function(event)
    {
        return event.checkData.showdlg;
    }
});
/*snsoft/ft/docu/accord/invoker/DocuAccordFundSrcDeleteInvoker.java*/
snsoft.ft.docu.accord.invoker.DocuAccordFundSrcDeleteInvoker=function(parameter){
    snsoft.ft.docu.accord.invoker.DocuAccordFundSrcDeleteInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.docu.accord.invoker.DocuAccordFundSrcDeleteInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.docu.accord.invoker.DocuAccordFundSrcDeleteInvoker",
    /*snsoft.ft.docu.accord.invoker.DocuAccordFundSrcDeleteInvoker.beforeCheck*/
    beforeCheck:function(event)
    {
        if(this.dataSet.getValue("srcismanual") == "N")
        {
            return {innercode:this.mainDataSet.get(this.mainDataSet.getCuInnerfld()),sheetcode:this.mainDataSet.get("sheetcode"),fsrcicode:this.dataSet.get(this.dataSet.getCuInnerfld())};
        }
        return null;
    }
});
/*snsoft/ft/docu/accord/invoker/DocuAccordSendSerCancelInvoker.java*/
snsoft.ft.docu.accord.invoker.DocuAccordSendSerCancelInvoker=function(parameter){
    snsoft.ft.docu.accord.invoker.DocuAccordSendSerCancelInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.docu.accord.invoker.DocuAccordSendSerCancelInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.docu.accord.invoker.DocuAccordSendSerCancelInvoker",
    /*snsoft.ft.docu.accord.invoker.DocuAccordSendSerCancelInvoker.invoke*/
    invoke:function(event)
    {
        return {acdicode:this.mainDataSet.get("acdicode")};
    },
    /*snsoft.ft.docu.accord.invoker.DocuAccordSendSerCancelInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        this.mainTable.refreshTable();
    }
});
/*snsoft/ft/docu/accord/invoker/LastReceiptInvoker.java*/
snsoft.ft.docu.accord.invoker.LastReceiptInvoker=function(parameter){
    snsoft.ft.docu.accord.invoker.LastReceiptInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.docu.accord.invoker.LastReceiptInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.docu.accord.invoker.LastReceiptInvoker",
    /*snsoft.ft.docu.accord.invoker.LastReceiptInvoker.beforeCheck*/
    beforeCheck:function(event)
    {
        var rows = this.table.getSelectedRowNumbers();
        if(rows == null || rows.length == 0)
        {
            throw new Error(Xjs.ResBundle.getResVal("FT.00000048"));
        }
        for(var i=0;i < rows.length;i++)
        {
            var r = rows[i],
                status = this.dataSet.getValue("status",r),
                islastbr = this.dataSet.getValue("islastbr",r);
            if(!(status == "70"))
            {
                throw new Error(Xjs.ResBundle.getResVal("FT-DOCU.00000003"));
            }
        }
        return null;
    },
    /*snsoft.ft.docu.accord.invoker.LastReceiptInvoker.invoke*/
    invoke:function(event)
    {
        var rows = this.table.getSelectedRowNumbers(),
            srcInnerCodes = new Array(rows.length);
        for(var i=0;i < rows.length;i++)
        {
            srcInnerCodes[i] = this.dataSet.getValue(this.dataSet.getCuInnerfld(),rows[i]);
        }
        return {srcInnerCodes:srcInnerCodes};
    },
    /*snsoft.ft.docu.accord.invoker.LastReceiptInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        this.dataSet.refresh();
    }
});
/*snsoft/ft/docu/accord/invoker/NewAccOrdFinancingInvoker.java*/
snsoft.ft.docu.accord.invoker.NewAccOrdFinancingInvoker=function(parameter){
    snsoft.ft.docu.accord.invoker.NewAccOrdFinancingInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.docu.accord.invoker.NewAccOrdFinancingInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.docu.accord.invoker.NewAccOrdFinancingInvoker",
    /*snsoft.ft.docu.accord.invoker.NewAccOrdFinancingInvoker.beforeCheck*/
    beforeCheck:function(event)
    {
        window.console.log("NewAccOrdFinancingInvoker");
        var rows = this.table.getSelectedRowNumbers();
        if(rows == null || rows.length == 0)
        {
            throw new Error(Xjs.ResBundle.getResVal("FT.00000048"));
        }
        var status = this.mainDataSet.getValue("status"),
            isreferable = this.mainDataSet.getValue("isreferable"),
            paymode = this.mainDataSet.getValue("paymode"),
            isfspats = this.mainDataSet.getValue("isfspats");
        if(status < 20)
        {
            if(!(isreferable == "Y") && (paymode == "0065" || paymode == "0075"))
            {
                throw new Error(Xjs.ResBundle.getResVal("FT-DOCU.00000008"));
            }
        } else if(status == 70)
        {
            if(isfspats == "Y" && (!(paymode == "0065") || !(paymode == "0075")))
            {
                throw new Error(Xjs.ResBundle.getResVal("FT-DOCU.00000008"));
            }
        } else 
        {
            throw new Error(Xjs.ResBundle.getResVal("FT-DOCU.00000008"));
        }
        var rowCount = this.dataSet.getRowCount();
        for(var i=0;i < rows.length;i++)
        {
            var r = rows[i],
                fundSrcType = this.dataSet.getValue("fundsrctype",r),
                srcCode = this.dataSet.getValue("srccode",r),
                fcyObj = this.dataSet.getValue("fcy",r);
            if(fundSrcType == "50" || fundSrcType == "60")
            {
                if(srcCode)
                {
                    throw new Error(Xjs.ResBundle.getResVal("FT-DOCU.00000010"));
                }
                if(fcyObj == null)
                {
                    throw new Error(Xjs.ResBundle.getResVal("FT-DOCU.00000011"));
                }
            } else 
            {
                throw new Error(Xjs.ResBundle.getResVal("FT-DOCU.00000010"));
            }
        }
        if(rowCount != rows.length)
        {
            for(var i=0;i < rowCount;i++)
            {
                var checkSelectNum = this.isNumberInArray(rows,i);
                if(!checkSelectNum)
                {
                    var fundSrcType = this.dataSet.getValue("fundsrctype",i);
                    if(fundSrcType == "50" || fundSrcType == "60")
                    {
                        throw new Error(Xjs.ResBundle.getResVal("FT-DOCU.00000009"));
                    }
                }
            }
        }
        var foundFundSrcType = null;
        for(var i=0;i < rows.length;i++)
        {
            var r = rows[i],
                fundSrcType = this.dataSet.getValue("fundsrctype",r);
            if(!foundFundSrcType)
            {
                foundFundSrcType = fundSrcType;
            }
            if(!(foundFundSrcType == fundSrcType))
            {
                throw new Error(Xjs.ResBundle.getResVal("FT-DOCU.00000057"));
            }
        }
        return null;
    },
    /*snsoft.ft.docu.accord.invoker.NewAccOrdFinancingInvoker.isNumberInArray*/
    isNumberInArray:function(array,number)
    {
        for(var num=0;num < array.length;num++)
        {
            if(array[num] == number)
            {
                return true;
            }
        }
        return false;
    },
    /*snsoft.ft.docu.accord.invoker.NewAccOrdFinancingInvoker.invoke*/
    invoke:function(event)
    {
        var rows = this.table.getSelectedRowNumbers(),
            srcInnerCodes = new Array(rows.length);
        for(var i=0;i < rows.length;i++)
        {
            srcInnerCodes[i] = this.dataSet.getValue(this.dataSet.getCuInnerfld(),rows[i]);
        }
        return {srcInnerCodes:srcInnerCodes,sheetcode:this.mainDataSet.getValue("sheetcode"),innercode:this.mainDataSet.getValue(this.mainDataSet.getCuInnerfld())};
    },
    /*snsoft.ft.docu.accord.invoker.NewAccOrdFinancingInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        this.table.refreshTable();
        var beforeCheckRtnVal = event.invokeRtnVal,
            acdfaicode = beforeCheckRtnVal.acdfaicode,
            sheetService = Xjs.RInvoke.newBean("SN-Busi.SheetService"),
            busiObject = sheetService.getBusiObject("FT-LOAN.AccOrdFinaApp"),
            data = {sheetcode:busiObject.sheetcode};
        data[busiObject.innerfld] = acdfaicode;
        snsoft.plat.bas.viewdetail.ViewDetailListener.invokeViewDetail(data,busiObject.outerfld);
    }
});
/*snsoft/ft/docu/accord/invoker/NoteffectrefEditInvoker.java*/
snsoft.ft.docu.accord.invoker.NoteffectrefEditInvoker=function(parameter){
    snsoft.ft.docu.accord.invoker.NoteffectrefEditInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.docu.accord.invoker.NoteffectrefEditInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.docu.accord.invoker.NoteffectrefEditInvoker",
    /*snsoft.ft.docu.accord.invoker.NoteffectrefEditInvoker.beforeCheck*/
    beforeCheck:function(event)
    {
        var isreferable = this.mainDataSet.getValue("isreferable");
        if(isreferable == "N")
        {
            this.mainTable.checkNonBlankForSubmit();
        }
        return {innercode:this.mainDataSet.get("acdicode"),sheetcode:this.mainDataSet.get("sheetcode")};
    },
    /*snsoft.ft.docu.accord.invoker.NoteffectrefEditInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        this.mainTable.refreshTable();
    }
});
/*snsoft/ft/docu/accord/invoker/RecValidOwnerShipInvoiceInvoker.java*/
snsoft.ft.docu.accord.invoker.RecValidOwnerShipInvoiceInvoker=function(parameter){
    snsoft.ft.docu.accord.invoker.RecValidOwnerShipInvoiceInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.docu.accord.invoker.RecValidOwnerShipInvoiceInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.docu.accord.invoker.RecValidOwnerShipInvoiceInvoker",
    /*snsoft.ft.docu.accord.invoker.RecValidOwnerShipInvoiceInvoker.invoke*/
    invoke:function(event)
    {
        var rows = this.table.getSelectedRowNumbers();
        if(rows == null || rows.length == 0)
        {
            throw new Error(Xjs.ResBundle.getResVal("FT.00000048"));
        }
        var acdcodeStr = "",
            acdicodes = new Array(rows.length);
        for(var i=0;i < rows.length;i++)
        {
            var r = rows[i];
            acdicodes[i] = this.dataSet.getValue("acdicode",r);
            var status = this.dataSet.getValue("status",r);
            if(!(status == "70"))
            {
                throw new Error(Xjs.ResBundle.getResVal("FT-LC.00000001"));
            }
            var domabr = this.dataSet.getValue("domabr",r);
            if(!(domabr == "20"))
            {
                throw new Error(Xjs.ResBundle.getResVal("FT-DOCU.10000002"));
            }
            var isrecvalidst = this.dataSet.getValue("isrecvalidst",r);
            if(isrecvalidst == "Y")
            {
                var acdcode = this.dataSet.getValue("acdcode",r);
                acdcodeStr = acdcodeStr + acdcode + "、";
            }
        }
        if(!(acdcodeStr == ""))
        {
            acdcodeStr = acdcodeStr.substring(0,acdcodeStr.length - 1);
            throw new Error(String.format(Xjs.ResBundle.getResVal("FT-DOCU.10000001"),acdcodeStr));
        }
        var p = {};
        p.acdicodes = acdicodes;
        return p;
    },
    /*snsoft.ft.docu.accord.invoker.RecValidOwnerShipInvoiceInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        this.dataSet.refresh();
    }
});
/*snsoft/ft/docu/accord/invoker/SendATSHandleInvoker.java*/
snsoft.ft.docu.accord.invoker.SendATSHandleInvoker=function(parameter){
    snsoft.ft.docu.accord.invoker.SendATSHandleInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.docu.accord.invoker.SendATSHandleInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.docu.accord.invoker.SendATSHandleInvoker",
    /*snsoft.ft.docu.accord.invoker.SendATSHandleInvoker.invoke*/
    invoke:function(event)
    {
        var rows = this.table.getSelectedRowNumbers(),
            srcInnerCodes = new Array(rows.length);
        for(var i=0;i < rows.length;i++)
        {
            srcInnerCodes[i] = this.dataSet.getValue(this.dataSet.getCuInnerfld(),rows[i]);
        }
        return {srcInnerCodes:srcInnerCodes,sheetcode:this.mainDataSet.getValue("sheetcode"),innercode:this.mainDataSet.getValue(this.mainDataSet.getCuInnerfld())};
    },
    /*snsoft.ft.docu.accord.invoker.SendATSHandleInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        event.mainTable.refreshTable();
    }
});
/*snsoft/ft/docu/dlyd/invoker/DelDocuDeliverygInvoker.java*/
Xjs.namespace("snsoft.ft.docu.dlyd.invoker");
snsoft.ft.docu.dlyd.invoker.DelDocuDeliverygInvoker=function(parameter){
    snsoft.ft.docu.dlyd.invoker.DelDocuDeliverygInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.docu.dlyd.invoker.DelDocuDeliverygInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.docu.dlyd.invoker.DelDocuDeliverygInvoker",
    /*snsoft.ft.docu.dlyd.invoker.DelDocuDeliverygInvoker.check*/
    check:function(event)
    {
        return {type:0,title:this.getTitle(),prompt:Xjs.ResBundle.getResVal("FT.00000221"),perform:new Xjs.FuncCall(this.onOk,this,[event])};
    },
    /*snsoft.ft.docu.dlyd.invoker.DelDocuDeliverygInvoker.invoke*/
    invoke:function(event)
    {
        return {dlydgicodes:this.dlydgicodes};
    },
    /*snsoft.ft.docu.dlyd.invoker.DelDocuDeliverygInvoker.onOk*/
    onOk:function(event)
    {
        var rows = this.table.getSelectedRowNumbers();
        if(rows == null || rows.length == 0)
        {
            this.dlydgicodes = [this.dataSet.get("dlydgicode").toString()];
        }
        var dlydgicodes = new Array(rows.length);
        for(var i=0;i < rows.length;i++)
        {
            var r = rows[i];
            dlydgicodes[i] = this.dataSet.getValue("dlydgicode",r);
        }
    },
    /*snsoft.ft.docu.dlyd.invoker.DelDocuDeliverygInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        this.mainTable.refreshTable();
    }
});
/*snsoft/ft/docu/dlyd/invoker/DocuDeliverySendSerCancelInvoker.java*/
snsoft.ft.docu.dlyd.invoker.DocuDeliverySendSerCancelInvoker=function(parameter){
    snsoft.ft.docu.dlyd.invoker.DocuDeliverySendSerCancelInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.docu.dlyd.invoker.DocuDeliverySendSerCancelInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.docu.dlyd.invoker.DocuDeliverySendSerCancelInvoker",
    /*snsoft.ft.docu.dlyd.invoker.DocuDeliverySendSerCancelInvoker.invoke*/
    invoke:function(event)
    {
        return {dlydicode:this.mainDataSet.get("dlydicode")};
    },
    /*snsoft.ft.docu.dlyd.invoker.DocuDeliverySendSerCancelInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        this.mainTable.refreshTable();
    }
});
/*snsoft/ft/docu/dlyd/invoker/FinanceCalculateInvoker.java*/
snsoft.ft.docu.dlyd.invoker.FinanceCalculateInvoker=function(parameter){
    snsoft.ft.docu.dlyd.invoker.FinanceCalculateInvoker.superclass.constructor.call(this,parameter);
    this.type = parameter.type;
};
Xjs.extend(snsoft.ft.docu.dlyd.invoker.FinanceCalculateInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.docu.dlyd.invoker.FinanceCalculateInvoker",
    /*snsoft.ft.docu.dlyd.invoker.FinanceCalculateInvoker.check*/
    check:function(event)
    {
        var dlydicodes = "",
            param = {},
            isEntry = this.type == "entry";
        if(isEntry)
        {
            var rows = this.table.getSelectedRowNumbers();
            if(rows == null || rows.length == 0)
            {
                throw new Error(Xjs.ResBundle.getResVal("FT.00000048"));
            }
            for(var i=0;i < rows.length;i++)
            {
                var r = rows[i];
                dlydicodes = dlydicodes + this.dataSet.getValue("dlydicode",r) + ",";
            }
        }
        dlydicodes = isEntry ? dlydicodes.substring(0,dlydicodes.length - 1) : this.dataSet.getValue("dlydicode");
        var dialogQuery = {};
        dialogQuery.dlydicode = dlydicodes;
        param.dlydicode = dlydicodes;
        return this.newDialogWithFunc(event,(e)=>{
            var initVals = {query:dialogQuery},
                dialogPane = Xjs.ui.UIUtil.loadDialog("FT-DOCU.FinanceCalculateDlg",0,null,{width:1200,height:450},null,initVals);
            dialogPane.showModal();
            return dialogPane;
        },(e,d)=>{
            var fTable = d.getItemByName("financeCalculate");
            Xjs.util.TableUtils.checkNonBlankForSubmitSelectRows(fTable);
            var fDataSet = fTable.getDataSet(),
                columns = fDataSet.columns,
                selectedRows = fTable.getSelectedRowNumbers();
            fTable.dataSet.postRow();
            if(isEntry && (selectedRows == null || selectedRows.length == 0))
            {
                throw new Error(Xjs.ResBundle.getResVal("FT.00000048"));
            }
            var length = isEntry ? selectedRows.length : 1,
                results = new Array(length);
            for(var i=0;i < length;i++)
            {
                var valueMap = {};
                for(var column of columns)
                {
                    var currentRowNum = isEntry ? selectedRows[i] : i,
                        columnValue = fDataSet.getValue(column.name,currentRowNum);
                    valueMap[column.name] = columnValue;
                }
                results[i] = valueMap;
            }
            param.results = results;
            event.checkData.param = param;
        });
    },
    /*snsoft.ft.docu.dlyd.invoker.FinanceCalculateInvoker.invoke*/
    invoke:function(event)
    {
        return event.checkData.param;
    },
    /*snsoft.ft.docu.dlyd.invoker.FinanceCalculateInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        this.mainTable.refreshTable();
    }
});
/*snsoft/ft/docu/dlyd/invoker/ModifyBankacccodeInvoker.java*/
snsoft.ft.docu.dlyd.invoker.ModifyBankacccodeInvoker=function(parameter){
    snsoft.ft.docu.dlyd.invoker.ModifyBankacccodeInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.docu.dlyd.invoker.ModifyBankacccodeInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.docu.dlyd.invoker.ModifyBankacccodeInvoker",
    /*snsoft.ft.docu.dlyd.invoker.ModifyBankacccodeInvoker.check*/
    check:function(event)
    {
        return this.newDialogWithFunc(event,(e)=>{
            var dialogPane = Xjs.ui.UIUtil.loadDialog("FT-DOCU.ModifyBankacccodeDlg",0,null,{width:325,height:120},null,null);
            dialogPane.title = this.cfg.title;
            var bankTable = dialogPane.getItemByName("modifyBankacccode");
            bankTable.dataSet.insertRow(3);
            bankTable.dataSet.setValue("bankacccode",this.mainDataSet.getValue("bankacccode"));
            bankTable.dataSet.setValue("fcode",this.mainDataSet.getValue("fcode"));
            bankTable.dataSet.setValue("corpbcode",this.mainDataSet.getValue("corpbcode"));
            dialogPane.showModal();
            return dialogPane;
        },(e,d)=>{
            var param = {};
            param.bankacccode = d.getItemByName("bankacccode").getValue();
            event.checkData.param = param;
        });
    },
    /*snsoft.ft.docu.dlyd.invoker.ModifyBankacccodeInvoker.invoke*/
    invoke:function(event)
    {
        return event.checkData.param;
    },
    /*snsoft.ft.docu.dlyd.invoker.ModifyBankacccodeInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        this.mainTable.refreshTable();
    }
});
/*snsoft/ft/docu/dlyd/invoker/OpenDocuDeliveryFinaInvoker.java*/
snsoft.ft.docu.dlyd.invoker.OpenDocuDeliveryFinaInvoker=function(parameter){
    snsoft.ft.docu.dlyd.invoker.OpenDocuDeliveryFinaInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.docu.dlyd.invoker.OpenDocuDeliveryFinaInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.docu.dlyd.invoker.OpenDocuDeliveryFinaInvoker",
    /*snsoft.ft.docu.dlyd.invoker.OpenDocuDeliveryFinaInvoker.invoke*/
    invoke:function(event)
    {
        return {dlydicode:this.mainDataSet.get("dlydicode")};
    },
    /*snsoft.ft.docu.dlyd.invoker.OpenDocuDeliveryFinaInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        var invokeRtnVal = event.invokeRtnVal,
            dlydfaicodes = invokeRtnVal.dlydfaicodes;
        if(!dlydfaicodes)
        {
            return;
        }
        var sheetService = Xjs.RInvoke.newBean("SN-Busi.SheetService"),
            busiObject = sheetService.getBusiObject("FT-LOAN.DocuDeliveryFinaApp"),
            data = {sheetcode:busiObject.sheetcode};
        data[busiObject.innerfld] = dlydfaicodes;
        snsoft.plat.bas.viewdetail.ViewDetailListener.invokeViewDetail(data,busiObject.outerfld);
    }
});
/*snsoft/ft/docu/dlyd/invoker/SignForDeliveryInvoker.java*/
snsoft.ft.docu.dlyd.invoker.SignForDeliveryInvoker=function(parameter){
    snsoft.ft.docu.dlyd.invoker.SignForDeliveryInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.docu.dlyd.invoker.SignForDeliveryInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.docu.dlyd.invoker.SignForDeliveryInvoker",
    /*snsoft.ft.docu.dlyd.invoker.SignForDeliveryInvoker.check*/
    check:function(event)
    {
        var rows = this.table.getSelectedRowNumbers();
        if(rows == null || rows.length == 0)
        {
            throw new Error(Xjs.ResBundle.getResVal("FT.00000048"));
        }
        var validateStr = "",
            dlydicodes = new Array(rows.length),
            initValue = {};
        for(var i=0;i < rows.length;i++)
        {
            var r = rows[i];
            dlydicodes[i] = this.dataSet.getValue("dlydicode",r);
            var senddate = this.dataSet.getValue("senddate",r);
            window.console.log(senddate);
            if(senddate)
            {
                senddate = new Date(senddate.getFullYear(),senddate.getMonth(),senddate.getDate());
            }
            var signdate = this.dataSet.getValue("signdate",r);
            if(signdate)
            {
                signdate = new Date(signdate.getFullYear(),signdate.getMonth(),signdate.getDate());
            }
            var signstatus = this.dataSet.getValue("signstatus",r),
                expresscode = this.dataSet.getValue("expresscode",r),
                cscompany = this.dataSet.getValue("cscompany",r),
                currentSignForDeliveryStr = String.obj2str(senddate,"") + String.obj2str(signdate,"") + signstatus + expresscode + cscompany;
            if(i == 0)
            {
                initValue.senddate = senddate;
                initValue.signdate = signdate;
                initValue.signstatus = signstatus;
                initValue.expresscode = expresscode;
                initValue.cscompany = cscompany;
                validateStr = currentSignForDeliveryStr;
            } else 
            {
                if(!(currentSignForDeliveryStr == validateStr))
                {
                    throw new Error(Xjs.ResBundle.getResVal("FT-DOCU.10000014"));
                }
            }
        }
        return this.newDialogWithFunc(event,(e)=>{
            var dialogPane = Xjs.ui.UIUtil.loadDialog("FT-DOCU.SignForDeliveryDlg",0,null,{width:425,height:210},null,null);
            dialogPane.title = this.cfg.title;
            var sfdTbale = dialogPane.getItemByName("signfordelivery");
            sfdTbale.dataSet.insertRow(3);
            sfdTbale.dataSet.setValue("senddate",initValue.senddate);
            sfdTbale.dataSet.setValue("signdate",initValue.signdate);
            sfdTbale.dataSet.setValue("signstatus",initValue.signstatus);
            sfdTbale.dataSet.setValue("expresscode",initValue.expresscode);
            sfdTbale.dataSet.setValue("cscompany",initValue.cscompany);
            dialogPane.showModal();
            return dialogPane;
        },(e,d)=>{
            var param = {};
            param.dlydicodes = dlydicodes;
            param.senddate = d.getItemByName("senddate").getValue();
            param.signdate = d.getItemByName("signdate").getValue();
            param.signstatus = d.getItemByName("signstatus").getValue();
            param.expresscode = d.getItemByName("expresscode").getValue();
            param.cscompany = d.getItemByName("cscompany").getValue();
            event.checkData.param = param;
        });
    },
    /*snsoft.ft.docu.dlyd.invoker.SignForDeliveryInvoker.invoke*/
    invoke:function(event)
    {
        return event.checkData.param;
    },
    /*snsoft.ft.docu.dlyd.invoker.SignForDeliveryInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        this.mainTable.refreshTable();
    }
});
/*snsoft/ft/docu/dlyd/invoker/SyncRecfcyInvoker.java*/
snsoft.ft.docu.dlyd.invoker.SyncRecfcyInvoker=function(parameter){
    snsoft.ft.docu.dlyd.invoker.SyncRecfcyInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.docu.dlyd.invoker.SyncRecfcyInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.docu.dlyd.invoker.SyncRecfcyInvoker",
    /*snsoft.ft.docu.dlyd.invoker.SyncRecfcyInvoker.invoke*/
    invoke:function(event)
    {
        return {dlydicode:this.mainDataSet.get("dlydicode")};
    },
    /*snsoft.ft.docu.dlyd.invoker.SyncRecfcyInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        this.mainTable.refreshTable();
    }
});
/*snsoft/ft/docu/dlyd/lis/DocuDeliveryDetailJSListener.java*/
Xjs.namespace("snsoft.ft.docu.dlyd.lis");
snsoft.ft.docu.dlyd.lis.DocuDeliveryDetailJSListener=function(params){
    snsoft.ft.docu.dlyd.lis.DocuDeliveryDetailJSListener.superclass.constructor.call(this,params);
};
Xjs.extend(snsoft.ft.docu.dlyd.lis.DocuDeliveryDetailJSListener,snsoft.plat.bas.busi.SystemFunctionListener,{
  _js$className_:"snsoft.ft.docu.dlyd.lis.DocuDeliveryDetailJSListener",
    /*snsoft.ft.docu.dlyd.lis.DocuDeliveryDetailJSListener.initComponent*/
    initComponent:function(table,values)
    {
        snsoft.ft.docu.dlyd.lis.DocuDeliveryDetailJSListener.superclass.initComponent.call(this,table,values);
        this.mainTable = table;
        if(!this.dstTable)
        {
            this.dstTable = this.getTable(table,"ft_docu_dlyds");
        }
        if(!this.basTable)
        {
            this.basTable = this.getTable(table,"ft_docu_dlyd_bas");
        }
    },
    /*snsoft.ft.docu.dlyd.lis.DocuDeliveryDetailJSListener.dataSetRowNavigated*/
    dataSetRowNavigated:function(dataSet,e)
    {
        snsoft.ft.docu.dlyd.lis.DocuDeliveryDetailJSListener.superclass.dataSetRowNavigated.call(this,dataSet,e);
        var gDateSet = this.dstTable.getDataSet();
        gDateSet.ensureOpened();
        this.mainTable.getDataSet().ensureOpened();
        var paymodelist = this.mainTable.getDataSet().getValue("paymodelist"),
            isExistDPorDA = String.isStrIn(paymodelist,"0025",",") || String.isStrIn(paymodelist,"0030",","),
            isExistNotTT = paymodelist!="0020";
        if(!gDateSet.getRows())
        {
            return;
        }
        var bankacccode = this.basTable.getColumn("bankacccode"),
            status = this.mainTable.getValue("status");
        if(isExistNotTT)
        {
            bankacccode.nonBlankOnSubmit = true;
            bankacccode.setReadonly(false);
            bankacccode.ignoreTblRdonly = true;
        } else 
        {
            this.basTable.getDataSet().setValue("bankacccode",null);
            bankacccode.nonBlankOnSubmit = false;
            bankacccode.setReadonly(true);
            bankacccode.ignoreTblRdonly = false;
        }
        var iccode = this.basTable.getColumn("iccode");
        if(isExistDPorDA)
        {
            iccode.nonBlankOnSubmit = true;
            iccode.setReadonly(false);
            iccode.ignoreTblRdonly = true;
        } else 
        {
            this.basTable.getDataSet().setValue("iccode",null);
            iccode.nonBlankOnSubmit = false;
            iccode.setReadonly(true);
            iccode.ignoreTblRdonly = false;
        }
        this.basTable.render(2);
    }
});
/*snsoft/ft/docu/dlyd/lis/FinanceCalculateJSListener.java*/
snsoft.ft.docu.dlyd.lis.FinanceCalculateJSListener=function(params){
    snsoft.ft.docu.dlyd.lis.FinanceCalculateJSListener.superclass.constructor.call(this,params);
};
Xjs.extend(snsoft.ft.docu.dlyd.lis.FinanceCalculateJSListener,snsoft.plat.bas.busi.SystemFunctionListener,{
  _js$className_:"snsoft.ft.docu.dlyd.lis.FinanceCalculateJSListener",
    /*snsoft.ft.docu.dlyd.lis.FinanceCalculateJSListener.initComponent*/
    initComponent:function(table,values)
    {
        snsoft.ft.docu.dlyd.lis.FinanceCalculateJSListener.superclass.initComponent.call(this,table,values);
        this.mainTable = table;
    },
    /*snsoft.ft.docu.dlyd.lis.FinanceCalculateJSListener.dataSetRowNavigated*/
    dataSetRowNavigated:function(dataSet,e)
    {
        snsoft.ft.docu.dlyd.lis.FinanceCalculateJSListener.superclass.dataSetRowNavigated.call(this,dataSet,e);
    },
    /*snsoft.ft.docu.dlyd.lis.FinanceCalculateJSListener.dataLoaded*/
    dataLoaded:function(dataSet,e)
    {
        snsoft.ft.docu.dlyd.lis.FinanceCalculateJSListener.superclass.dataLoaded.call(this,dataSet,e);
        dataSet.ensureOpened();
        var caldate = dataSet.get("caldate"),
            dlydicode = dataSet.get("dlydicode"),
            rate = Xjs.RInvoke.rmInvoke("snsoft.ft.docu.dlyd.service.impl.DocuDeliveryServiceImpl.getIrtRate",dlydicode,caldate);
        for(var row=0;row < dataSet.getRowCount();row++)
        {
            dataSet.gotoRow(row);
            var rdate = dataSet.get("rdate");
            if(caldate && rdate)
            {
                var diffDays = snsoft.ft.docu.dlyd.lis.FinanceCalculateJSListener.getDiffDay(dataSet);
                dataSet.setValue("findays",diffDays);
            }
            if(rate)
            {
                dataSet.setValue("loanrate",rate);
            }
        }
    },
    /*snsoft.ft.docu.dlyd.lis.FinanceCalculateJSListener.dataSetFieldPosting*/
    dataSetFieldPosting:function(dataSet,event)
    {
        snsoft.ft.docu.dlyd.lis.FinanceCalculateJSListener.superclass.dataSetFieldPosting.call(this,dataSet,event);
        if(event.columnName == "caldate")
        {
            var rdate = dataSet.getValue("rdate"),
                caldate = event.value;
            if(caldate > rdate)
            {
                throw new Error(Xjs.ResBundle.getResVal("FT-DOCU.10000017"));
            }
        }
    },
    /*snsoft.ft.docu.dlyd.lis.FinanceCalculateJSListener.dataSetFieldPosted*/
    dataSetFieldPosted:function(dataSet,event)
    {
        snsoft.ft.docu.dlyd.lis.FinanceCalculateJSListener.superclass.dataSetFieldPosted.call(this,dataSet,event);
        var caldate = dataSet.getValue("caldate");
        if(caldate && event.columnName == "caldate")
        {
            var dlydicode = dataSet.get("dlydicode"),
                rate = Xjs.RInvoke.rmInvoke("snsoft.ft.docu.dlyd.service.impl.DocuDeliveryServiceImpl.getIrtRate",dlydicode,caldate);
            dataSet.setValue("loanrate",rate);
        }
        var diffDays = 0,
            rdate = dataSet.getValue("rdate");
        if(caldate && rdate)
        {
            diffDays = snsoft.ft.docu.dlyd.lis.FinanceCalculateJSListener.getDiffDay(dataSet);
            dataSet.setValue("findays",diffDays);
        }
        var forfrate = dataSet.getValue("forfrate"),
            tfloat = dataSet.getValue("tfloat"),
            dayrate = 0,
            dayratescale = this.mainTable.getColumn("dayrate").maxDecimals + 2;
        if(!(forfrate == null) && !(tfloat == null))
        {
            dayrate = this.round(snsoft.ft.utils.FTUtils.div(forfrate + tfloat,360),dayratescale);
            dataSet.setValue("dayrate",dayrate);
        }
        var fcy = dataSet.getValue("fcy");
        if(!(fcy == null) && !(diffDays == 0) && !(dayrate == 0))
        {
            var minstdfcy = dataSet.getValue("minstdfcy"),
                tempfee = this.round(snsoft.ft.utils.FTUtils.mul(snsoft.ft.utils.FTUtils.mul(fcy,diffDays),dayrate),2),
                diffee = snsoft.ft.utils.FTUtils.sub(tempfee,minstdfcy);
            dataSet.setValue("forffcy",diffee > 0 ? tempfee : minstdfcy);
        }
        var loanrate = dataSet.getValue("loanrate"),
            interestfcy = 0;
        if(!(fcy == null) && !(diffDays == 0) && !(loanrate == null))
        {
            var tempfee = snsoft.ft.utils.FTUtils.mul(snsoft.ft.utils.FTUtils.mul(fcy,diffDays),loanrate);
            interestfcy = this.round(snsoft.ft.utils.FTUtils.div(tempfee,360),2);
            dataSet.setValue("interestfcy",interestfcy);
        }
        var forffcy = dataSet.getValue("forffcy");
        if(!(interestfcy == 0) && !(forffcy == null))
        {
            dataSet.setValue("difffcy",this.round(snsoft.ft.utils.FTUtils.sub(interestfcy,forffcy),2));
        }
    }
});
Xjs.apply(snsoft.ft.docu.dlyd.lis.FinanceCalculateJSListener,{
    /*snsoft.ft.docu.dlyd.lis.FinanceCalculateJSListener.getDiffDay*/
    getDiffDay:function(dataSet)
    {
        var rdate = dataSet.getValue("rdate"),
            caldate = dataSet.getValue("caldate");
        rdate = new Date(rdate.getFullYear(),rdate.getMonth(),rdate.getDate());
        caldate = new Date(caldate.getFullYear(),caldate.getMonth(),caldate.getDate());
        var diff = (rdate.getTime() - caldate.getTime()) / (24 * 60 * 60 * 1000);
        return diff;
    }
});
/*snsoft/ft/docu/dlyd/lis/ModifyBankacccodeMatchValue.java*/
snsoft.ft.docu.dlyd.lis.ModifyBankacccodeMatchValue=function(config){
    snsoft.ft.docu.dlyd.lis.ModifyBankacccodeMatchValue.superclass.constructor.call(this,config);
};
Xjs.extend(snsoft.ft.docu.dlyd.lis.ModifyBankacccodeMatchValue,Xjs.table.sample.TableOptsCtrlListener$MatchValue,{
  _js$className_:"snsoft.ft.docu.dlyd.lis.ModifyBankacccodeMatchValue",
    /*snsoft.ft.docu.dlyd.lis.ModifyBankacccodeMatchValue.matchColumn*/
    matchColumn:Xjs.trueFn,
    /*snsoft.ft.docu.dlyd.lis.ModifyBankacccodeMatchValue.match*/
    match:function(value,row)
    {
        var paymodelist = this.table.getRootTable().dataSet.getValue("paymodelist");
        return paymodelist!="0020";
    }
});
