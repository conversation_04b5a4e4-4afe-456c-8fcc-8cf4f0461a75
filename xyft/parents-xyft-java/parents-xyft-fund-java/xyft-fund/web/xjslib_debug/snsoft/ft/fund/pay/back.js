Xjs.loadedXjs.push("snsoft/ft/fund/pay/back");
/*snsoft/ft/pay/back/invoker/afterBackCopyInvoker.java*/
Xjs.namespace("snsoft.ft.pay.back.invoker");
snsoft.ft.pay.back.invoker.afterBackCopyInvoker=function(parameter){
    snsoft.ft.pay.back.invoker.afterBackCopyInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.pay.back.invoker.afterBackCopyInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.pay.back.invoker.afterBackCopyInvoker",
    /*snsoft.ft.pay.back.invoker.afterBackCopyInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        event.mainTable.refreshTable();
    }
});
/*snsoft/ft/pay/back/invoker/BackPayAppPushFundBatchInvoker.java*/
snsoft.ft.pay.back.invoker.BackPayAppPushFundBatchInvoker=function(parameter){
    snsoft.ft.pay.back.invoker.BackPayAppPushFundBatchInvoker.superclass.constructor.call(this,parameter);
    this.isEntry = String.obj2bool(parameter.isEntry,false);
};
Xjs.extend(snsoft.ft.pay.back.invoker.BackPayAppPushFundBatchInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.pay.back.invoker.BackPayAppPushFundBatchInvoker",
    /*snsoft.ft.pay.back.invoker.BackPayAppPushFundBatchInvoker.beforeCheck*/
    beforeCheck:function(event)
    {
        if(this.isEntry)
        {
            var rows = this.table.getSelectedRowNumbers();
            if(rows == null || rows.length == 0)
            {
                throw new Error(Xjs.ResBundle.getResVal("FT.00000048"));
            }
            for(var i=0;i < rows.length;i++)
            {
                var r = rows[i],
                    status = this.dataSet.getValue("status",r),
                    isneedpfund = this.dataSet.getValue("isneedpfund",r);
                if(!(status == "60" || status == "59"))
                {
                    throw new Error(Xjs.ResBundle.getResVal("FT-PAY.00000088"));
                }
                if(isneedpfund == "N")
                {
                    throw new Error(Xjs.ResBundle.getResVal("FT-PAY.00000015"));
                }
            }
        } else 
        {
            var status = this.dataSet.getValue("status"),
                isneedpfund = this.dataSet.getValue("isneedpfund"),
                isautopfund = this.dataSet.getValue("isautopfund");
            if(!(status == "60" || status == "59"))
            {
                throw new Error(Xjs.ResBundle.getResVal("FT-PAY.00000089"));
            }
            if(isneedpfund == "N")
            {
                throw new Error(Xjs.ResBundle.getResVal("FT-PAY.00000090"));
            }
        }
        return null;
    },
    /*snsoft.ft.pay.back.invoker.BackPayAppPushFundBatchInvoker.invoke*/
    invoke:function(event)
    {
        var cmd = event.cfg.cmd;
        if(cmd=="pushFund" || cmd=="pushFundBatch")
        {
            this.oncmd_pushFundBatch(this.table,event);
        }
        return null;
    },
    /*snsoft.ft.pay.back.invoker.BackPayAppPushFundBatchInvoker.oncmd_pushFundBatch*/
    oncmd_pushFundBatch:function(table,event)
    {
        var paicodes,
            mv = {},
            sheetcode = this.dataSet.getValue("sheetcode");
        mv.innercode = this.dataSet.getValue("paicode");
        mv.sheetcode = sheetcode;
        mv.modifydate = this.dataSet.getValue(String.obj2str(this.dataSet.modifyDateColumn,"modifydate"));
        if(this.isEntry)
        {
            paicodes = Xjs.util.TableUtils.getSelectedColumnValues(table,"paicode",true);
        } else 
        {
            paicodes = [this.mainDataSet.getValue("paicode")];
        }
        var service = Xjs.RInvoke.newBean("FT-PAY.PayAppUIService");
        service.pushFund(paicodes,mv,true);
        var dlgTitle = snsoft.ft.utils.FTUtils.getBtnTitle(table,event.cfg.cmd);
        Xjs.ui.UIUtil.showConfirmDialog(dlgTitle,Xjs.ResBundle.getResVal("C001",dlgTitle),new Xjs.FuncCall(this.pushFundBatch,this,[paicodes,mv]),null);
    },
    /*snsoft.ft.pay.back.invoker.BackPayAppPushFundBatchInvoker.pushFundBatch*/
    pushFundBatch:function(dialogPane,cmd,paicodes,mv)
    {
        if("ok"==cmd)
        {
            var service = Xjs.RInvoke.newBean("FT-PAY.PayAppUIService");
            service.pushFund(paicodes,mv,false);
            this.table.refreshTable();
        }
    }
});
/*snsoft/ft/pay/back/invoker/ClientRefundInvoker.java*/
snsoft.ft.pay.back.invoker.ClientRefundInvoker=function(parameter){
    snsoft.ft.pay.back.invoker.ClientRefundInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.pay.back.invoker.ClientRefundInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.pay.back.invoker.ClientRefundInvoker",
    /*snsoft.ft.pay.back.invoker.ClientRefundInvoker.check*/
    check:function(event)
    {
        if(this.mainDataSet.getValue("ccode") == null)
        {
            throw new Error(Xjs.ResBundle.getResVal("FT-PAY.00000043"));
        }
        return null;
    }
});
/*snsoft/ft/pay/back/lis/BackRefundJSListener.java*/
Xjs.namespace("snsoft.ft.pay.back.lis");
snsoft.ft.pay.back.lis.BackRefundJSListener=function(params){
    snsoft.ft.pay.back.lis.BackRefundJSListener.superclass.constructor.call(this,params);
};
Xjs.extend(snsoft.ft.pay.back.lis.BackRefundJSListener,snsoft.plat.bas.busi.SystemFunctionListener,{
  _js$className_:"snsoft.ft.pay.back.lis.BackRefundJSListener",
    /*snsoft.ft.pay.back.lis.BackRefundJSListener.dataSetFieldPosted*/
    dataSetFieldPosted:function(dataSet,event)
    {
        if(event.columnName == "curfcy")
        {
            var curfcy = dataSet.getValue(event.columnName),
                fcying = dataSet.getValue("fcying");
            if(curfcy > fcying)
            {
                throw new Error(Xjs.ResBundle.getResVal("FT-PAY.00000049"));
            }
        }
    },
    /*snsoft.ft.pay.back.lis.BackRefundJSListener.dataSetRowSelected*/
    dataSetRowSelected:function(dataSet,e)
    {
        dataSet.gotoRow(e.row);
        var table = dataSet.getTables()[0],
            fcying = table.getValue("fcying",e.row);
        dataSet.setValue("curfcy",fcying);
    }
});
