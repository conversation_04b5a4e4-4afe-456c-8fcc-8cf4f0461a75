Xjs.loadedXjs.push("snsoft/ft/fund/forex/moa");
/*snsoft/ft/forex/moa/invoker/DeleteForexMoveAppInvoker.java*/
Xjs.namespace("snsoft.ft.forex.moa.invoker");
snsoft.ft.forex.moa.invoker.DeleteForexMoveAppInvoker=function(parameter){
    snsoft.ft.forex.moa.invoker.DeleteForexMoveAppInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.forex.moa.invoker.DeleteForexMoveAppInvoker,snsoft.ext.cmd.com.biz.CommandDelete,{
  _js$className_:"snsoft.ft.forex.moa.invoker.DeleteForexMoveAppInvoker",
    /*snsoft.ft.forex.moa.invoker.DeleteForexMoveAppInvoker.beforeInvoke*/
    beforeInvoke:function(event)
    {
        var ds = this.mainDataSet;
        return ds.getKeyValues();
    }
});
/*snsoft/ft/forex/moa/invoker/ForexMoveAppPushFundInvoker.java*/
snsoft.ft.forex.moa.invoker.ForexMoveAppPushFundInvoker=function(parameter){
    snsoft.ft.forex.moa.invoker.ForexMoveAppPushFundInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.forex.moa.invoker.ForexMoveAppPushFundInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.forex.moa.invoker.ForexMoveAppPushFundInvoker",
    /*snsoft.ft.forex.moa.invoker.ForexMoveAppPushFundInvoker.invoke*/
    invoke:function(event)
    {
        var invokeParam = this.buildInvokeParam(null);
        return invokeParam;
    },
    /*snsoft.ft.forex.moa.invoker.ForexMoveAppPushFundInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        this.mainTable.refreshTableIfOK();
    }
});
