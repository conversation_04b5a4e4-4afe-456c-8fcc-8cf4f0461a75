#!/bin/bash

# 检查指定作者在分支中的所有提交
AUTHOR="池启苹"

echo "=========================================="
echo "检查作者提交记录: $AUTHOR"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "\n${YELLOW}=== $AUTHOR 在 origin/phase2 分支的所有提交 ===${NC}"
phase2_all=$(git log origin/phase2 --author="$AUTHOR" --no-merges --pretty=format:"%h - %an, %ad : %s" --date=format:'%Y-%m-%d %H:%M:%S')
if [ -n "$phase2_all" ]; then
    echo "$phase2_all"
    echo -e "\n${BLUE}提交数量: $(echo "$phase2_all" | wc -l)${NC}"
else
    echo "  无提交记录"
fi

echo -e "\n${YELLOW}=== $AUTHOR 在 origin/master 分支的所有提交 ===${NC}"
master_all=$(git log origin/master --author="$AUTHOR" --no-merges --pretty=format:"%h - %an, %ad : %s" --date=format:'%Y-%m-%d %H:%M:%S')
if [ -n "$master_all" ]; then
    echo "$master_all"
    echo -e "\n${BLUE}提交数量: $(echo "$master_all" | wc -l)${NC}"
else
    echo "  无提交记录"
fi

# 检查是否有相同的提交但哈希不同
echo -e "\n${YELLOW}=== 检查相同内容但不同哈希的提交 ===${NC}"
if [ -n "$phase2_all" ] && [ -n "$master_all" ]; then
    echo -e "${BLUE}比较两个分支中的提交内容...${NC}"
    
    # 提取提交主题进行比较
    phase2_subjects=$(git log origin/phase2 --author="$AUTHOR" --no-merges --pretty=format:"%s")
    master_subjects=$(git log origin/master --author="$AUTHOR" --no-merges --pretty=format:"%s")
    
    echo -e "\n${GREEN}Phase2 分支中的提交主题:${NC}"
    echo "$phase2_subjects" | nl
    
    echo -e "\n${GREEN}Master 分支中的提交主题:${NC}"
    echo "$master_subjects" | nl
    
    # 查找共同的提交主题
    echo -e "\n${YELLOW}共同的提交主题:${NC}"
    comm -12 <(echo "$phase2_subjects" | sort) <(echo "$master_subjects" | sort) | nl
    
elif [ -n "$phase2_all" ] || [ -n "$master_all" ]; then
    echo -e "${YELLOW}只有一个分支包含该作者的提交${NC}"
else
    echo -e "${RED}两个分支都没有该作者的提交记录${NC}"
fi

# 检查作者名称的变体
echo -e "\n${YELLOW}=== 检查作者名称的可能变体 ===${NC}"
echo -e "${BLUE}搜索包含 '池' 或 '启' 或 '苹' 的作者...${NC}"

echo -e "\n${GREEN}Phase2 分支中包含相关字符的作者:${NC}"
git log origin/phase2 --pretty=format:"%an" | grep -E '[池启苹]' | sort | uniq || echo "  无匹配作者"

echo -e "\n${GREEN}Master 分支中包含相关字符的作者:${NC}"
git log origin/master --pretty=format:"%an" | grep -E '[池启苹]' | sort | uniq || echo "  无匹配作者"

# 列出所有作者供参考
echo -e "\n${YELLOW}=== 参考：所有作者列表 (最近50个提交) ===${NC}"
echo -e "${GREEN}Phase2 分支的作者:${NC}"
git log origin/phase2 -50 --pretty=format:"%an" | sort | uniq

echo -e "\n${GREEN}Master 分支的作者:${NC}"
git log origin/master -50 --pretty=format:"%an" | sort | uniq