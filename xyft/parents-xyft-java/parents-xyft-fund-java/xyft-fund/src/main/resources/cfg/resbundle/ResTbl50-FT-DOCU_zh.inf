# 承兑赎单主表
ft_docu_acd=承兑赎单
ft_docu_acd.acdicode=承兑赎单内码
ft_docu_acd.acdcode=承兑单号
ft_docu_acd.status=${status}
ft_docu_acd.wfcode=审批编码
ft_docu_acd.wfuid=${wfuid}
ft_docu_acd.bcode=${b_wcode}
ft_docu_acd.wcode=${wcode}
ft_docu_acd.corpbcode=${corpbcode}
ft_docu_acd.sheetcode=${sheetcode}
ft_docu_acd.cuicode=${cuicode}
ft_docu_acd.ratifydate=${ratifydate}
ft_docu_acd.submitdate=${submitdate}
ft_docu_acd.performdate=审核时间
ft_docu_acd.vprepare=${vprepare}
ft_docu_acd.predate=${predate}
ft_docu_acd.modifier=${modifier}
ft_docu_acd.modifydate=${modifydate}
ft_docu_acd.ismerge=是否合并赎单
ft_docu_acd.mergebill=合并赎单
ft_docu_acd.adate=${adate}
ft_docu_acd.paymode=${paymode}
ft_docu_acd.lccode=${lccode}
ft_docu_acd.paicode=付款申请单内码
ft_docu_acd.pacode=${pacode}
ft_docu_acd.issecond=二次承兑
ft_docu_acd.fcode=${fcode}
ft_docu_acd.sfcode=${sfcode}
ft_docu_acd.fcy=付款申请金额
ft_docu_acd.isautogeninv=自动生成发票
ft_docu_acd.isnotrare=是否免审
ft_docu_acd.isdocmatch=是否单证相符
ft_docu_acd.invaliddesc=不符点描述
ft_docu_acd.acdate=承兑日期
ft_docu_acd.romode=赎单方式
ft_docu_acd.pfbankcode=付汇银行
ft_docu_acd.pfdate=付汇日期
ft_docu_acd.iccode=IC编号
ft_docu_acd.brdate=到单日
ft_docu_acd.purccode=${purcode}
ft_docu_acd.beneccode=${beneccode}
ft_docu_acd.lastredate=最迟赎单日
ft_docu_acd.islastbr=是否最后到单
ft_docu_acd.isrecvalidst=是否收到有效货权单据
ft_docu_acd.remark=${remark}
ft_docu_acd.isfspats=是否资金来源发送ATS
ft_docu_acd.isreferable=未生效时可引用
ft_docu_acd.tcaptime=事务处理日期
ft_docu_acd.prjcode=采购业务编号
ft_docu_acd.outbolcode=外部提单号
ft_docu_acd.purshipcoder=到货单号
ft_docu_acd.adateto=${dateto}
ft_docu_acd.acdatefm=承兑日期从
ft_docu_acd.acdateto=${dateto}
ft_docu_acd.pfdatefm=付汇日期从
ft_docu_acd.pfdateto=${dateto}
ft_docu_acd.cfcy=${oplcfcy}
ft_docu_acd.vprepare1=申请人
ft_docu_acd.ordcorpbcode=合同公司
ft_docu_acd.lcaicode=信用证开证申请内码
payapp.postscript=货款
ft_docu_acd.isinit=是否初始化

# 承兑赎单明细表
ft_docu_acdg=承兑赎单明细
ft_docu_acdg.acdgicode=承兑明细内码
ft_docu_acdg.acdicode=承兑赎单内码
ft_docu_acdg.purshipssicoder=采购到货单付款方式分摊内码
ft_docu_acdg.idx=${idx}
ft_docu_acdg.purshipicoder=到货单内码
ft_docu_acdg.purshipcoder=到货单号
ft_docu_acdg.outbolcode=外部提单号
ft_docu_acdg.purordicode=采购合同号内码
ft_docu_acdg.purordcode=${purordcode}
ft_docu_acdg.prjcode=采购业务编号
ft_docu_acdg.prjicode=业务内码
ft_docu_acdg.purshipfcy=${fcy}
ft_docu_acdg.feeproperty=费用性质
ft_docu_acdg.fcy=付款申请金额
ft_docu_acdg.eta=预计到港日期
ft_docu_acdg.loadingport=${loadingport}
ft_docu_acdg.dischargeport=${dischargeport}


# 资金来源表
ft_docu_fsrc=${fstype}
ft_docu_fsrc.fsrcicode=资金来源内码
ft_docu_fsrc.acdicode=承兑赎单内码
ft_docu_fsrc.fundsrctype=${fstype}
ft_docu_fsrc.bcode=${bcode}
ft_docu_fsrc.callbcode=现汇调用部门
ft_docu_fsrc.fcy=金额
ft_docu_fsrc.srccode=${srccode}
ft_docu_fsrc.srcsheetcode=${srcsheetcode}
ft_docu_fsrc.srcicode=${srcicode}
ft_docu_fsrc.srcismanual=来源单据是否手工创建



# 到货单对承兑赎单明细核销余额表
ft_docu_psacd_cb=到货单对承兑赎单明细核销余额表
ft_docu_psacd_cb.purshipssicoder=采购到货单付款方式分摊内码
ft_docu_psacd_cb.corflag=标识
ft_docu_psacd_cb.fcy=${planexecutefcy}
ft_docu_psacd_cb.fcyed=${executefcy}
ft_docu_psacd_cb.fcying=${unexecfcy}
ft_docu_psacd_cb.purshipicoder=到货单内码
ft_docu_psacd_cb.purshipcoder=到货单号
ft_docu_psacd_cb.outbolcode=外部提单号
ft_docu_psacd_cb.purordicode=采购合同号内码
ft_docu_psacd_cb.purordcode=${purordcode}
ft_docu_psacd_cb.prjcode=采购业务编号
ft_docu_psacd_cb.prjicode=采购业务内码
ft_docu_psacd_cb.purccode=${purcode}
ft_docu_psacd_cb.paymode=${paymode}
ft_docu_psacd_cb.lccode=${lccode}
ft_docu_psacd_cb.lctype=${olctype}
ft_docu_psacd_cb.outordcode=外部合同号
ft_docu_psacd_cb.fcode=${fcode}
ft_docu_psacd_cb.eta=预计到港日期
ft_docu_psacd_cb.loadingport=${loadingport}
ft_docu_psacd_cb.dischargeport=${dischargeport}
ft_docu_psacd_cb.domabr=${domabr}
ft_docu_psacd_cb.status=${status}
ft_docu_psacd_cb.corpbcode=${corpbcode}
ft_docu_psacd_cb.vprepare=${vprepare}
ft_docu_psacd_cb.predate=${predate}
ft_docu_psacd_cb.modifier=${modifier}
ft_docu_psacd_cb.modifydate=${modifydate}
ft_docu_psacd_cb.fcyingms=${unexecfcy}


# 到货单对承兑赎单明细核销记录表
ft_docu_psacd_ct=到货单对承兑赎单明细核销记录表
ft_docu_psacd_ct.tgticode=目标表主键
ft_docu_psacd_ct.tgttbl=目标表名
ft_docu_psacd_ct.purshipssicoder=采购到货单付款方式分摊内码
ft_docu_psacd_ct.fcy=${fcy}

# 承兑赎单商品明细表
ft_docu_psg.idx=${idx}
ft_docu_psg.purordcode=${purordcode}
ft_docu_psg.purshipcoder=到货单号
ft_docu_psg.gcode=${gcode}
ft_docu_psg.cnamedesc=${cnamedesc}
ft_docu_psg.enamedesc=${enamedesc}
ft_docu_psg.goodsdesc=${goodsdesc}
ft_docu_psg.qtc=${qtc}
ft_docu_psg.qtcunit=${qtcunit}
ft_docu_psg.upric=${upric}
ft_docu_psg.purshipfcy=原币金额
ft_docu_psg.fcy=付款申请金额
ft_docu_psg.prjcode=采购业务编号
ft_docu_psg.acdicode=承兑赎单内码
ft_docu_psg.acdgicode=承兑明细内码
ft_docu_psg.purshipgicode=采购到货单商品明细内码
ft_docu_psg.purordgicode=采购合同商品明细内码

# 承兑赎单支付记录表
ft_docu_acdpg.paytype=${paytype}
ft_docu_acdpg.bankcode=银行
ft_docu_acdpg.bankacccode=银行账号
ft_docu_acdpg.paydate=支付日期
ft_docu_acdpg.fcode=${fcode}
ft_docu_acdpg.fcy=支付金额
ft_docu_acdpg.fserate=${fserate}
ft_docu_acdpg.scy=折本位币金额
ft_docu_acdpg.fpgcode=资金实付单号
ft_docu_acdpg.bankno=网银流水号
ft_docu_acdpg.paymode=${paymode}
ft_docu_acdpg.acdicode=承兑赎单内码
ft_docu_acdpg.paicode=付款申请内码
ft_docu_acdpg.pgicode=支付记录内码


# 议付交单主表
ft_docu_dlyd=议付交单
ft_docu_dlyd.dlydicode=议付交单主表内码
ft_docu_dlyd.dlydcode=议付交单申请单号
ft_docu_dlyd.adate=申请日期
ft_docu_dlyd.adatefm=申请日期从
ft_docu_dlyd.adateto=${dateto}
ft_docu_dlyd.salccode=客户
ft_docu_dlyd.fcode=交单币种
ft_docu_dlyd.fcy=交单金额
ft_docu_dlyd.dlyddate=交单日期
ft_docu_dlyd.dlyddatefm=交单日期从
ft_docu_dlyd.dlyddateto=${dateto}
ft_docu_dlyd.bankacccode=交单银行/账号
ft_docu_dlyd.lccode=信用证号
ft_docu_dlyd.lcregicode=到证登记及认领内码
ft_docu_dlyd.isaossd=是否提前交单
ft_docu_dlyd.senddate=寄单日期
ft_docu_dlyd.signdate=签收日期
ft_docu_dlyd.signstatus=签收状态
ft_docu_dlyd.remark=备注
ft_docu_dlyd.rdate=预计收款日期
ft_docu_dlyd.rdatefm=预计收款日期从
ft_docu_dlyd.rdateto=${dateto}
ft_docu_dlyd.invdate=发票日期
ft_docu_dlyd.settdoccode=议付发票号
ft_docu_dlyd.acdate=承兑日期
ft_docu_dlyd.ccodeaddr=客户地址
ft_docu_dlyd.rpbankcode=代收行
ft_docu_dlyd.iccode=IC编号
ft_docu_dlyd.isfirst=首次交单
ft_docu_dlyd.rpbankaddr=代收行地址
ft_docu_dlyd.vddate=有效日期
ft_docu_dlyd.lsddate=最迟交单日期
ft_docu_dlyd.isdis=是否不符点交单
ft_docu_dlyd.distxt=不符点
ft_docu_dlyd.ntbankcode=通知行
ft_docu_dlyd.expresscode=寄单快递号
ft_docu_dlyd.cscompany=快递公司
ft_docu_dlyd.status=状态
ft_docu_dlyd.wfcode=审批编码
ft_docu_dlyd.wfuid=审批节点
ft_docu_dlyd.bcode=部门-业务员
ft_docu_dlyd.wcode=业务员
ft_docu_dlyd.corpbcode=公司
ft_docu_dlyd.vsn=${vbhistory}
ft_docu_dlyd.vsnflag=版本标记
ft_docu_dlyd.vsntype=版本修改类型
ft_docu_dlyd.sheetcode=单据类型
ft_docu_dlyd.cuicode=商户
ft_docu_dlyd.ratifydate=生效时间
ft_docu_dlyd.submitdate=提交时间
ft_docu_dlyd.performdate=审核时间
ft_docu_dlyd.vprepare=创建人
ft_docu_dlyd.predate=创建时间
ft_docu_dlyd.modifier=修改人
ft_docu_dlyd.modifydate=修改时间
ft_docu_dlyd.srcsheetcodelist=来源单据类型
ft_docu_dlyd.paymodelist=收款方式
ft_docu_dlyd.paymode=收款方式
ft_docu_dlyd.abgoodslist=货前后
ft_docu_dlyd.bcode_ex=部门-业务员
ft_docu_dlyd.predate_ex=创建日期
ft_docu_dlyd.rdate_ex=预计收款日期
ft_docu_dlyd.adate_ex=申请日期
ft_docu_dlyd.dlyddate_ex=交单日期
ft_docu_dlyd.islast=是否最后交单
ft_docu_dlyd.lcbank=开证行
ft_docu_dlyd.lcbank=测算日期
ft_docu_dlyd.lcbank=融资天数
ft_docu_dlyd.lcbank=福费廷费率
ft_docu_dlyd.lcbank=浮点
ft_docu_dlyd.lcbank=开证行
ft_docu_dlyd.caldate=测算日期
ft_docu_dlyd.findays=融资天数
ft_docu_dlyd.forfrate=福费廷费率
ft_docu_dlyd.tfloat=浮点
ft_docu_dlyd.dayrate=日利率
ft_docu_dlyd.minstdfcy=最低收费标准
ft_docu_dlyd.forffcy=福费廷费用①
ft_docu_dlyd.loanrate=借款利率
ft_docu_dlyd.interestfcy=内部计息②
ft_docu_dlyd.difffcy=差额②-①
ft_docu_dlyd.docudeliveryfinastatus=交单融资申请单状态


# 议付交单明细表
ft_docu_dlydg=交单明细
ft_docu_dlydg.dlydgicode=议付交单子表内码
ft_docu_dlydg.dlydgicoder=议付交单子表原始内码
ft_docu_dlydg.dlydicode=议付交单主表内码
ft_docu_dlydg.salshipssicoder=销售发货单分摊收款方式原始内码
ft_docu_dlydg.idx=序号
ft_docu_dlydg.salordcode=销售合同号
ft_docu_dlydg.salordicode=销售合同号内码
ft_docu_dlydg.salprjicode=销售业务编号内码
ft_docu_dlydg.salprjcode=销售业务编号
ft_docu_dlydg.srcsheetcode=来源单据类型
ft_docu_dlydg.salshipcoder=发货单号
ft_docu_dlydg.salshipicoder=发货单内码
ft_docu_dlydg.tsosicoder=出仓单回单内码
ft_docu_dlydg.tsoscoder=出仓回单号
ft_docu_dlydg.abgoods=货前后
ft_docu_dlydg.paymode=收款方式
ft_docu_dlydg.recfcy=应收金额
ft_docu_dlydg.fcy=交单金额
ft_docu_dlydg.purordcode=采购合同号
ft_docu_dlydg.purordicode=采购合同号内码
ft_docu_dlydg.purprjicode=采购业务编号内码
ft_docu_dlydg.purprjcode=采购业务编号
ft_docu_dlydg.purshipicoder=到货单号内码
ft_docu_dlydg.purshipcoder=到货单号
ft_docu_dlydg.gcode=商品编码
ft_docu_dlydg.cnamedesc=商品名称
ft_docu_dlydg.enamedesc=商品英文名称
ft_docu_dlydg.gvcode=商品细类
ft_docu_dlydg.wcode=业务员
ft_docu_dlydg.bcode=业务员部门
ft_docu_dlydg.redflag=红冲
ft_docu_dlydg.lccode=信用证号
ft_docu_dlydg.lcregicode=到证登记及认领内码
ft_docu_dlydg.fserate=折本位币汇率
ft_docu_dlydg.suerate=折美元汇率
ft_docu_dlydg.scerate=折人民币汇率
ft_docu_dlydg.scy=折本位币金额
ft_docu_dlydg.zusd=折美元金额
ft_docu_dlydg.zcny=折人民币金额

# 议付交单-提货号信息表
ft_docu_dlydb=提货号信息
ft_docu_dlydb.dlydbicode=提单号信息内码
ft_docu_dlydb.dlydicode=议付交单主表内码
ft_docu_dlydb.bolcode=提单号
ft_docu_dlydb.logisticode=排载单内码
ft_docu_dlydb.logistcode=排载单号

# 议付交单-收款方式表
ft_docu_dlyds=收款方式
ft_docu_dlyds.dlydsicode=收款方式内码
ft_docu_dlyds.dlydicode=议付交单主表内码
ft_docu_dlyds.abgoods=货前后
ft_docu_dlyds.paymode=收款方式
ft_docu_dlyds.fcy=交单金额

# 议付交单-交单承兑确认表
ft_docu_dlydc=交单承兑确认
ft_docu_dlydc.dlydcicode=交单承兑确认内码
ft_docu_dlydc.dlydicode=议付交单主表内码
ft_docu_dlydc.idx=序号
ft_docu_dlydc.docutype=交单/承兑
ft_docu_dlydc.odate=业务日期
ft_docu_dlydc.rdate=预计收款日期
ft_docu_dlydc.tcaptime=事务处理日期
ft_docu_dlydc.remark=备注
ft_docu_dlydc.redflag=红冲


# 议付交单-融资测算表
ft_docu_dlydf=融资测算
ft_docu_dlydf.dlydficode=融资测算内码
ft_docu_dlydf.dlydicode=议付交单主表内码
ft_docu_dlydf.dlydcode=议付交单申请单号
ft_docu_dlydf.dlyddate=交单日期
ft_docu_dlydf.rdate=预计收款日期
ft_docu_dlydf.acdate=承兑日期
ft_docu_dlydf.fcode=币种
ft_docu_dlydf.fcy=交单金额
ft_docu_dlydf.caldate=测算日期
ft_docu_dlydf.findays=融资天数
ft_docu_dlydf.forfrate=福费廷费率
ft_docu_dlydf.tfloat=浮点
ft_docu_dlydf.dayrate=日利率
ft_docu_dlydf.minstdfcy=最低收费标准
ft_docu_dlydf.forffcy=福费廷费用①
ft_docu_dlydf.loanrate=借款利率
ft_docu_dlydf.interestfcy=内部计息②
ft_docu_dlydf.difffcy=差额②-①


# 议付交单-单证要求表
ft_docu_dlydr=单证要求
ft_docu_dlydr.dlydricode=交单承兑确认内码
ft_docu_dlydr.dlydicode=议付交单主表内码
ft_docu_dlydr.idx=序号
ft_docu_dlydr.docname=单证名称
ft_docu_dlydr.nums=份数
ft_docu_dlydr.remark=备注


# 发货单对议付交单明细核销余额表
ft_docu_dlydg_cb=发货单对议付交单明细核销余额
ft_docu_dlydg_cb.salshipssicoder=销售发货单分摊收款方式原始内码
ft_docu_dlydg_cb.corflag=标识
ft_docu_dlydg_cb.salccode=客户
ft_docu_dlydg_cb.salordcode=销售合同号
ft_docu_dlydg_cb.salordicode=销售合同号内码
ft_docu_dlydg_cb.salprjicode=销售业务编号内码
ft_docu_dlydg_cb.salprjcode=销售业务编号
ft_docu_dlydg_cb.srcsheetcode=来源单据类型
ft_docu_dlydg_cb.srcicode=来源单据内码
ft_docu_dlydg_cb.srccode=来源单据号
ft_docu_dlydg_cb.salshipcoder=发货单号
ft_docu_dlydg_cb.salshipicoder=发货单内码
ft_docu_dlydg_cb.tsosicoder=出仓单号内码
ft_docu_dlydg_cb.tsoscoder=出仓单号
ft_docu_dlydg_cb.abgoods=货前后
ft_docu_dlydg_cb.paymode=收款方式
ft_docu_dlydg_cb.fcode=币种
ft_docu_dlydg_cb.overseas=境内外
ft_docu_dlydg_cb.fcy=计划执行金额
ft_docu_dlydg_cb.fcyed=已执行金额
ft_docu_dlydg_cb.fcying=待执行金额
ft_docu_dlydg_cb.purordcode=采购合同号
ft_docu_dlydg_cb.purordicode=采购合同号内码
ft_docu_dlydg_cb.purprjicode=采购业务编号内码
ft_docu_dlydg_cb.purprjcode=采购业务编号
ft_docu_dlydg_cb.purshipicoder=到货单号内码
ft_docu_dlydg_cb.purshipcoder=到货单号
ft_docu_dlydg_cb.gcode=商品编码
ft_docu_dlydg_cb.cnamedesc=商品名称
ft_docu_dlydg_cb.enamedesc=商品英文名称
ft_docu_dlydg_cb.gvcode=商品细类
ft_docu_dlydg_cb.wcode=业务员
ft_docu_dlydg_cb.bcode=业务员部门
ft_docu_dlydg_cb.corpbcode=公司
ft_docu_dlydg_cb.lccode=信用证号
ft_docu_dlydg_cb.lcregicode=到证登记及认领内码
ft_docu_dlydg_cb.trademode=贸易方式
ft_docu_dlydg_cb.stockdate=出仓日期
ft_docu_dlydg_cb.isaossd=是否提前交单
ft_docu_dlydg_cb.vprepare=创建人
ft_docu_dlydg_cb.predate=创建时间
ft_docu_dlydg_cb.modifydate=修改时间
ft_docu_dlydg_cb.modifier=修改人

# 承兑赎单接口相关

# 赎单申请撤回是否成功接口
ft_acs.accrdback.operator=操作人[operator]
ft_acs.accrdback.retractresult=撤回是否成功[retractresult]
ft_acs.accrdback.acdicode=【承兑赎单】内码[acdicode]
ft_acs.accrdback.accOrdBackParams=接口参数[accOrdBackParams]
# 赎单确认、承兑确认接口参数实体
ft_acs.accrd.accOrdConfirmParams=接口参数[accOrdConfirmParams]
ft_acs.accrd.confirmtype=确认类型[confirmtype]
ft_acs.accrd.acdate=承兑日期[acdate]
ft_acs.accrd.operator=操作人[operator]
ft_acs.accrd.srcsysno=来源系统唯一ID[srcsysno]
ft_acs.accrd.acdicode=【承兑赎单】内码[acdicode]
ft_acs.accrd.iccode=【IC编码】[iccode]
# 赎单付款接口参数
ft_acs.accrdpay.accOrdPayParams=接口参数[accOrdPayParams]
ft_acs.accrdpay.bankacccode=银行账号[bankacccode]
ft_acs.accrdpay.paydate=支付日期[paydate]
ft_acs.accrdpay.fcy=支付金额[fcy]
ft_acs.accrdpay.fpgcode=资金实付单号[fpgcode]
ft_acs.accrdpay.srcsysno=来源系统唯一ID[srcsysno]
ft_acs.accrdpay.bankno=网银流水号[bankno]
ft_acs.accrdpay.operator=操作人[operator]
ft_acs.accrdpay.acdicode=承兑赎单内码[acdicode]
# 赎单确认、承兑确认、赎单付款 撤回接口参数
ft_acs.accOrdCancel.accOrdCancelParams=接口参数[accOrdCancelParams]
ft_acs.accOrdCancel.confirmtype=确认类型[confirmtype]
ft_acs.accOrdCancel.operator=操作人[operator]
ft_acs.accOrdCancel.srcsysno=来源系统唯一ID[srcsysno]
ft_acs.accOrdCancel.acdicode=【承兑赎单】内码[acdicode]



# 交单确认接口
ft_acs.dlyd.confirm.docuDeliveryConfirmParams=接口参数[docuDeliveryConfirmParams]
ft_acs.dlyd.confirm.dlydicode=议付交单内码[dlydicode]
ft_acs.dlyd.confirm.operator=操作人[operator]
ft_acs.dlyd.confirm.odate=业务日期[odate]
ft_acs.dlyd.confirm.rdate=预计收款日期[rdate]
ft_acs.dlyd.confirm.confirmtype=确认类型[confirmtype]
# 交单申请撤回是否成功接口
ft_acs.dlyd.applyback.docuDeliveryBackParams=接口参数[docuDeliveryBackParams]
ft_acs.dlyd.applyback.dlydicode=议付交单内码[dlydicode]
ft_acs.dlyd.applyback.operator=操作人[operator]
ft_acs.dlyd.applyback.retractresult=撤回是否成功[retractresult]
# 交单、承兑确认取消接口
ft_acs.dlyd.cancel.docuDeliveryCancelParams=接口参数[docuDeliveryCancelParams]
ft_acs.dlyd.cancel.dlydicode=议付交单内码[dlydicode]
ft_acs.dlyd.cancel.operator=操作人[operator]
ft_acs.dlyd.cancel.canceltype=取消类型[canceltype]


# 承兑赎单凭证
ft_docu_actvou_view.acdcode=承兑赎单号
ft_docu_actvou_view.acdicode=承兑赎单内码
ft_docu_actvou_view.fpgcode=资金实付单号
ft_docu_actvou_view.vmarkicode=制凭证标记表内码
ft_docu_actvou_view.paytype=支付类型
ft_docu_actvou_view.paydate=支付日期
ft_docu_actvou_view.beneccode=收款方
ft_docu_actvou_view.bankcode=银行
ft_docu_actvou_view.bankacccode=银行账号
ft_docu_actvou_view.bcode=业务员部门
ft_docu_actvou_view.wcode=业务员
ft_docu_actvou_view.vprepare=创建人
ft_docu_actvou_view.predate=创建日期
ft_docu_actvou_view.isreplc=代开证
ft_docu_actvou_view.redflag=红冲
ft_docu_actvou_view.redflag#1=${bered}
ft_docu_actvou_view.redflag#2=${red}
ft_docu_actvou_view.year=年
ft_docu_actvou_view.month=月
ft_docu_actvou_view.vidflag=制证选项
ft_docu_actvou_view.paydatefm=实付日期从
ft_docu_actvou_view.paydateto=到
ft_docu_actvou_view.lrpicode=往来内码


#承兑明细
actvoug.lrpicode=往来内码
actvoug.rptype=收付项目
actvoug.invrptype=费用类型
actvoug.tradetype=贸易方式
actvoug.fcy=原币金额
actvoug.purordcode=采购合同号
actvoug.purordicode=采购合同号内码
actvoug.purprjcode=采购业务编号
actvoug.purprjicode=采购业务内码
actvoug.purshipcoder=到货单号
actvoug.purshipicoder=到货单号内码
actvoug.salshipcoder=发货单号
actvoug.salshipicoder=发货单内码
actvoug.gvcode=商品类目
actvoug.vprepare=创建人
actvoug.predate=创建时间
