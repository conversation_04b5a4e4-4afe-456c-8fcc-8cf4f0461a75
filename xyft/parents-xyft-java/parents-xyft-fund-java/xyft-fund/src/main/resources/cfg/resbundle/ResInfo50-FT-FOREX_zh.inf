# FT-FOREX系统异常资源文件
FT-FOREX.00000001=〖%0〗与〖%1〗“%2”汇总必须相等！
FT-FOREX.00000002=〖%0〗与〖%1〗未完成匹配！
FT-FOREX.00000003=〖释放信息〗“变更金额”汇总值与〖重新关联信息〗“交易金额”汇总值不相等
FT-FOREX.00000004=以下敞口对象的“%0”禁止大于含溢装的“待外汇交易金额”，请检查：%1、%2
FT-FOREX.00000005=〖外汇敞口明细〗中近端、远端申请交易金额汇总值不一致，请检查！
FT-FOREX.00000006=〖外汇敞口明细〗中近端和远端存在相同的合同性质，请检查！
FT-FOREX.00000007=开仓手数*10,0000与交易金额不一致，请检查！
FT-FOREX.00000008=%0申请单未提交到同审状态下，请检查！
FT-FOREX.00000009=〖平仓明细〗和〖外汇交易明细〗不能为空！
FT-FOREX.00000010=勾选记录中存在非待送接口状态单据，禁止操作！
FT-FOREX.00000011=确定推送资金吗？
FT-FOREX.00000012=请同时选择近远端数据！
FT-FOREX.00000013=“近远端“平仓金额”不一致，请检查！
FT-FOREX.00000014=平仓手数*10,0000与平仓金额不相等，请检查！
FT-FOREX.00000015=区间宝产品禁止不行权！
FT-FOREX.00000016=展期手数*10,0000与展期金额不一致，请检查！
FT-FOREX.00000017=〖平仓明细〗须全部为远端数据，请检查！
FT-FOREX.00000018=近端数据未交割，请先处理近端数据！
FT-FOREX.00000019=〖外汇敞口明细〗必须为空！
FT-FOREX.00000020=〖外汇敞口明细〗不可为空！
FT-FOREX.00000021=以下单据必须一起同审\n【外汇移仓申请单】:%0\n【外汇交易申请单】:%1\n【外汇平仓申请单】:%2
FT-FOREX.00000022=%0销售合同号下〖外汇敞口明细〗“原币金额”汇总值与〖结汇资金明细〗“结汇金额”不一致！
FT-FOREX.00000023=未找到此结汇行为对应的部门！
FT-FOREX.00000024=以下销售合同号〖交割明细〗交割金额与〖业务信息〗交割金额汇总值不相等!\n%0
FT-FOREX.00000025=〖业务信息〗不为空!
FT-FOREX.00000026=以下采购合同号〖交割明细〗交割金额与〖业务信息〗交割金额汇总值不相等!\n%0
FT-FOREX.00000027=交割手数*10,0000与交割金额不相等!
FT-FOREX.00000028=“申请交割日期”不得晚于“交易到期日到”!
FT-FOREX.00000029=〖平仓明细〗禁止非空！
FT-FOREX.00000030=〖外汇交易明细〗禁止非空！
FT-FOREX.00000031=申请已办理展期，禁止操作！
FT-FOREX.00000032=数据已入账，禁止操作！
FT-FOREX.00000033=上月数据未红冲！
FT-FOREX.00000034=记账凭证字%0年%1月%2日%3号
FT-FOREX.00000035=“估值数据未入账，禁止红冲！”或“估值数据已红冲，禁止操作！”
FT-FOREX.00000036=根据内码%0未找到%1！
FT-FOREX.00000037=该笔流水禁止手动关联业务信息！
FT-FOREX.00000038=该笔流水禁止取消关联业务信息！
FT-FOREX.00000039=已红冲的数据禁止取消关联！
FT-FOREX.00000040=购汇金额与关联金额汇总值不一致，请检查！
FT-FOREX.00000041=存在数据未入账，禁止红冲！
FT-FOREX.00000042=数据已红冲，禁止操作！
FT-FOREX.00000043=“交易到期日到”不得早于“交易到日期从”!
FT-FOREX.00000044=根据来源单据内码%0未找到%1！
FT-FOREX.00000045=交易序号重复！
FT-FOREX.00000046=区间宝产品下“原交易序号禁止为空！”
FT-FOREX.00000047=找不到对应有效数据！
FT-FOREX.00000048=该交易序号不存在！
FT-FOREX.00000049=根据来源系统唯一ID查找此数据已入账，禁止撤回！
FT-FOREX.00000050=根据内码%0未找到外汇交易申请!
FT-FOREX.00000051=该交易序号已被下游单据引用，禁止撤回！
FT-FOREX.00000052=禁止手动推送资金！
FT-FOREX.00000053=根据来源单据内码%0未找到外汇平仓申请！
FT-FOREX.00000054=该交易序号下的平仓信息已释放敞口数据，且平仓金额大于剩余敞口余额，禁止撤回！
FT-FOREX.00000055=根据来源单据内码%0未找到外汇交割申请！
FT-FOREX.00000056=根据来源单据内码%0未找到外汇展期申请！
FT-FOREX.00000057=根据内码%0未找到外汇展期申请！
FT-FOREX.00000058=根据内码%0未找到外汇敞口对冲申请！
FT-FOREX.00000059=根据内码%0未找到外汇移仓申请！
FT-FOREX.00000060=根据内码%0未找到即期结汇申请！
FT-FOREX.00000061=仅可勾选一行数据！
FT-FOREX.00000062=根据来源单据号手工录入%0未找到即期结汇申请！
FT-FOREX.00000063=%0合同锁汇比例未超%1，请确认！
FT-FOREX.00000064=%0锁汇比例已超%1，请检查！
FT-FOREX.00000065=%0意向锁汇比例已超%1，请检查！
FT-FOREX.00000066=%0事业部%1产品申请交易金额已超出交易剩余额度，请检查！
FT-FOREX.00000067=剩余未交易金额不一致！

# FT-FOREX系统提示资源文件
FT-FOREX.10000001=将是否需要关联敞口由是改为否时，将清空〖外汇敞口明细〗，确定修改吗？
FT-FOREX.10000002=是否生成模拟结汇申请？
FT-FOREX.10000003=银行估值表数据已存在，确定重新获取吗？
FT-FOREX.10000004=是否取消关联？
FT-FOREX.10000005=%0合同锁汇比例未超70%，请确认
