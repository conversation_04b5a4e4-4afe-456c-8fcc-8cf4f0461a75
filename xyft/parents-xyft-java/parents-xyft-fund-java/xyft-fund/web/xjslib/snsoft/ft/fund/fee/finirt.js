Xjs.loadedXjs.push("snsoft/ft/fund/fee/finirt");
Xjs.namespace("snsoft.ft.fee.finirt.invoker");snsoft.ft.fee.finirt.invoker.FinIrtEditIsSettleInvoker=function(parameter){snsoft.ft.fee.finirt.invoker.FinIrtEditIsSettleInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.fee.finirt.invoker.FinIrtEditIsSettleInvoker,snsoft.ext.cmd.CommandInvoker,{check:function(event){var rows=this.table.getSelectedRowNumbers();if(!rows||rows.length==0){throw new Error(Xjs.ResBundle.getResVal("FT.00000048"));}for(var i=0;i<rows.length;i++){var issettle=this.dataSet.getValue("issettle",rows[i]);if("Y"==issettle){throw new Error(Xjs.ResBundle.getResVal("FT-FEE.00000018"));}}return this.showConfirmDialog(event,this.getTitle(),this.getResVal("FT-FEE.00000026"));},invoke:function(event){var rows=this.table.getSelectedRowNumbers(),finirticodes=[];for(var i=0;i<rows.length;i++){var issettle=this.dataSet.getValue("issettle",rows[i]);if("Y"==issettle){throw new Error(Xjs.ResBundle.getResVal("FT-FEE.00000018"));}finirticodes.push(this.dataSet.getValue("finirticode",rows[i]));}var ps=this.buildInvokeParam(null);ps.finirticodes=finirticodes;return ps;},afterInvoke:function(event){this.mainDataSet.refresh(this.mainDataSet._refreshParameter);var tsInfo=Xjs.ResBundle.getResVal("Dlg.Info"),msg=Xjs.ResBundle.getResVal("FT-FEE.msg_success");Xjs.ui.UIUtil.showSuccessInfoDialog(tsInfo,msg);}});