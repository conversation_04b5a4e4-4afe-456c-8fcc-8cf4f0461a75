Xjs.loadedXjs.push("snsoft/ft/fund/loan/factfin");
Xjs.namespace("snsoft.ft.loan.factfin.invoker");snsoft.ft.loan.factfin.invoker.FactFinaGeneratePayAppInvoker=function(parameter){snsoft.ft.loan.factfin.invoker.FactFinaGeneratePayAppInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.loan.factfin.invoker.FactFinaGeneratePayAppInvoker,snsoft.ext.cmd.CommandInvoker,{beforeCheck:function(event){if(!("70"==this.mainDataSet.get("status"))){var column=this.mainTable.getColumn("status"),codeData=column.selectOptions,codeName1=codeData.getCodeName1("70");throw Xjs.ResBundle.getResVal("FT.00000060",codeName1);}if("N"==this.mainDataSet.get("isbackown")){throw Xjs.ResBundle.getResVal("FT-LOAN.00000070");}var faliTable=Xjs.util.TableUtils.getTable_$Panel_$String(this.mainTable,"ft_loan_factfali"),faliTableDataSet=faliTable.getDataSet();faliTableDataSet.ensureOpened();if(faliTableDataSet.getRowCount()<1){throw new Error(Xjs.ResBundle.getResVal("FT-LOAN.00000071"));}return null;},invoke:function(event){var beforeCheckRtnVal=event.beforeCheckRtnVal,paicodes=beforeCheckRtnVal.paicode;if(paicodes){if(beforeCheckRtnVal.already){var funcCall=new Xjs.FuncCall(this.toDetail,this,[paicodes]);Xjs.ui.UIUtil.showConfirmDialog(this.getTitle(),Xjs.ResBundle.getResVal("FT-LOAN.00000072"),funcCall,null);}else this.toDetail(null,"ok",paicodes);}return null;},toDetail:function(dialog,command,paicode){if("ok"==command)snsoft.plat.bas.viewdetail.ViewDetailListener.invokeViewDetail({sheetcode:"FT-PAY.PayApp",sheetEntry:true,paicode:paicode},"pacode");}});snsoft.ft.loan.factfin.invoker.FactFinaGenInvInvoker=function(parameter){snsoft.ft.loan.factfin.invoker.FactFinaGenInvInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.loan.factfin.invoker.FactFinaGenInvInvoker,snsoft.ext.cmd.CommandInvoker,{invoke:function(event){var rows=this.table.getSelectedRowNumbers();if(rows==null||rows.length==0){throw new Error(Xjs.ResBundle.getResVal("FT.00000048"));}else if(rows.length>1){throw new Error(Xjs.ResBundle.getResVal("FT.00000087"));}if(!this.dataSet.getValue("rptype",rows[0])){throw new Error(Xjs.ResBundle.getResVal("FT-LOAN.00000062",this.table.getColumn("rptype").title));}if(!this.dataSet.getValue("fcy",rows[0])){throw new Error(Xjs.ResBundle.getResVal("FT-LOAN.00000062",this.table.getColumn("fcy").title));}var invokeParam=this.buildInvokeParam(null);invokeParam.mains=[];for(var r=0;r<rows.length;r++){var keyVals=Xjs.util.DataSetUtils.getValuesTo(this.dataSet,rows[r],null);invokeParam.mains.push(keyVals);}return invokeParam;},afterInvoke:function(event){snsoft.plat.bas.viewdetail.ViewDetailListener.invokeViewDetail(event.invokeRtnVal,"purfeeinvcode");}});snsoft.ft.loan.factfin.invoker.FactFinaGenPayAppInvoker=function(parameter){snsoft.ft.loan.factfin.invoker.FactFinaGenPayAppInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.loan.factfin.invoker.FactFinaGenPayAppInvoker,snsoft.ext.cmd.CommandInvoker,{invoke:function(event){var rows=this.table.getSelectedRowNumbers();if(rows==null||rows.length==0){throw new Error(Xjs.ResBundle.getResVal("FT.00000048"));}else if(rows.length>1){throw new Error(Xjs.ResBundle.getResVal("FT.00000087"));}if(!this.dataSet.getValue("rptype",rows[0])){throw new Error(Xjs.ResBundle.getResVal("FT-LOAN.00000062",this.table.getColumn("rptype").title));}if(!this.dataSet.getValue("fcy",rows[0])){throw new Error(Xjs.ResBundle.getResVal("FT-LOAN.00000062",this.table.getColumn("fcy").title));}var invokeParam=this.buildInvokeParam(null);invokeParam.mains=[];for(var r=0;r<rows.length;r++){var keyVals=Xjs.util.DataSetUtils.getValuesTo(this.dataSet,rows[r],null);invokeParam.mains.push(keyVals);}return invokeParam;},afterInvoke:function(event){snsoft.plat.bas.viewdetail.ViewDetailListener.invokeViewDetail(event.invokeRtnVal,"pacode");}});snsoft.ft.loan.factfin.invoker.FactFinaOpenPayAppInvoker=function(parameter){snsoft.ft.loan.factfin.invoker.FactFinaOpenPayAppInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.loan.factfin.invoker.FactFinaOpenPayAppInvoker,snsoft.ext.cmd.CommandInvoker,{invoke:function(event){var data={sheetcode:"FT-PAY.PayApp"};data.sheetEntry=true;data.paicode=event.beforeCheckRtnVal.paicode;snsoft.plat.bas.viewdetail.ViewDetailListener.invokeViewDetail(data,"pacode");return null;}});snsoft.ft.loan.factfin.invoker.FactFinasendSerCancelInvoker=function(parameter){snsoft.ft.loan.factfin.invoker.FactFinasendSerCancelInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.loan.factfin.invoker.FactFinasendSerCancelInvoker,snsoft.ext.cmd.CommandInvoker,{beforeCheck:function(event){var faliTable=Xjs.util.TableUtils.getTable_$Panel_$String(this.mainTable,"ft_loan_factfali"),faliTableDataSet=faliTable.getDataSet();faliTableDataSet.ensureOpened();if(faliTableDataSet.getRowCount()>0){throw new Error(Xjs.ResBundle.getResVal("FT-LOAN.00000016"));}return null;},invoke:function(event){return {factfaicode:this.mainDataSet.get("factfaicode")};},afterInvoke:function(event){this.mainTable.refreshTable();}});snsoft.ft.loan.factfin.invoker.IsbackownEditInvoker=function(parameter){snsoft.ft.loan.factfin.invoker.IsbackownEditInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.loan.factfin.invoker.IsbackownEditInvoker,snsoft.ext.cmd.CommandInvoker,{invoke:function(event){var ftLoanFactfari=Xjs.util.TableUtils.getTable_$Panel_$String(this.mainTable,"ft_loan_factfari");if(ftLoanFactfari.getDataSet().getRowCount()>0){throw new Error(Xjs.ResBundle.getResVal("FT-LOAN.00000076"));}return null;}});Xjs.namespace("snsoft.ft.loan.factfin.lis");snsoft.ft.loan.factfin.lis.FactFinaAppColumnControlJSListener=function(params){snsoft.ft.loan.factfin.lis.FactFinaAppColumnControlJSListener.superclass.constructor.call(this,params);};Xjs.extend(snsoft.ft.loan.factfin.lis.FactFinaAppColumnControlJSListener,snsoft.plat.bas.busi.SystemFunctionListener,{initComponent:function(table,values){snsoft.ft.loan.factfin.lis.FactFinaAppColumnControlJSListener.superclass.initComponent.call(this,table,values);this.mainTable=this.getTable(table,"ft_loan_factfa");this.mainDataSet=this.mainTable.getDataSet();this.basTable=this.getTable(table,"ft_loan_factfa_bas");this.basDataSet=this.basTable.getDataSet();},dataSetFieldPosted:function(dataSet,event){if(event.columnName=="isfreqccode"){if(!dataSet.getValue("facttype")){if(dataSet.getValue(event.columnIndex)=="Y")this.mainDataSet.setValue("facttype","10");else this.mainDataSet.setValue("facttype","20");}}}});