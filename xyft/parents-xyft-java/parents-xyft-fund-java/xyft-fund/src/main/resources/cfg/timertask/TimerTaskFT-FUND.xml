<?xml version="1.0" encoding="UTF-8"?>
<timertask-list xmlns="http://www.snsoft.com.cn/schema/TimerTask"
                xsi:schemaLocation="http://www.snsoft.com.cn/schema/TimerTask http://www.snsoft.com.cn/schema/TimerTask.xsd"
                xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <timertask id="FT-FUND.001" name="资金自动凭证消息重推" work-time="CRON:0 0 1 * * ?"  interval="0" auto-start="true" thread="true" task-type="tac">
        <task>
            cuicode=snsoft.context.AppContext.getUserSession(true).getUserCuicode()
            sql = "select srcsheetcode,srcicode,vmarkicode from ft_fund_vmark where vidflag=0 and manualflag=0 and redflag= 0 and status='70' and cuicode='"+cuicode+"'"
            db = getDatabaseByTable("ft_fund_vmark")
            vks = db.query3(sql)
            if vks != null &amp;&amp; vks.length > 0
            for vk in vks
            snsoft.sna.vm.vmark.utils.VMarkUtils.sendAutoVoucherMessage(vk[0],vk[0],vk[1],vk[2])
            java.lang.Thread.sleep(2000)
            end for
            end if
        </task>
        <remark><![CDATA[
            其他参数类需配置：
                env.USERCUICODE=XXX(商户)
                env.USERCODE=XXX(用户)
        ]]></remark>
    </timertask>

</timertask-list>