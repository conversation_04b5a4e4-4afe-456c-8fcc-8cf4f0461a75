Xjs.loadedXjs.push("snsoft/ft/fund/rpadj/lrpdeal");
Xjs.namespace("snsoft.ft.rpadj.lrpdeal.invoker");snsoft.ft.rpadj.lrpdeal.invoker.LrpDealSelectAcodeJSInvoker=function(parameter){snsoft.ft.rpadj.lrpdeal.invoker.LrpDealSelectAcodeJSInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.rpadj.lrpdeal.invoker.LrpDealSelectAcodeJSInvoker,snsoft.ext.cmd.CommandInvoker,{onClick:function(event){var acode=this.dataSet.getValue("acode");if(acode)return {type:0,title:this.getTitle(),prompt:Xjs.ResBundle.getResVal("FT-RPADJ.00000006"),perform:new Xjs.FuncCall(this._resolve,this,[event])};return null;},_resolve:function(event){event.flag="yes";},check:function(event){if(!this.dataSet.getValue("acode")||"yes"==event.flag)return this.newDialogWithFunc(event,(e)=>{var param=new Xjs.ui.aid.AidInfoService$AidInfoParam("#SNA-ACC.Acode",null),cmparams={};cmparams.JSONFILTER="{n:'atype',v:['E','R'],op:'in'}";cmparams.sheetcode=this.dataSet.getValue("sheetcode");cmparams.opids="I";cmparams.corpbcode=this.dataSet.getValue("corpbcode");cmparams.bcode=this.dataSet.getValue("bcode");param.setCmparams(cmparams);param.setSelectMiddle(false);var aidInfo=Xjs.ui.aid.AidInfo.createAidInfo(param),dlg=aidInfo.aidInputer;dlg.defaultCodeData=aidInfo.toCodeData();dlg.selOptions|=0x4000;dlg.title=this.cfg.title;dlg.doAidInput(this.table,null,null);return dlg;},(e,d)=>{var snaAccAcode=d.getItemByName("LIST"),aidDataSet=snaAccAcode.getDataSet(),param={};param.lrpdicode=this.dataSet.getValue("lrpdicode");param.acode=aidDataSet.get("acode");var mains=[],main={};main.lrpdicode=this.dataSet.getValue("lrpdicode");main.sheetcode=this.dataSet.getValue("sheetcode");main.modifydate=this.dataSet.getValue("modifydate");mains.push(main);param.mains=mains;event.checkData.param=param;});return null;},invoke:function(event){return event.checkData.param;},afterInvoke:function(event){this.mainTable.refreshTable();}});