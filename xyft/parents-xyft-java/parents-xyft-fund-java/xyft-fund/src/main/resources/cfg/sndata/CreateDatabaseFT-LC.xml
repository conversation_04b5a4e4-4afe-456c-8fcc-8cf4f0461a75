<?xml version="1.0" encoding="UTF-8"?>
<db id="FT-LC" title="信用证" groupid="FT-FUND" xmlns="http://www.snsoft.com.cn/schema/CreateDatabase"
	xsi:schemaLocation="http://www.snsoft.com.cn/schema/CreateDatabase http://www.snsoft.com.cn/schema/CreateDatabase.xsd"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

	<table id="30010" name="ft_lc_pca" title="信用证预排额度申请主表" datasrcid="FT-FUND" rdatasrcid="FT-FUND" storegrp="FTFUNDDATA_TBS">
		<column name="pcaicode" title="信用证预排额度申请单内码" type="VARCHAR(SZIBILL)" primkey="true" />
		<column name="pcacode" title="预排额度申请单号" type="VARCHAR(SZNBILL)" />
		<column name="status" title="状态" type="VARCHAR(SZSTATUS)" />
		<column name="wfcode" title="审批编码" type="VARCHAR(SZWFCODE)" />
		<column name="wfuid" title="审批节点" type="VARCHAR(SZWFUID)" />
		<column name="cuicode" title="商户编码" type="VARCHAR(SZCUICODE)" />
		<column name="sheetcode" title="单据类型" type="VARCHAR(SZSHEET)" />
		<column name="bcode" title="业务员部门" type="VARCHAR(SZBCODE)" />
		<column name="wcode" title="业务员" type="VARCHAR(SZWCODE)" />
		<column name="corpbcode" title="公司" type="VARCHAR(SZBCODE)" />
		<column name="deptbcode" title="事业部" type="VARCHAR(SZBCODE)" />
		<column name="ratifydate" title="生效时间" type="DATE" />
		<column name="submitdate" title="提交时间" type="DATE" />
		<column name="performdate" title="审核时间" type="DATE" />
		<columns cpFromTable="tplt_cm"/>
		<column name="tendbankesc" title="意向银行" type="VARCHAR(SZTEXT)" />
		<column name="bankcode" title="排证银行" type="VARCHAR(SZBANKCODE)" />
		<column name="adate" title="申请日期" type="DATE" />
		<column name="ishighrisk" title="是否涉及高风险国家" type="CHAR(SZYN)" />
		<column name="ncode" title="贸易国别(地区)" type="VARCHAR(SZNCODE)" />
		<column name="tradetype" title="贸易方式" type="VARCHAR(SZTYPE)" />
		<column name="fcy" title="排证金额" type="NUMERIC(MNYINT.MNYDEC)" />
		<column name="shipratefcy" title="排证金额(含溢装)" type="NUMERIC(MNYINT.MNYDEC)" />
		<column name="fcyed" title="开证金额" type="NUMERIC(MNYINT.MNYDEC)" />
		<column name="isdelterm" title="是否删除制裁条款" type="CHAR(SZYN)" />
		<column name="fcode" title="币种" type="VARCHAR(SZFCODE)" />
		<column name="lctype" title="信用证类型" type="VARCHAR(SZRPCODE)" />
		<column name="days" title="天数" type="INTEGER" />
		<column name="isosrate" title="是否溢短装" type="CHAR(SZYN)" />
		<column name="overshiprate" title="溢装率" type="NUMERIC(OSRINT.OSRDEC)" />
		<column name="plcdate" title="预计开证日期" type="DATE" />
		<column name="alctype" title="开证方式" type="VARCHAR(SZTYPE)" />
		<column name="isonelc" title="是否一次性开证" type="CHAR(SZYN)" />
		<column name="stbreason" title="选择意向银行原因" type="VARCHAR(512)" />
		<column name="beneccode" title="受益人" type="VARCHAR(SZCCODE)"/>
		<column name="subbcode" title="商品表部门" type="VARCHAR(SZTEXT)" />
		<column name="srcserialno" title="来源流水号" type="VARCHAR(SZIBILL)" />
		<column name="rbefstatus" title="额度释放/撤回前状态" type="VARCHAR(SZSTATUS)" />
		<index name="ft_lc_pca_predate" fields="predate,cuicode,corpbcode,bcode,wcode" storegrp="FTFUNDINDX_TBS"/>
		<index name="ft_lc_pca_pcacode" fields="pcacode,sheetcode,cuicode" storegrp="FTFUNDINDX_TBS"/>
	</table>

	<table id="30020" name="ft_lc_pcag" title="信用证预排额度申请商品表" datasrcid="FT-FUND" rdatasrcid="FT-FUND" storegrp="FTFUNDDATA_TBS">
		<column name="pcagicode" title="预排额度申请商品内码" type="VARCHAR(SZIBILL)" primkey="true" />
		<column name="pcaicode" title="信用证预排额度申请单内码" type="VARCHAR(SZIBILL)" />
		<column name="gvcode" title="商品小类" type="VARCHAR(SZGVCODE)" />
		<column name="fcy" title="排证金额" type="NUMERIC(MNYINT.MNYDEC)" />
		<column name="bcode" title="业务员部门" type="VARCHAR(SZBCODE)" />
		<index name="ft_lc_pcag_pcaicode" fields="pcaicode" storegrp="FTFUNDINDX_TBS"/>
	</table>

	<table id="30030" name="ft_lc_app" title="信用证开证申请" datasrcid="FT-FUND" rdatasrcid="FT-FUND" storegrp="FTFUNDDATA_TBS">
		<column name="lcaicode" title="信用证开证申请内码" type="VARCHAR(SZIBILL)" primkey="true" />
		<column name="lcaicodev" title="信用证开证申请原版本内码" type="VARCHAR(SZIBILL)"/>
		<column name="lcacode" title="开证申请单号" type="VARCHAR(SZNBILL)" notnull="true" />
		<column name="status" title="状态" type="VARCHAR(SZSTATUS)" />
		<column name="wfcode" title="审批编码" type="VARCHAR(SZWFCODE)" />
		<column name="wfuid" title="审批节点" type="VARCHAR(SZWFUID)" />
		<column name="bcode" title="业务员部门" type="VARCHAR(SZBCODE)" />
		<column name="wcode" title="业务员" type="VARCHAR(SZWCODE)" />
		<column name="corpbcode" title="公司" type="VARCHAR(SZBCODE)" />
		<column name="deptbcode" title="事业部" type="VARCHAR(SZBCODE)" />
		<column name="sheetcode" title="单据类型" type="VARCHAR(SZSHEET)" />
		<column name="cuicode" title="商户" type="VARCHAR(SZCUICODE)" />
		<column name="curratifydate" title="本版本生效时间" type="DATE" />
		<column name="ratifydate" title="生效时间" type="DATE" />
		<column name="submitdate" title="提交时间" type="DATE" />
		<column name="performdate" title="审核时间" type="DATE" />
		<column name="vsn" title="版本" type="INTEGER" />
		<column name="vsnflag" title="版本标记列名" type="INTEGER" />
		<columns cpFromTable="tplt_cm"/>
		<column name="lccode" title="信用证号" type="VARCHAR(SZLCCODE)" />
		<column name="lcdate" title="开证日期" type="DATE" />
		<column name="bank" title="开证银行" type="VARCHAR(64)" />
		<column name="bankacccode" title="开证银行账户" type="VARCHAR(SZBANKACCCODE)" />
		<column name="mergeflag" title="合并开证" type="SMALLINT" />
		<column name="changeflag" title="改证" type="SMALLINT" />
		<column name="retractflag" title="撤证" type="SMALLINT" />
		<column name="isreplc" title="是否代开证" type="CHAR(SZYN)" />
		<column name="lccorpbcode" title="开证公司" type="VARCHAR(SZBCODE)" />
		<column name="priceterm" title="贸易术语" type="VARCHAR(SZPRICETERM)" />
		<column name="fcy" title="开证金额" type="NUMERIC(MNYINT.MNYDEC)" />
		<column name="lctype" title="信用证类型" type="VARCHAR(SZRPCODE)" />
		<column name="days" title="天数" type="INTEGER" />
		<column name="vddate" title="有效日期" type="DATE" />
		<column name="fcode" title="币种" type="VARCHAR(SZFCODE)" />
		<column name="lsdate" title="最迟装船日期" type="DATE" />
		<column name="purccode" title="供应商" type="VARCHAR(SZCCODE)" />
		<column name="isosrate" title="是否溢短装" type="CHAR(SZYN)" />
		<column name="overshiprate" title="溢装率" type="NUMERIC(OSRINT.OSRDEC)" />
		<column name="adate" title="申请日期" type="DATE" />
		<column name="beneccode" title="受益人" type="VARCHAR(SZCCODE)" />
		<column name="benenameesc" title="受益人描述" type="VARCHAR(SZTEXT)" />
		<column name="plcdate" title="预计开证日期" type="DATE" />
		<column name="pcaicode" title="预排额度申请单内码" type="VARCHAR(SZIBILL)" />
		<column name="pcacode" title="预排额度申请单号" type="VARCHAR(SZNBILL)" />
		<column name="bankcode" title="排证银行" type="VARCHAR(SZBANKCODE)" />
		<column name="linkman" title="联系人" type="VARCHAR(SZUNAME)" />
		<column name="linkmanmobile" title="联系电话" type="VARCHAR(SZTELEPH)" />
		<column name="isneednextlc" title="是否需收到下家信用证" type="CHAR(SZYN)" />
		<column name="isneedmarg" title="是否需收到保证金" type="CHAR(SZYN)" />
		<column name="bkticode" title="开证银行模板内码" type="VARCHAR(SZIBILL)" />
		<column name="bktcode" title="开证银行模板" type="VARCHAR(SZNBILL)" />
		<column name="alctype" title="开证方式" type="VARCHAR(SZTYPE)" />
		<column name="remark" title="备注" type="VARCHAR(512)" />
		<column name="rbefstatus" title="闭卷撤回前状态" type="VARCHAR(SZSTATUS)" />
		<column name="clcdate" title="改证日期" type="DATE" />
		<column name="clcreason" title="改证原因" type="VARCHAR(SZTYPE)" />
		<column name="clctype" title="改证方式" type="VARCHAR(SZLTYPE)" />
		<column name="clcpayertype" title="改证付费人" type="VARCHAR(SZLCPAYERTYPE)" />
		<column name="clccontent" title="改证内容" type="VARCHAR(512)" />
		<column name="clcbeneficiary" title="改证受益人" type="VARCHAR(SZTEXT)" />
		<column name="clcreasonremark" title="改证原因备注" type="VARCHAR(512)" />
		<column name="rlcdate" title="撤证日期" type="DATE" />
		<column name="rlcpayertype" title="撤证付费人" type="VARCHAR(SZLCPAYERTYPE)" />
		<column name="rlcreason" title="撤证原因" type="VARCHAR(512)" />
		<column name="rlcreasonremark" title="撤证原因备注" type="VARCHAR(512)" />
		<column name="vsntype" title="版本修改类型" type="VARCHAR(SZTYPE)" />
		<column name="srcserialno" title="来源流水号" type="VARCHAR(SZIBILL)" />
		<column name="isfundcf" title="版本修改是否资金确认" type="CHAR(SZYN)" />
		<column name="isinit" title="是否初始化" type="CHAR(SZYN)"/>
		<index name="ft_lc_app_predate" fields="predate,cuicode,corpbcode,bcode,wcode" storegrp="FTFUNDINDX_TBS"/>
		<index name="ft_lc_app_lcacode" fields="lcacode,sheetcode,cuicode" storegrp="FTFUNDINDX_TBS"/>
	</table>
	<table id="30040" name="ft_lc_appg" title="信用证开证申请商品信息表" datasrcid="FT-FUND" rdatasrcid="FT-FUND" storegrp="FTFUNDDATA_TBS">
		<column name="lcagicode" title="信用证开证申请商品表内码" type="VARCHAR(SZIBILL)" primkey="true" />
		<column name="lcagicodev" title="信用证开证申请商品表原版本内码" type="VARCHAR(SZIBILL)"/>
		<column name="lcaicode" title="信用证开证申请内码" type="VARCHAR(SZIBILL)" notnull="true" />
		<column name="lcaicodev" title="信用证开证申请原版本内码" type="VARCHAR(SZIBILL)"/>
		<column name="purordicode" title="采购合同内码" type="VARCHAR(SZIBILL)" />
		<column name="purordcode" title="采购合同号" type="VARCHAR(SZNBILL)" />
		<column name="purordgicode" title="采购合同商品明细内码" type="VARCHAR(SZIBILL)" />
		<column name="idx" title="序号" type="INTEGER" />
		<column name="prjcode" title="业务编号" type="VARCHAR(SZNBILL)" />
		<column name="prjicode" title="业务内码" type="VARCHAR(SZIBILL)" />
		<column name="outordcode" title="采购外部合同号" type="VARCHAR(SZOUTCODE)" />
		<column name="fcode" title="币种" type="VARCHAR(SZFCODE)" />
		<column name="fcy" title="开证金额" type="NUMERIC(MNYINT.MNYDEC)" />
		<column name="upric" title="单价" type="NUMERIC(UPRINT.UPRDEC)" />
		<column name="ordfcy" title="原币金额" type="NUMERIC(MNYINT.MNYDEC)" />
		<column name="gcode" title="商品编码" type="VARCHAR(SZGCODE)" />
		<column name="cnamedesc" title="商品名称" type="VARCHAR(SZGNAME)" />
		<column name="enamedesc" title="商品英文名称" type="VARCHAR(SZGNAME)" />
		<column name="goodsdesc" title="货物描述" type="VARCHAR(SZTEXT)" />
		<!--库存属性-->
		<columns cpFromTable="sgattrgroup" />
		<column name="qtc" title="签约数量" type="NUMERIC(QTYINT.QTYDEC)" />
		<column name="qtcunit" title="签约单位" type="VARCHAR(SZUNIT)" />
		<column name="loadingportlist" title="装货港" type="VARCHAR(SZPORTCODELIST)" />
		<column name="dischargeportlist" title="卸货港" type="VARCHAR(SZPORTCODELIST)" />
		<column name="osctrlmode" title="合同溢短装控制方式" type="VARCHAR(SZTYPE)" />
		<column name="overshiprate" title="溢装率" type="NUMERIC(OSRINT.OSRDEC)" />
		<index name="ft_lc_appg_lcaicode" fields="lcaicode" storegrp="FTFUNDINDX_TBS" />
	</table>

	<table id="30050" name="ft_lc_purtolca_cb" title="采购合同对信用证开证核销余额表" datasrcid="FT-FUND" rdatasrcid="FT-FUND"
		storegrp="FTFUNDDATA_TBS">
		<column name="purordgicode" title="采购合同商品明细内码" type="VARCHAR(SZIBILL)" primkey="true" />
		<column name="corflag" title="核销标识" type="SMALLINT"/>
		<column name="fcy" title="原币金额|原值" type="NUMERIC(MNYINT.MNYDEC)" />
		<column name="fcyed" title="原币金额|已核" type="NUMERIC(MNYINT.MNYDEC)" />
		<column name="fcying" title="原币金额|未核" type="NUMERIC(MNYINT.MNYDEC)" />
		<column name="purordicode" title="采购合同内码" type="VARCHAR(SZIBILL)" />
		<column name="purordcode" title="采购合同号" type="VARCHAR(SZNBILL)" />
		<column name="cuicode" title="商户编码" type="VARCHAR(SZCUICODE)" />
		<column name="status" title="状态" type="VARCHAR(SZSTATUS)" />
		<column name="bcode" title="业务员部门" type="VARCHAR(SZBCODE)" />
		<column name="wcode" title="业务员" type="VARCHAR(SZWCODE)" />
		<column name="corpbcode" title="公司" type="VARCHAR(SZBCODE)" />
		<columns cpFromTable="tplt_cm"/>
		<column name="prjcode" title="业务编号" type="VARCHAR(SZNBILL)" />
		<column name="prjicode" title="业务内码" type="VARCHAR(SZIBILL)" />
		<column name="outordcode" title="采购外部合同号" type="VARCHAR(SZOUTCODE)" />
		<column name="signdate" title="签约日期" type="DATE" />
		<column name="idx" title="序号" type="INTEGER" />
		<column name="gcode" title="商品编码" type="VARCHAR(SZGCODE)" />
		<column name="cnamedesc" title="商品名称" type="VARCHAR(SZGNAME)" />
		<column name="enamedesc" title="商品英文名称" type="VARCHAR(SZGNAME)" />
		<column name="goodsdesc" title="货物描述" type="VARCHAR(SZTEXT)" />
		<columns cpFromTable="sgattrgroup" />
		<column name="qtc" title="签约数量" type="NUMERIC(QTYINT.QTYDEC)" />
		<column name="qtcunit" title="签约单位" type="VARCHAR(SZUNIT)" />
		<column name="upric" title="单价" type="NUMERIC(UPRINT.UPRDEC)" />
		<column name="fcode" title="币种" type="VARCHAR(SZFCODE)" />
		<column name="purccode" title="供应商" type="VARCHAR(SZCCODE)" />
		<column name="coopccode" title="合作方" type="VARCHAR(SZCCODE)"/>
		<column name="ccodetrust" title="委托方" type="VARCHAR(SZCCODE)"/>
		<column name="loadingportlist" title="装货港" type="VARCHAR(SZPORTCODELIST)" />
		<column name="dischargeportlist" title="卸货港" type="VARCHAR(SZPORTCODELIST)" />
		<column name="osctrlmode" title="合同溢短装控制方式" type="VARCHAR(SZTYPE)" />
		<column name="overshiprate" title="溢装率" type="NUMERIC(OSRINT.OSRDEC)" />
		<column name="priceterm" title="贸易术语" type="VARCHAR(SZPRICETERM)" />
		<column name="srcsheetcode" title="来源单据类型" type="VARCHAR(SZSHEET)" />
		<column name="srcicode" title="来源单据内码" type="VARCHAR(SZIBILL)" />
	</table>

	<table id="30060" name="ft_lc_purtolca_ct" title="采购合同对信用证开证核销记录表" datasrcid="FT-FUND" rdatasrcid="FT-FUND"
		storegrp="FTFUNDDATA_TBS">
		<column name="tgticode" title="目标表主键" type="VARCHAR(SZIBILL)" primkey="true" />
		<column name="tgttbl" title="目标表名" type="VARCHAR(SZTABLE)" primkey="true" />
		<column name="purordgicode" title="采购合同商品明细内码" type="VARCHAR(SZIBILL)" notnull="true" />
		<column name="fcy" title="原币金额" type="NUMERIC(MNYINT.MNYDEC)" />
		<index name="ft_lc_purtolca_ct_purordgicode" fields="purordgicode" storegrp="FTFUNDINDX_TBS" />
	</table>


	<table id="30070" name="ft_lc_bkt" title="开证银行模板表" datasrcid="FT-FUND" rdatasrcid="FT-FUND" storegrp="FTFUNDDATA_TBS">
		<column name="bkticode" title="开证银行模板内码" type="VARCHAR(SZIBILL)" primkey="true" />
		<column name="bktcode" title="开证银行模板编码" type="VARCHAR(SZNBILL)" />
		<column name="cuicode" title="商户" type="VARCHAR(SZCUICODE)" />
		<column name="sheetcode" title="单据类型" type="VARCHAR(SZSHEET)" />
		<column name="bankcode" title="外部银行" type="VARCHAR(SZBANKCODE)" />
		<column name="sncitycode" title="市及地区" type="VARCHAR(MDMCODE)" />
		<column name="remark" title="备注" type="VARCHAR(SZTEXT)" />
		<column name="status" title="状态" type="VARCHAR(SZSTATUS)" />
		<columns cpFromTable="tplt_cm"/>
	</table>


	<table id="30080" name="ft_lc_reg" title="信用证到证登记及认领主表" datasrcid="FT-FUND" rdatasrcid="FT-FUND" storegrp="FTFUNDDATA_TBS">
		<column name="lcregicode" title="到证登记及认领内码" type="VARCHAR(SZIBILL)" primkey="true"/>
		<column name="lcregicodev" title="到证登记及认领原原版本内码" type="VARCHAR(SZIBILL)"/>
		<column name="lcregcode" title="到证登记及认领单号" type="VARCHAR(SZNBILL)" notnull="true"/>
		<column name="status" title="状态" type="VARCHAR(SZSTATUS)" />
		<column name="wfcode" title="审批编码" type="VARCHAR(SZWFCODE)" />
		<column name="wfuid" title="审批节点" type="VARCHAR(SZWFUID)" />
		<column name="bcode" title="业务员部门" type="VARCHAR(SZBCODE)" />
		<column name="wcode" title="业务员" type="VARCHAR(SZWCODE)" />
		<column name="corpbcode" title="公司" type="VARCHAR(SZBCODE)" />
		<column name="sheetcode" title="单据类型" type="VARCHAR(SZSHEET)" />
		<column name="cuicode" title="商户" type="VARCHAR(SZCUICODE)" />
		<column name="curratifydate" title="本版本生效时间" type="DATE" />
		<column name="ratifydate" title="生效时间" type="DATE" />
		<column name="submitdate" title="提交时间" type="DATE" />
		<column name="performdate" title="审核时间" type="DATE" />
		<column name="vsn" title="版本" type="INTEGER" />
		<column name="vsnflag" title="版本标记列名" type="INTEGER" />
		<column name="vsntype" title="版本修改类型" type="VARCHAR(SZTYPE)" />
		<columns cpFromTable="tplt_cm"/>
		<column name="lcdate" title="开证日期" type="DATE" />
		<column name="lccode" title="信用证号" type="VARCHAR(SZLCCODE)" />
		<column name="lctype" title="信用证类型" type="VARCHAR(SZRPCODE)" />
		<column name="fardays" title="远期天数" type="INTEGER" />
		<column name="vddate" title="有效日期" type="DATE" />
		<column name="rldate" title="收证日期" type="DATE" />
		<column name="fcode" title="收证币种" type="VARCHAR(SZFCODE)" />
		<column name="fcy" title="收证金额" type="NUMERIC(MNYINT.MNYDEC)" />
		<column name="isosrate" title="是否溢短装" type="CHAR(SZYN)" />
		<column name="overshiprate" title="溢装率" type="NUMERIC(OSRINT.OSRDEC)" />
		<column name="salccode" title="客户" type="VARCHAR(SZCCODE)" />
		<column name="priceterm" title="贸易术语" type="VARCHAR(SZPRICETERM)" />
		<column name="lsdate" title="最迟装运日期" type="DATE" />
		<column name="lsddate" title="最迟交单日期" type="DATE" />
		<column name="lcaccode" title="开证申请人" type="VARCHAR(SZCCODE)" />
		<column name="aulcdate" title="审证日期" type="DATE" />
		<column name="remark" title="备注" type="VARCHAR(SZTEXT)" />
		<column name="lcbank" title="开证行" type="VARCHAR(SZBANKNAME)" />
		<column name="swiftcode" title="SWIFTCODE" type="VARCHAR(SZBANKCODE)" />
		<column name="ntbankcode" title="通知行" type="VARCHAR(SZBANKCODE)" />
		<column name="clcucode" title="审证人" type="VARCHAR(SZUCODE)" />
		<column name="cantransflag" title="可转让信用证" type="SMALLINT" />
		<column name="canrevocflag" title="可撤销信用证" type="SMALLINT" />
		<column name="secbeneflag" title="第二受益人" type="SMALLINT" />
		<column name="lastdocflag" title="最后到证" type="SMALLINT" />
		<column name="changeflag" title="改证" type="SMALLINT" />
		<column name="retractflag" title="撤证" type="SMALLINT" />
		<column name="clcdate" title="改证日期" type="DATE" />
		<column name="clcreason" title="改证原因" type="VARCHAR(SZTYPE)" />
		<column name="clctype" title="改证方式" type="VARCHAR(SZLTYPE)" />
		<column name="clcpayertype" title="改证付费人" type="VARCHAR(SZLCPAYERTYPE)" />
		<column name="clccontent" title="改证内容" type="VARCHAR(SZTEXT)" />
		<column name="clcbeneficiary" title="改证受益人" type="VARCHAR(SZTEXT)" />
		<column name="clcreasonremark" title="改证原因备注" type="VARCHAR(SZTEXT)" />
		<column name="rlcdate" title="撤证日期" type="DATE" />
		<column name="rlcpayertype" title="撤证付费人" type="VARCHAR(SZLCPAYERTYPE)" />
		<column name="rlcreason" title="撤证原因" type="VARCHAR(SZTYPE)" />
		<column name="rlcreasonremark" title="撤证原因备注" type="VARCHAR(SZTEXT)" />
		<column name="domabr" title="境内外" type="VARCHAR(SZDOMABR)" />
		<column name="srcserialno" title="来源流水号" type="VARCHAR(SZIBILL)" />
		<column name="isinit" title="是否初始化" type="CHAR(SZYN)"/>
		<index name="ft_lc_reg_predate" fields="predate,cuicode,corpbcode,bcode,wcode" storegrp="FTFUNDINDX_TBS"/>
		<index name="ft_lc_reg_lcregcode" fields="lcregcode,sheetcode,cuicode" storegrp="FTFUNDINDX_TBS"/>
	</table>
	<table id="30090" name="ft_lc_regg" title="信用证到证登记及认领明细表" datasrcid="FT-FUND" rdatasrcid="FT-FUND" storegrp="FTFUNDDATA_TBS">
		<column name="lcreggicode" title="明细表内码" type="VARCHAR(SZIBILL)" primkey="true"/>
		<column name="lcreggicodev" title="到证登记及认领明细原版本内码" type="VARCHAR(SZIBILL)"/>
		<column name="lcregicode" title="到证登记及认领内码" type="VARCHAR(SZIBILL)" notnull="true"/>
		<column name="lcregicodev" title="到证登记及认领原版本内码" type="VARCHAR(SZIBILL)"/>
		<column name="salordssicode" title="销售合同收付分摊表内码" type="VARCHAR(SZIBILL)" />
		<column name="idx" title="序号" type="INTEGER" />
		<column name="salordicode" title="销售合同内码" type="VARCHAR(SZIBILL)" />
		<column name="salordcode" title="销售合同号" type="VARCHAR(SZNBILL)" />
		<column name="outordcode" title="销售外部合同号" type="VARCHAR(SZOUTCODE)" />
		<column name="prjcode" title="业务编号" type="VARCHAR(SZNBILL)" />
		<column name="prjicode" title="业务内码" type="VARCHAR(SZIBILL)" />
		<column name="fcode" title="币种" type="VARCHAR(SZFCODE)" />
		<column name="ordfcy" title="原币金额" type="NUMERIC(MNYINT.MNYDEC)" />
		<column name="fcy" title="认领金额" type="NUMERIC(MNYINT.MNYDEC)" />
		<column name="remark" title="备注" type="VARCHAR(SZTEXT)" />
		<index name="ft_lc_regg_lcregicode" fields="lcregicode" storegrp="FTFUNDINDX_TBS"/>
	</table>

	<table id="30100" name="ft_lc_reggs" title="信用证到证登记及认领销售合同明细表" datasrcid="FT-FUND" rdatasrcid="FT-FUND" storegrp="FTFUNDDATA_TBS">
		<column name="lcreggsicode" title="孙表内码" type="VARCHAR(SZIBILL)" primkey="true"/>
		<column name="lcreggsicodev" title="单证要求表原版本内码" type="VARCHAR(SZIBILL)"/>
		<column name="lcreggicode" title="明细表内码" type="VARCHAR(SZIBILL)" notnull="true"/>
		<column name="lcreggicodev" title="明细表原版本内码" type="VARCHAR(SZIBILL)"/>
		<column name="lcregicode" title="到证登记及认领内码" type="VARCHAR(SZIBILL)" notnull="true"/>
		<column name="lcregicodev" title="到证登记及认领原版本内码" type="VARCHAR(SZIBILL)"/>
		<column name="idx" title="序号" type="INTEGER" />
		<column name="salordgicode" title="销售合同子表内码" type="VARCHAR(SZIBILL)" />
		<column name="salordicode" title="销售合同内码" type="VARCHAR(SZIBILL)" />
		<column name="salordcode" title="销售合同号" type="VARCHAR(SZNBILL)" />
		<column name="outordcode" title="销售外部合同号" type="VARCHAR(SZOUTCODE)" />
		<column name="prjcode" title="业务编号" type="VARCHAR(SZNBILL)" />
		<column name="prjicode" title="业务内码" type="VARCHAR(SZIBILL)" />
		<column name="ordfcy" title="原币金额" type="NUMERIC(MNYINT.MNYDEC)" />
		<column name="fcy" title="认领金额" type="NUMERIC(MNYINT.MNYDEC)" />
		<column name="gcode" title="商品编码" type="VARCHAR(SZGCODE)" />
		<column name="cnamedesc" title="商品名称" type="VARCHAR(SZGNAME)" />
		<column name="enamedesc" title="商品英文名称" type="VARCHAR(SZGNAME)" />
		<column name="specifidesc" title="规格型号" type="VARCHAR(SZSPECIFIDESC)" />
		<column name="goodsdesc" title="货物描述" type="VARCHAR(SZTEXT)" />
		<!--库存属性-->
		<columns cpFromTable="sgattrgroup" />
		<column name="qtc" title="签约数量" type="NUMERIC(QTYINT.QTYDEC)" />
		<column name="qtcunit" title="签约单位" type="VARCHAR(SZUNIT)" />
		<column name="upric" title="单价" type="NUMERIC(UPRINT.UPRDEC)" />
		<index name="ft_lc_reggs_lcregicode" fields="lcregicode" storegrp="FTFUNDINDX_TBS"/>
		<index name="ft_lc_reggs_lcreggicode" fields="lcreggicode" storegrp="FTFUNDINDX_TBS"/>
	</table>

	<table id="30110" name="ft_lc_soreg_cb" title="销售合同对信用证到证登记及认领核销余额表" datasrcid="FT-FUND" rdatasrcid="FT-FUND" storegrp="FTFUNDDATA_TBS">
		<column name="salordssicode" title="销售合同收付分摊表内码" type="VARCHAR(SZIBILL)" primkey="true"/>
		<column name="corflag" title="核销标识" type="SMALLINT"/>
		<column name="fcy" title="原币金额|原值" type="NUMERIC(MNYINT.MNYDEC)" />
		<column name="fcyed" title="原币金额|已核销" type="NUMERIC(MNYINT.MNYDEC)" />
		<column name="fcying" title="原币金额|未核销" type="NUMERIC(MNYINT.MNYDEC)" />
		<column name="salordicode" title="销售合同内码" type="VARCHAR(SZIBILL)" />
		<column name="salordcode" title="销售合同号" type="VARCHAR(SZNBILL)" />
		<column name="outordcode" title="销售外部合同号" type="VARCHAR(SZOUTCODE)" />
		<column name="prjcode" title="业务编号" type="VARCHAR(SZNBILL)" />
		<column name="prjicode" title="业务内码" type="VARCHAR(SZIBILL)" />
		<column name="cuicode" title="商户" type="VARCHAR(SZCUICODE)" />
		<column name="status" title="状态" type="VARCHAR(SZSTATUS)" />
		<column name="bcode" title="业务员部门" type="VARCHAR(SZBCODE)" />
		<column name="wcode" title="业务员" type="VARCHAR(SZWCODE)" />
		<column name="corpbcode" title="公司" type="VARCHAR(SZBCODE)" />
		<columns cpFromTable="tplt_cm"/>
		<column name="signdate" title="签约日期" type="DATE" />
		<column name="salccode" title="客户" type="VARCHAR(SZCCODE)" />
		<column name="fcode" title="币种" type="VARCHAR(SZFCODE)" />
		<column name="domabr" title="境内外" type="VARCHAR(SZDOMABR)" />
		<column name="rldate" title="最迟收证日期" type="DATE" />
		<column name="overshiprate" title="溢装率" type="NUMERIC(OSRINT.OSRDEC)" />
		<column name="priceterm" title="贸易术语" type="VARCHAR(SZPRICETERM)" />
		<column name="paymode" title="收款方式" type="VARCHAR(SZRPCODE)" />
		<column name="srcsheetcode" title="来源单据类型" type="VARCHAR(SZSHEET)" />
		<column name="srcicode" title="来源单据内码" type="VARCHAR(SZIBILL)" />
	</table>

	<table id="30120" name="ft_lc_regd" title="信用证到证登记及认领单证要求表" datasrcid="FT-FUND" rdatasrcid="FT-FUND" storegrp="FTFUNDDATA_TBS">
		<column name="lcregdicode" title="单证要求表内码" type="VARCHAR(SZIBILL)" primkey="true"/>
		<column name="lcregdicodev" title="单证要求表原版本内码" type="VARCHAR(SZIBILL)"/>
		<column name="lcregicode" title="到证登记及认领内码" type="VARCHAR(SZIBILL)" notnull="true"/>
		<column name="lcregicodev" title="单证要求表原版本内码" type="VARCHAR(SZIBILL)"/>
		<column name="idx" title="序号" type="INTEGER"/>
		<column name="docname" title="单证名称" type="VARCHAR(SZTYPE)"/>
		<column name="nums" title="份数" type="INTEGER"/>
		<column name="remark" title="备注" type="VARCHAR(SZTEXT)"/>
		<index name="ft_lc_regd_lcregicode" fields="lcregicode" storegrp="FTFUNDINDX_TBS"/>
	</table>

	<table id="30130" name="ft_lc_soreg_ct" title="销售合同对信用证到证登记及认领核销记录表" datasrcid="FT-FUND" rdatasrcid="FT-FUND" storegrp="FTFUNDDATA_TBS">
		<column name="tgticode" title="目标表主键" type="VARCHAR(SZIBILL)" primkey="true"/>
		<column name="tgttbl" title="目标表名" type="VARCHAR(SZTABLE)" primkey="true"/>
		<column name="salordssicode" title="销售合同收付分摊表内码" type="VARCHAR(SZIBILL)" notnull="true"/>
		<column name="fcy" title="原币金额" type="NUMERIC(MNYINT.MNYDEC)" />
		<index name="ft_lc_soreg_ct_salordssicode" fields="salordssicode" storegrp="FTFUNDINDX_TBS"/>
	</table>
</db>
