Xjs.loadedXjs.push("snsoft/ft/fund/forex/sesa");
/*snsoft/ft/forex/sesa/invoker/ForexSpotExcSettAppGDetailCopyJSInvoker.java*/
Xjs.namespace("snsoft.ft.forex.sesa.invoker");
snsoft.ft.forex.sesa.invoker.ForexSpotExcSettAppGDetailCopyJSInvoker=function(parameter){
    snsoft.ft.forex.sesa.invoker.ForexSpotExcSettAppGDetailCopyJSInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.forex.sesa.invoker.ForexSpotExcSettAppGDetailCopyJSInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.forex.sesa.invoker.ForexSpotExcSettAppGDetailCopyJSInvoker",
    /*snsoft.ft.forex.sesa.invoker.ForexSpotExcSettAppGDetailCopyJSInvoker.beforeCheck*/
    beforeCheck:function(event)
    {
        var gTable = Xjs.util.TableUtils.getTable_$Panel_$String(this.mainTable,"ft_forex_sesag");
        if(gTable.getDataSet().getRowCount() == 0 || gTable.getDataSet().isNewRow())
        {
            throw new Error(Xjs.ResBundle.getResVal("FT.00000089",gTable.title));
        }
        return null;
    },
    /*snsoft.ft.forex.sesa.invoker.ForexSpotExcSettAppGDetailCopyJSInvoker.check*/
    check:function(event)
    {
        return this.newDialogWithFunc(event,(e)=>{
            var dialogPane = Xjs.ui.UIUtil.loadDialog("FT-FOREX.ForexExpObjWorkBench");
            dialogPane.setWidth("auto");
            dialogPane.setHeight("80%");
            dialogPane.title = event.cfg.title;
            var queryPane = dialogPane.getItemByName("param");
            queryPane.getItemByName("corpbcode").setValue(this.mainDataSet.getValue("corpbcode"));
            queryPane.getItemByName("corpbcode").setReadonly(true);
            queryPane.getItemByName("bcode").setValue(this.mainDataSet.getValue("bcode"));
            queryPane.getItemByName("bcode").setReadonly(true);
            queryPane.getItemByName("fcode").setValue(this.mainDataSet.getValue("fcode"));
            queryPane.getItemByName("fcode").setReadonly(true);
            queryPane.getItemByName("salordcode").setValue(Xjs.util.TableUtils.getTable_$Panel_$String(this.mainTable,"ft_forex_sesag").getDataSet().getValue("salordcode"));
            queryPane.getItemByName("salordcode").setReadonly(true);
            dialogPane.showModal();
            var dlgtbl = dialogPane.getItemByName("ft_forex_expost_cb_view");
            Xjs.ui.Menu.setNodeVisible(dlgtbl.getMenuNode("copyWork").node,false);
            return dialogPane;
        },(e,d)=>{
            var srcTable = snsoft.ft.utils.FTUtils.getTableFromPanel(d),
                rows = srcTable.getSelectedRowNumbers(),
                invokeParam = this.buildInvokeParam(null),
                exposticodes = [];
            for(var i=0;i < rows.length;i++)
            {
                var exposticode = srcTable.getDataSet().getValue("exposticode",rows[i]);
                if(exposticode)
                {
                    exposticodes.push(exposticode);
                }
            }
            invokeParam.exposticodes = exposticodes;
            invokeParam.sesaicode = this.mainDataSet.getValue("sesaicode");
            invokeParam.sesagicode = Xjs.util.TableUtils.getTable_$Panel_$String(this.mainTable,"ft_forex_sesag").getDataSet().getValue("sesagicode");
            event.checkData.param = invokeParam;
        });
    },
    /*snsoft.ft.forex.sesa.invoker.ForexSpotExcSettAppGDetailCopyJSInvoker.invoke*/
    invoke:function(event)
    {
        return event.checkData.param;
    },
    /*snsoft.ft.forex.sesa.invoker.ForexSpotExcSettAppGDetailCopyJSInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        this.dataSet.refresh();
        this.mainTable.refreshTable();
    }
});
/*snsoft/ft/forex/sesa/invoker/ForexSpotExcSettAppGenerateExchangeJSInvoker.java*/
snsoft.ft.forex.sesa.invoker.ForexSpotExcSettAppGenerateExchangeJSInvoker=function(parameter){
    snsoft.ft.forex.sesa.invoker.ForexSpotExcSettAppGenerateExchangeJSInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.forex.sesa.invoker.ForexSpotExcSettAppGenerateExchangeJSInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.forex.sesa.invoker.ForexSpotExcSettAppGenerateExchangeJSInvoker",
    /*snsoft.ft.forex.sesa.invoker.ForexSpotExcSettAppGenerateExchangeJSInvoker.beforeCheck*/
    beforeCheck:function(event)
    {
        var invokeParam = this.buildInvokeParam(null);
        invokeParam.invokeMethod = "beforeCheck";
        invokeParam.sesaicode = this.dataSet.getValue("sesaicode");
        return invokeParam;
    },
    /*snsoft.ft.forex.sesa.invoker.ForexSpotExcSettAppGenerateExchangeJSInvoker.check*/
    check:function(event)
    {
        var beforeCheckRtnVal = event.beforeCheckRtnVal;
        if(beforeCheckRtnVal)
        {
            var sesaicodes = beforeCheckRtnVal.sesaicodes;
            if(sesaicodes)
            {
                var sheetService = Xjs.RInvoke.newBean("SN-Busi.SheetService"),
                    busiObject = sheetService.getBusiObject("FT-FOREX.ForexSpotExcSettApp"),
                    data = {sheetcode:busiObject.sheetcode};
                data[busiObject.innerfld] = sesaicodes;
                data.sheetEntry = true;
                snsoft.plat.bas.viewdetail.ViewDetailListener.invokeViewDetail(data,busiObject.outerfld);
                return null;
            }
        }
        var btnTitle = snsoft.ft.utils.FTUtils.getBtnTitle(event.table,event.cfg.cmd),
            message = Xjs.ResBundle.getResVal("FT-FOREX.10000002");
        return {type:0,title:btnTitle,prompt:message};
    },
    /*snsoft.ft.forex.sesa.invoker.ForexSpotExcSettAppGenerateExchangeJSInvoker.invoke*/
    invoke:function(event)
    {
        var invokeParam = this.buildInvokeParam(null);
        invokeParam.invokeMethod = "invoke";
        invokeParam.sesaicode = this.dataSet.getValue("sesaicode");
        return invokeParam;
    },
    /*snsoft.ft.forex.sesa.invoker.ForexSpotExcSettAppGenerateExchangeJSInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        var invokeRtnVal = event.invokeRtnVal;
        if(invokeRtnVal)
        {
            var sesaicode = invokeRtnVal.sesaicode;
            if(sesaicode)
            {
                var sheetService = Xjs.RInvoke.newBean("SN-Busi.SheetService"),
                    busiObject = sheetService.getBusiObject("FT-FOREX.ForexSpotExcSettApp"),
                    data = {sheetcode:busiObject.sheetcode};
                data[busiObject.innerfld] = sesaicode;
                data.sheetEntry = true;
                snsoft.plat.bas.viewdetail.ViewDetailListener.invokeViewDetail(data,busiObject.outerfld);
            }
        }
    }
});
/*snsoft/ft/forex/sesa/invoker/ForexSpotExcSettAppIscasexpJSInvoker.java*/
snsoft.ft.forex.sesa.invoker.ForexSpotExcSettAppIscasexpJSInvoker=function(parameter){
    snsoft.ft.forex.sesa.invoker.ForexSpotExcSettAppIscasexpJSInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.forex.sesa.invoker.ForexSpotExcSettAppIscasexpJSInvoker,snsoft.ft.comm.cmdreg.CellsModifyInvoker,{
  _js$className_:"snsoft.ft.forex.sesa.invoker.ForexSpotExcSettAppIscasexpJSInvoker",
    /*snsoft.ft.forex.sesa.invoker.ForexSpotExcSettAppIscasexpJSInvoker.onClick*/
    onClick:function(event)
    {
        var gsTable = Xjs.util.TableUtils.getTable_$Panel_$String(this.mainTable,"ft_forex_sesags"),
            iscasexp = this.dataSet.getValue("iscasexp");
        if(iscasexp == "Y" && gsTable.getDataSet().getRowCount() != 0 && !gsTable.getDataSet().isNewRow())
        {
            return {type:0,title:this.getTitle(),prompt:Xjs.ResBundle.getResVal("FT-FOREX.10000001")};
        }
        return null;
    },
    /*snsoft.ft.forex.sesa.invoker.ForexSpotExcSettAppIscasexpJSInvoker.check*/
    check:function(event)
    {
        return snsoft.ft.forex.sesa.invoker.ForexSpotExcSettAppIscasexpJSInvoker.superclass.check.call(this,event);
    },
    /*snsoft.ft.forex.sesa.invoker.ForexSpotExcSettAppIscasexpJSInvoker.invoke*/
    invoke:function(event)
    {
        var jsObject = event.checkData.defvaluesCol;
        jsObject.sesaicode = this.mainDataSet.getValue("sesaicode");
        return jsObject;
    },
    /*snsoft.ft.forex.sesa.invoker.ForexSpotExcSettAppIscasexpJSInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        this.mainTable.refreshTable();
    }
});
/*snsoft/ft/forex/sesa/invoker/ForexSpotExcSettAppOpenExchangeJSInvoker.java*/
snsoft.ft.forex.sesa.invoker.ForexSpotExcSettAppOpenExchangeJSInvoker=function(parameter){
    snsoft.ft.forex.sesa.invoker.ForexSpotExcSettAppOpenExchangeJSInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.forex.sesa.invoker.ForexSpotExcSettAppOpenExchangeJSInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.forex.sesa.invoker.ForexSpotExcSettAppOpenExchangeJSInvoker",
    /*snsoft.ft.forex.sesa.invoker.ForexSpotExcSettAppOpenExchangeJSInvoker.invoke*/
    invoke:function(event)
    {
        var invokeParam = this.buildInvokeParam(null);
        invokeParam.sesaicode = this.dataSet.getValue("sesaicode");
        return invokeParam;
    },
    /*snsoft.ft.forex.sesa.invoker.ForexSpotExcSettAppOpenExchangeJSInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        var invokeRtnVal = event.invokeRtnVal;
        if(invokeRtnVal)
        {
            var sesaicodes = invokeRtnVal.sesaicodes;
            if(sesaicodes)
            {
                var sheetService = Xjs.RInvoke.newBean("SN-Busi.SheetService"),
                    busiObject = sheetService.getBusiObject("FT-FOREX.ForexSpotExcSettApp"),
                    data = {sheetcode:busiObject.sheetcode};
                data[busiObject.innerfld] = sesaicodes;
                data.sheetEntry = true;
                snsoft.plat.bas.viewdetail.ViewDetailListener.invokeViewDetail(data,busiObject.outerfld);
            }
        }
    }
});
