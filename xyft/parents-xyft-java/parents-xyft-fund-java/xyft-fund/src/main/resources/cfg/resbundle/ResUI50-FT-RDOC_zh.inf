## 单据类型
title_FT-RDOC.RdocSign=应收票据签收工作台
title_FT-RDOC.RdocClaimWork=应收票据认领工作台
title_FT-RDOC.RdocClaim=收票认领单
title_FT-RDOC.DistApp=贴现申请单
title_FT-RDOC.RdocDist=贴现结果记录
title_FT-RDOC.TransRate=应收票据转移费率维护
title_FT-RDOC.TransOut=票据转出单
title_FT-RDOC.TransIn=票据转入单
title_FT-RDOC.RdocCollWork=票据托收处理工作台
title_FT-RDOC.RdocColl=应收票据托收
title_FT-RDOC.RdocPle=票据质押处理工作台
title_FT-RDOC.RdocLock=应收票据锁定工作台
title_FT-RDOC.RdocQuery=应收票据台账查询
title_FT-RDOC.RdocDistVMark=票据贴现凭证
title_FT-RDOC.RdocDistDueVMark=票据贴现到期凭证
title_FT-RDOC.RdocCollVouVmark=应付票据托收凭证
title_FT-RDOC.TransOutVouVmark=票据转出单凭证
title_FT-RDOC.TransInVmark=票据转入单凭证
title_FT-RDOC.RdocAcbk=承兑人银行大行分类对应关系维护
title_FT-RDOC.RdocDistInintVMark=应收票据贴现到期凭证（初始化）
title_FT-RDOC.RdocEndorseInitVMark=应收票据背书付款到期凭证（初始化）

## 入口
title_FT-RDOC.DistAppEntry=贴现申请单-入口
title_FT-RDOC.TransOutEntry=票据转出单-入口
title_FT-RDOC.TransInEntry=票据转入单-入口

## 详情
title_FT-RDOC.DistAppDetail=贴现申请单
title_FT-RDOC.TransOutDetail=票据转出单
title_FT-RDOC.TransInDetail=票据转入单

## 工作台
title_FT-RDOC.RdocSignWorkBench=应收票据签收工作台
title_FT-RDOC.DistAppWorkBench=票据贴现申请工作台
title_FT-RDOC.TransOutWorkBench=票据转移申请工作台
title_FT-RDOC.RdocCollWorkBench=票据托收处理工作台
title_FT-RDOC.RdocLockWorkBench=应收票据锁定工作台
title_FT-RDOC.PayAppAddRecDoc=票据背书申请工作台
title_FT-RDOC.RdocDistWorkBench=票据贴现入账处理工作台
title_FT-RDOC.RdocDistDueWorkBench=票据贴现到期入账工作台
title_FT-RDOC.RdocQuery=应收票据台账查询
title_FT-RDOC.RdocClaimWorkBench=应收票据认领工作台
title_FT-RDOC.RdocCollVouWorkBench=应收票据托收凭证
title_FT-RDOC.RdocTransOutVouWorkBench=票据转出单凭证
title_FT-RDOC.RdocTransInVouWorkBench=票据转入单凭证
title_FT-RDOC.RecDocInit=应收票据认领工作台(初始化)
title_FT-RDOC.RdocEndoDistWorkBench=有追索应收票据背书/贴现到期凭证(初始化)

## 标签页
title.F.tab.rdocsign=应收票据
title.F.tab.rdocsigng=签收明细
title.F.tab.transoutg=转出明细
title.F.tab.transing=转入明细
title.F.tab.recdocg=应收票据分摊明细
title.F.tab.transoutfee=转出财务费用
title.F.tab.transinfee=转入财务费用
title.F.tab.rdoccoll=托收处理明细
title.F.tab.rdocple=质押处理明细
title.F.tab.rdocquery=应收票据流转信息
title.F.tab.rdoclock=锁定信息
title.F.tab.rdocdistg=票据明细信息
title.F.tab.rdocdistfee=贴现费用明细
title.F.tab.rdocclaimwork=应收票据
title.F.tab.rdocclaim=收款认领明细
title.F.tab.rdocshare=应收票据分摊
title.F.tab.rdocsource=应收票据分摊来源

## 按钮
FT.cmd_rdocsign=票据签收
FT.cmd_rdocSignSubmits=应收票据批量提交
FT.cmd_createTgbSubmit=创建同审单
FT.cmd_openClaimSign=打开收款认领
FT.cmd_genDistApp=生成贴现申请单
FT.cmd_addDistg=追加应收票据
FT.cmd_sendSerCancel=已送接口撤回
FT.cmd_cancleLock=取消锁定
FT.cmd_createDistApp=生成贴现申请单
FT.cmd_createTransOut=生成票据转出单
FT.cmd_backATS=退回ATS
FT.cmd_createAmort=生成费用摊销
FT.cmd_genAdvRecOffRdoc=生成预收款冲销单

## 特殊状态定义
status_501=待转入