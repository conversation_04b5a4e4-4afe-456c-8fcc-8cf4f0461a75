<?xml version="1.0" encoding="UTF-8"?>
<db id="FT-RDOC_View" title="应收票据视图" xmlns="http://www.snsoft.com.cn/schema/CreateView" xsi:schemaLocation="http://www.snsoft.com.cn/schema/CreateView http://www.snsoft.com.cn/schema/CreateView.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

	<view name="ft_rdoc_dists_view" title="票据贴现入账处理工作台视图" datasrcid="FT-FUND" viewexpr="票据贴现入账处理工作台视图">
		<![CDATA[
			select
				s.rdocdistsicode rdocdistsicode,s.rdocdisticode rdocdisticode,s.rdocdistgicode rdocdistgicode,s.rdocdistsicoder rdocdistsicoder,
				s.redflag redflag,s.sheetcode sheetcode,s.cuicode cuicode,
				v.vmarkicode vmarkicode,v.sheetcode vousheetcode,v.status status,
				v.vidflag vidflag,v.batchvid batchvid,m.corpbcode corpbcode,v.keptbcode keptbcode,v.tcaptime tcaptime,m.rdocdistcode rdocdistcode,
				g.diststatus diststatus,m.adate adate,g.docno docno,g.doctype doctype,g.subdocfm subdocfm,g.subdocto subdocto,g.iscansplit iscansplit,
				m.isdircon isdircon,g.drawer drawer,g.rdoccode rdoccode,g.rdocicode rdocicode,g.docfcy docfcy,r.duedate duedate,g.fcy fcy,s.distfcy distfcy,
				s.distpayer distpayer,s.distpayerbank distpayerbank,s.distbankcode distbankcode,s.distbankacccode distbankacccode,s.isrecourse isrecourse,r.isfundcorp isfundcorp,
				s.irtpaytype irtpaytype,s.distrate distrate,s.distinterest distinterest,s.distdate distdate,s.payirtmode payirtmode,s.bankno bankno,
				s.distirtbankno distirtbankno,s.fserate fserate,s.scerate scerate,s.suerate suerate,s.distfcyscy distfcyscy,s.distirtscy distirtscy,
				m.wcode wcode,v.bcode bcode,v.vprepare vprepare,v.predate predate,s.modifier modifier,s.modifydate modifydate
			from
				ft_rdoc_dists s
				inner join ft_fund_vmark v on s.rdocdistsicode = v.srcicode
				inner join ft_rdoc_dist m on s.rdocdisticode = m.rdocdisticode
				inner join ft_rdoc_distg g on s.rdocdistgicode = g.rdocdistgicode
				inner join ft_rdoc_rdoc r on s.rdocicode = r.rdocicode
		]]>
	</view>

	<view name="ft_rdoc_collvou_view" title="应收票据托收凭证视图" datasrcid="FT-FUND" viewexpr="应收票据托收凭证视图">
		<![CDATA[
			select
				v.vmarkicode,
				v.srcsheetcode sheetcode,
				v.sheetcode srcsheetcode,
				v.vidflag,
				g.docno,
				m.doctype,
				g.collbankcode,
				m.drawer,
				v.keptbcode,
				v.tcaptime,
				v.vprepare,
				v.predate,
				v.batchvid,
				m.duedate,
				m.ccode,
				g.bcode,
				g.wcode,
				m.fcy,
				g.fcy vcollfcy,
				g.bankno,
				v.corpbcode,
				m.rdocicode,
				g.colldate,
				g.modifier,
				g.modifydate,
				v.year,
				v.month,
				g.cuicode,
				m.isfundcorp,
				g.collbankacccode,
				m.rdoccode,
				g.rdoccollicode
			from
				ft_rdoc_rdoc m
				inner join ft_rdoc_coll g on m.rdocicode = g.rdocicode
				inner join ft_fund_vmark v on g.rdoccollicode = v.srcicode
		]]>
	</view>

	<view name="ft_rdoc_tsoutvou_view" title="票据转出单凭证视图" datasrcid="FT-FUND" viewexpr="票据转出单凭证视图">
		<![CDATA[
			select v.vmarkicode,v.srcsheetcode sheetcode,v.sheetcode srcsheetcode,v.vidflag ,m.rdoctsoutcode,m.rdoctsouticode,m.tsoutdate,m.redflag,
			v.corpbcode ,v.bcode ,v.keptbcode ,v.tcaptime ,v.vprepare ,v.predate,v.cuicode,m.modifier,m.modifydate,v.year,v.month,v.batchvid,m.status
		from ft_rdoc_tsout m inner join ft_rdoc_rdocg g on m.rdoctsouticode = g.srcicode inner join ft_fund_vmark v on m.rdoctsouticode = v.srcicode
		]]>
	</view>

	<view name="ft_rdoc_tsinvou_view" title="票据转入单凭证视图" datasrcid="FT-FUND" viewexpr="票据转入单凭证视图">
		<![CDATA[
		select v.vmarkicode,v.srcsheetcode sheetcode,v.sheetcode srcsheetcode,v.vidflag ,m.rdoctsincode,m.rdoctsinicode,m.tsindate,m.redflag,v.bcode,
			v.corpbcode ,v.keptbcode ,v.tcaptime ,v.vprepare ,v.predate,v.cuicode,m.modifier,m.modifydate,v.year,v.month,v.batchvid,m.status
		from ft_rdoc_tsin m inner join ft_rdoc_infee g on m.rdoctsinicode = g.rdoctsinicode inner join ft_fund_vmark v on m.rdoctsinicode = v.srcicode
		]]>
	</view>

</db>
