## 单据类型
title_FT-RPADJ.AdvPayOff=预付冲销单
title_FT-RPADJ.LrpOff=往来冲抵单
title_FT-RPADJ.LrpOffVmark=往来冲抵单凭证
title_FT-RPADJ.AdjPayOff=付款调整单
title_FT-RPADJ.AdjPayOffSingle=付款调整单(单笔)
title_FT-RPADJ.AdjPayOffBatch=付款调整单(批量)
title_FT-RPADJ.LrpDeal=往来处理单
title_FT-RPADJ.AdjRecOff=收款认领调整单
title_FT-RPADJ.AdjRecOffSingle=收款认领调整单(单笔)
title_FT-RPADJ.AdjRecOffBatch=收款认领调整单(批量)
title_FT-RPADJ.AdvRecOff=预收冲销单
title_FT-RPADJ.LrpAdvOff=往来冲销工作台
title_FT-RPADJ.AdjPayNSimulBusiVMark=付款申请单(非模拟)事业部凭证-付款调整单
title_FT-RPADJ.AdvPayNSimulBusiVMark=付款申请单(非模拟)事业部凭证-预付冲销单
title_FT-RPADJ.LrpDealVmark=往来处理单凭证
title_FT-RPADJ.AdjRecOffVMark=认领单凭证(场景一:收款认领调整单)
title_FT-RPADJ.AdvRecOffVMark=认领单凭证(场景一:预收冲销单)
## 入口
title_FT-RPADJ.AdvPayOffEntry=预付冲销单-入口
title_FT-RPADJ.LrpOffEntry=往来冲抵单-入口
title_FT-RPADJ.AdjPayOffEntry=付款调整单-入口
title_FT-RPADJ.LrpDealEntry=往来处理单-入口
title_FT-RPADJ.AdjRecOffEntry=收款认领调整单-入口
title_FT-RPADJ.AdvRecOffEntry=预收冲销单-入口

## 详情
title_FT-RPADJ.AdvPayOffDetail=预付冲销单
title_FT-RPADJ.LrpOffDetail=往来冲抵单
title_FT-RPADJ.AdjPayOffSingleDetail=付款调整单(单笔)
title_FT-RPADJ.AdjPayOffBatchDetail=付款调整单(批量)
title_FT-RPADJ.LrpDealDetail=往来处理单
title_FT-RPADJ.AdjRecOffSingleDetail=收款认领调整单(单笔)
title_FT-RPADJ.AdjRecOffBatchDetail=收款认领调整单(批量)
title_FT-RPADJ.AdvRecOffDetail=预收冲销单

## 工作台
title_FT-RPADJ.AdjPayOffWorkBench=付款调整工作台
title_FT-RPADJ.AdjRecOffWorkBench=收款认领调整工作台
title_FT-RPADJ.FinPayDetailDialog=追加支付明细
title_FT-RPADJ.AdjPayOffSubmitDlg=付款调整提交对话框
title_FT-RPADJ.LrpDealVou=往来处理单凭证
title_FT-RPADJ.LrpAdvOffWorkBench=往来冲销工作台
title_FT-RPADJ.LrpOffVou=往来冲抵单凭证


## group 分组
FT.title.grp.AdvPayOffg=预付款
FT.title.grp.AdvPayOffgs=冲销数据
FT.title.grp.calloutinfo=调出信息
FT.title.F.grp.bcin=调入信息
FT.title.F.grp.bcsum=调整后汇总信息
FT.title.grp.AdvRecOffg=预收款
FT.title.grp.AdvRecOffgs=冲销数据
FT.title.grp.pradjShare=往来处理信息

## 标签页
title.F.grp.lrpoffg=冲抵明细
title.F.tab.lrpdealg=往来处理信息

## 按钮
FT.cmd_addAdvance=追加预付
FT.cmd_addDue=追加应付
FT.cmd_automatching=自动匹配
FT.cmd_generadjpay=生成付款调整单(批量)
FT.cmd_genRecOffBatch=生成收款认领调整单(批量)
FT.cmd_exportmsg=导出信息
FT.cmd_excelStrategyImp=导入信息
FT.cmd_addPayDetail=追加支付明细
FT.cmd_ccodeRefund=客户退款
FT.cmd_addAdvRec=追加预收
FT.cmd_addRec=追加应收
FT.cmd_addAdjRecOffCallOut=追加待调整数据
FT.cmd_selectAcode=选择科目
FT.cmd_editPayCcode=修改收款方
FT.cmd_editRecCcode=修改客户

recPayAdj=调整
recPayAdv=冲销

