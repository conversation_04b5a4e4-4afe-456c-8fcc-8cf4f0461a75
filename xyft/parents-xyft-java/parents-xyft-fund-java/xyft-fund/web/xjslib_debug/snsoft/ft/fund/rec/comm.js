Xjs.loadedXjs.push("snsoft/ft/fund/rec/comm");
/*snsoft/ft/rec/comm/invoker/GenRecClaimInvoker.java*/
Xjs.namespace("snsoft.ft.rec.comm.invoker");
snsoft.ft.rec.comm.invoker.GenRecClaimInvoker$GenRclmParams=function(){
};
snsoft.ft.rec.comm.invoker.GenRecClaimInvoker=function(parameter){
    snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.superclass.constructor.call(this,parameter);
    this.ckfixCol = String.obj2str(parameter.ckfixCol,"corpbcode,ccode,fcode");
};
Xjs.extend(snsoft.ft.rec.comm.invoker.GenRecClaimInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.rec.comm.invoker.GenRecClaimInvoker",
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.beforeCheck*/
    beforeCheck:function(event)
    {
        event.table.dataSet.postRow();
        this.checkSelRow(event.table);
        var invokeParam = this.buildInvokeParam(null),
            genRclmParams = this.getGenRclmParams(event);
        invokeParam.genRclmParams = genRclmParams;
        invokeParam.invokeMethod = "beforeCheck";
        return invokeParam;
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.check*/
    check:function(event)
    {
        this.doCheckBeforeShow(event);
        if(this.muiid)
        {
            if(this.okAndSubmit)
                return this.newDialogWithFuncExt(event,this.onDlgShow,this.onDlgOk,this.onDlgOkAndSubmit);
            else 
                return this.newDialogWithFunc(event,this.onDlgShow,this.onDlgOk);
        } else 
        {
            var btnTitle = snsoft.ft.utils.FTUtils.getBtnTitle(event.table,event.cfg.cmd),
                message = Xjs.ResBundle.getResVal("C001",this.getConfirmMessageMacro(event));
            return this.showConfirmDialog(event,btnTitle,message,null,this.onDlgOk);
        }
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.newDialogWithFuncExt*/
    newDialogWithFuncExt:function(event,showFunc,okFunc,okSubmitFunc)
    {
        var scope = this;
        return {type:2,showConfirm:new Xjs.FuncCall((onConfirm)=>{
            var dlg = showFunc.call(scope,event);
            if(dlg)
            {
                dlg.addListener("onOk",new Xjs.FuncCall((dlgPane)=>{
            if(okFunc)
                okFunc.call(scope,event,dlgPane);
            onConfirm.call(null,"ok");
        }));
                dlg.addListener("oncmd_" + snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.cmd_okAndSubmit,new Xjs.FuncCall((dlgPane)=>{
            if(okSubmitFunc)
                okSubmitFunc.call(scope,event,dlgPane);
            dlg.closeModal();
            onConfirm.call(null,"ok");
        }));
            } else 
                onConfirm.call(null,"ok");
        })};
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.getConfirmMessageMacro*/
    getConfirmMessageMacro:function(event)
    {
        return event.cfg.title;
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.doCheckBeforeShow*/
    doCheckBeforeShow:Xjs.emptyFn,
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.onDlgShow*/
    onDlgShow:function(e)
    {
        this.dealParamMacros(this.table);
        var dlg = Xjs.ui.UIUtil.loadDialog(this.muiid);
        dlg.setWidth(1100);
        dlg.setHeight(600);
        dlg.title = snsoft.ft.utils.FTUtils.getBtnTitle(e.table,e.cfg.cmd);
        var dlgtbl = snsoft.ft.utils.FTUtils.getTableFromPanel(dlg);
        dlgtbl.addListener("dataSetRefreshing",new Xjs.FuncCall((ds,sde,p)=>{
            if(!sde.refreshParam)
            {
                sde.refreshParam = {};
            }
            Xjs.apply(sde.refreshParam,p);
        },this,[this.refreshParams]));
        dlg.beforeShowing = this.resetBeforeShowing.createDelegate(this,[dlg,dlgtbl],true);
        dlg.addListener("onShowing",new Xjs.FuncCall(this.resetOnShowing,this,[dlgtbl]));
        this.beforeShow(e,dlg);
        dlg.showModal();
        return dlg;
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.beforeShow*/
    beforeShow:function(e,dlg)
    {
        if(this.okAndSubmit)
        {
            var newButtons = [],
                btn = new Xjs.ui.Button({text:Xjs.ResBundle.getResVal("FT.cmd_okAndSubmit"),command:snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.cmd_okAndSubmit,className:"btn ok"});
            newButtons.push(btn);
            var dftButtons = Xjs.ui.Panel.newDefaultButtons();
            dftButtons.forEach((b)=>{
            return newButtons.push(b);
        });
            dlg.buttons = newButtons;
        }
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.dealParamMacros*/
    dealParamMacros:function(table)
    {
        if(this.refreshParams)
        {
            var parammacros = {},
                fixVals = {};
            for(var mackey in this.refreshParams)
            {
                var pattern = this.refreshParams[mackey];
                if(!pattern || pattern instanceof Object)
                    continue;
                var valStr = String.obj2str(pattern,null);
                if(valStr.startsWith("@"))
                {
                    fixVals[mackey] = valStr.substring(1,valStr.length);
                }
                if(valStr.startsWith("{"))
                {
                    parammacros[mackey] = valStr.startsWith("{") ? valStr.substring(1,valStr.length - 1) : valStr;
                }
            }
            var values = snsoft.ft.utils.FTUtils.parseMuiidMacros(table,parammacros);
            if(!this.isEmpty(fixVals))
            {
                Xjs.applyIf(values,fixVals);
            }
            this.refreshParams = Xjs.applyIf(values,this.refreshParams);
        }
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.addInitValCols*/
    addInitValCols:function(col,add)
    {
        if(!col)
            return;
        if(!this.initValCols)
        {
            this.initValCols = col;
        } else 
        {
            if(add)
            {
                if(!String.isStrIn(this.initValCols,col))
                    this.initValCols = this.initValCols + "," + col;
            } else 
            {
                var initValColsA = this.initValCols.split(",");
                initValColsA.remove(col);
                this.initValCols = initValColsA.join(",");
            }
        }
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.addRdonlyCols*/
    addRdonlyCols:function(col,add)
    {
        if(!col)
            return;
        if(!this.rdonlyCols)
        {
            this.rdonlyCols = col;
        } else 
        {
            if(add)
            {
                if(!String.isStrIn(this.rdonlyCols,col))
                    this.rdonlyCols = this.rdonlyCols + "," + col;
            } else 
            {
                var rdonlyColsA = this.rdonlyCols.split(",");
                rdonlyColsA.remove(col);
                this.rdonlyCols = rdonlyColsA.join(",");
            }
        }
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.setInitParasVales*/
    setInitParasVales:function(queryParam)
    {
        var initValCols = this.initValCols ? this.initValCols.split(",") : [];
        if(initValCols.length > 0)
        {
            for(var i=0;i < initValCols.length;i++)
            {
                var col = initValCols[i],
                    item = queryParam.getItemByName(col);
                if(item)
                {
                    var val = this.refreshParams[col];
                    if(!val)
                    {
                        val = this.getSelOneRowValue(this.table,col);
                    }
                    if(val)
                        item.setValue(val);
                }
            }
        }
        var rdcols = this.rdonlyCols ? this.rdonlyCols.split(",") : [];
        if(rdcols.length > 0)
        {
            for(var i=0;i < rdcols.length;i++)
            {
                var col = rdcols[i],
                    item = queryParam.getItemByName(col);
                if(item)
                {
                    item.setReadonly(true);
                }
            }
        }
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.isEmpty*/
    isEmpty:function(o)
    {
        for(var name in o)
        {
            return false;
        }
        return true;
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.resetBeforeShowing*/
    resetBeforeShowing:function(dlg,dlgTable)
    {
        this.setInitParasVales(dlg);
        snsoft.ft.utils.FTUtils.hiddenPaneCollapseBtns(dlgTable.queryParam);
        snsoft.ft.utils.FTUtils.hiddenSaveLoadValueBtns(dlgTable.queryParam);
        snsoft.ft.utils.FTUtils.setDialogToolVisiable(dlg,this.obj2bool(this.isToolHidden,true));
        snsoft.ft.utils.FTUtils.setBtnVisible(dlgTable,this.toolHidCommands,false);
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.obj2bool*/
    obj2bool:function(value,dft)
    {
        if(dft===undefined)
            dft = false;
        return value == null || value===undefined ? dft : Xjs.Data.parseFromSqlType(value,-7);
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.resetOnShowing*/
    resetOnShowing:function(dlg,dlgtbl)
    {
        snsoft.ft.utils.FTUtils.resetBackupVal(dlg,dlgtbl);
        if(this.tgtSheetCode)
            snsoft.ft.utils.FTUtils.addAidsSheetfilter(dlgtbl,this.tgtSheetCode,this.limitcols);
        dlgtbl.refreshTableIfOK();
        if(dlg && dlg.dom && Xjs.DOM.findById("PaneCollapseBtn2",dlg.dom))
        {
            snsoft.ft.utils.FTUtils.setWidthfmOffsetWidth(dlg);
        }
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.onDlgOk*/
    onDlgOk:function(e,dlg,cmd)
    {
        var pass = this.doCheckBeforeOk(e,dlg);
        if(pass)
        {
            var genRclmParams = this.getGenRclmParams(e);
            genRclmParams.execSubmit = snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.cmd_okAndSubmit==cmd;
            this.buildGenRclmParams(genRclmParams,e,dlg);
            e.checkData.genRclmParams = genRclmParams;
        }
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.onDlgOkAndSubmit*/
    onDlgOkAndSubmit:function(event,dlg)
    {
        this.onDlgOk(event,dlg,snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.cmd_okAndSubmit);
        var invokeParam = this.getInvokeParam(event,false),
            invokeRtnVal = snsoft.ext.cmd.CommandInvokers.remoteInvoke(event.table,event.cfg,"invoke",invokeParam);
        if(invokeRtnVal)
        {
            snsoft.ft.rec.comm.invoker.RecClaimBmpCheckInvoker.showBmpChkDlg(invokeRtnVal,event.cfg.title);
        }
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.doCheckBeforeOk*/
    doCheckBeforeOk:Xjs.trueFn,
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.getGenRclmParams*/
    getGenRclmParams:function(e)
    {
        var genRclmParams = {};
        if(!this.sheetCode)
            this.sheetCode = e.table.dataSet.getValue("sheetcode");
        genRclmParams.refSheetCode = this.sheetCode;
        genRclmParams.tgtSheetCode = this.tgtSheetCode;
        genRclmParams.reficodes = this.getSelRowInnercodes(e.table);
        genRclmParams.refSortFlds = this.refSortFlds;
        genRclmParams.copyRefCols = this.copyRefCols;
        return genRclmParams;
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.buildGenRclmParams*/
    buildGenRclmParams:Xjs.emptyFn,
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.getSelRowInnercodes*/
    getSelRowInnercodes:function(table)
    {
        var rows = table.getSelectedRowNumbers(),
            innerColumn = table.dataSet.getKeyColumnNames()[0],
            arry = rows.map((r)=>{
            return table.dataSet.getValue(innerColumn,r);
        });
        return arry.join(",");
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.getAllRowValues*/
    getAllRowValues:function(table)
    {
        var vals = [];
        for(var j=0;j < table.dataSet.getRowCount();j++)
        {
            vals.push(this.getRowColValues(table.dataSet,j));
        }
        return vals;
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.getRowColValues*/
    getRowColValues:function(rclmDataSet,r)
    {
        var rowValues = rclmDataSet.getRowData(r);
        if(rowValues == null)
            return null;
        var values = {};
        {
            for(var j=0;j < rclmDataSet.columns.length;j++)
            {
                if(rowValues[j])
                    values[rclmDataSet.columns[j].name] = rowValues[j];
            }
        }
        return values;
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.getSelRowValues*/
    getSelRowValues:function(table)
    {
        var vals = [],
            rows = table.getSelectedRowNumbers();
        for(var j=0;j < rows.length;j++)
        {
            vals.push(this.getRowColValues(table.dataSet,rows[j]));
        }
        return vals;
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.checkSelRow*/
    checkSelRow:function(table)
    {
        var rows = table.getSelectedRowNumbers();
        if(rows == null || rows.length == 0)
        {
            throw new Error(Xjs.ResBundle.getResVal("FT.00000048"));
        }
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.checkSelectOne*/
    checkSelectOne:function(macro)
    {
        var rows = this.table.getSelectedRowNumbers();
        if(rows.length != 1)
        {
            throw new Error(Xjs.ResBundle.getResVal("FT-REC.00000010",macro));
        }
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.checkSelectOne2*/
    checkSelectOne2:function(table,macro)
    {
        var rows = table.getSelectedRowNumbers();
        if(rows.length != 1)
        {
            throw new Error(Xjs.ResBundle.getResVal("FT-REC.00000013",macro));
        }
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.getSelOneRowValue*/
    getSelOneRowValue:function(table,column)
    {
        var rows = table.getSelectedRowNumbers();
        if(rows != null)
        {
            return table.dataSet.getValue(column,rows[0]);
        }
        return null;
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.checkSelRowValid*/
    checkSelRowValid:function()
    {
        var rows = this.table.getSelectedRowNumbers(),
            no_valid_rows = rows.filter((r)=>{
            return this.dataSet.getValue("isvalid",r) !== "Y";
        });
        if(no_valid_rows != null && no_valid_rows.length > 0)
        {
            throw new Error(Xjs.ResBundle.getResVal("FT-REC.00000002"));
        }
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.checkSelRowFundType10*/
    checkSelRowFundType10:function()
    {
        this.checkSelRowFundType("10");
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.checkSelRowFundType20*/
    checkSelRowFundType20:function()
    {
        this.checkSelRowFundType("20");
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.checkSelRowFundType*/
    checkSelRowFundType:function(code)
    {
        var rows = this.table.getSelectedRowNumbers(),
            no_recfundtype_rows = rows.filter((r)=>{
            return this.dataSet.getValue("recfundtype",r) !== code;
        });
        if(no_recfundtype_rows != null && no_recfundtype_rows.length > 0)
        {
            throw new Error(Xjs.ResBundle.getResVal("FT-REC.00000003",this.getCodeDataName("recfundtype",code)));
        }
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.getCodeDataName*/
    getCodeDataName:function(colname,code)
    {
        var column = this.table.getColumn(colname);
        if(column && column.selectOptions && column.selectOptions instanceof Xjs.dx.CodeData)
        {
            var codeData = column.selectOptions,
                codeNames = codeData.getCodeNames([code]);
            return codeNames ? codeNames[0] : code;
        }
        return code;
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.checkSelRowBnostatusNot90*/
    checkSelRowBnostatusNot90:function()
    {
        var rows = this.table.getSelectedRowNumbers(),
            status90_rows = rows.filter((r)=>{
            return this.dataSet.getValue("bnostatus",r) == "90";
        });
        if(status90_rows != null && status90_rows.length > 0)
        {
            throw new Error(Xjs.ResBundle.getResVal("FT-REC.00000045"));
        }
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.checkSelRowRclmfcyingGtZero*/
    checkSelRowRclmfcyingGtZero:function()
    {
        this.checkSelRowRclmfcyingGtZero2("FT-REC.00000004",this.table.getColumn("rclmfcying").title);
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.checkSelRowRclmfcyingGtZero*/
    checkSelRowRclmfcyingGtZero2:function(key,t)
    {
        var rows = this.table.getSelectedRowNumbers(),
            no_rclmfcying_rows = rows.filter((r)=>{
            return this.dataSet.getValue("rclmfcying",r) === 0;
        });
        if(no_rclmfcying_rows != null && no_rclmfcying_rows.length > 0)
        {
            throw new Error(Xjs.ResBundle.getResVal(key,t));
        }
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.checkSelRowSameCol*/
    checkSelRowSameCol:function(table)
    {
        this.checkSelRowSameCol2(table,this.ckfixErrExtCol);
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.checkSelRowSameCol*/
    checkSelRowSameCol2:function(table,ckfixErrExtCol)
    {
        var rows = table.getSelectedRowNumbers(),
            errArgs = snsoft.ft.utils.FTUtils.initErrCheckArgs(table,this.ckfixCol.split(","));
        errArgs.selrow = rows;
        errArgs.title = this.getTitle();
        errArgs.showExtCols = ckfixErrExtCol.split(",");
        snsoft.ft.utils.FTUtils.checkfixmodeGridErrRows(errArgs);
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.checkNonBlank*/
    checkNonBlank:function(t,checkOpts)
    {
        var tDataSet = t.dataSet;
        tDataSet.postRow();
        t.checkNonBlankForSubmit(checkOpts);
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.verifySelectErr*/
    verifySelectErr:function(table)
    {
        this.doVerifySelectedErr(table,null,false);
        var selectRow = table.rowMutiSelectable,
            matchCol = this.findMatchCol(table);
        this.doVerifySelectedErr(table,matchCol,selectRow);
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.findMatchCol*/
    findMatchCol:function(table)
    {
        var mc = this.matchCol ? this.matchCol : table.dataSet.getCuInnerfld();
        if(!mc || mc===undefined)
        {
            mc = table.dataSet.getColumn(0).name;
        }
        return mc;
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.doVerifySelectedErr*/
    doVerifySelectedErr:function(table,matchCol,selectRow)
    {
        if(!matchCol)
        {
            if(!table.dataSet.isOpen())
            {
                this.throwNullDataErr();
            }
        } else 
        {
            var icodeS = snsoft.ft.utils.FTUtils.getSelectedColumnValuesCaseChild(table,matchCol,!selectRow);
            if(!icodeS || icodeS.length == 0)
            {
                this.throwNullDataErr();
            }
        }
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.throwNullDataErr*/
    throwNullDataErr:function()
    {
        throw new Error(Xjs.ResBundle.getString("UI","Aid.Dlg.NulValueSelected"));
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.sameFcodeMatch*/
    sameFcodeMatch:function(table,dlgTable)
    {
        var getFcodes = (t)=>{
            var selectRows = t.getSelectedRowNumbers(t.rowMutiSelectable ? 0 : 1),
                allSelected = snsoft.ft.utils.FTUtils.getAllDataSetRow(t,selectRows,t.getDataSet().columnAt("fcode")),
                columnIdx = t.dataSet.columnAt("fcode");
            return allSelected.map((dataSetRow)=>{
            return dataSetRow[columnIdx];
        });
        },
            fcodes = getFcodes(table),
            dlgFcodes = getFcodes(dlgTable),
            func = (_f,_fcodes)=>{
            for(var i=0;i < _fcodes.length;i++)
            {
                if(_fcodes[i] != _f)
                    return false;
            }
            return true;
        };
        for(var i=0;i < fcodes.length;i++)
        {
            if(!func(fcodes[i],dlgFcodes))
                return false;
        }
        for(var j=0;j < dlgFcodes.length;j++)
        {
            if(!func(dlgFcodes[j],fcodes))
                return false;
        }
        return true;
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.checkRefMatchCol*/
    checkRefMatchCol:function(table,dlgTable,dlgColname,fldTitle,selrow)
    {
        var rows = table.getSelectedRowNumbers(),
            rclmfcying = 0;
        for(var i=0;i < rows.length;i++)
        {
            rclmfcying = snsoft.ft.utils.FTUtils.add(rclmfcying,table.dataSet.getValue("rclmfcying",rows[i]));
        }
        var fcy = 0;
        if(selrow)
        {
            var drows = dlgTable.getSelectedRowNumbers();
            for(var m=0;m < drows.length;m++)
            {
                fcy = snsoft.ft.utils.FTUtils.add(fcy,dlgTable.dataSet.getValue(dlgColname,drows[m]));
            }
        } else 
        {
            for(var j=0;j < dlgTable.dataSet.getRowCount();j++)
            {
                fcy = snsoft.ft.utils.FTUtils.add(fcy,dlgTable.dataSet.getValue(dlgColname,j));
            }
        }
        if(rclmfcying < fcy)
        {
            throw new Error(Xjs.ResBundle.getResVal("FT-REC.00000005",table.getColumn("rclmfcying").title,rclmfcying,fldTitle,fcy));
        }
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.postRow*/
    postRow:function(table)
    {
        if(table instanceof Xjs.ui.GridTable && table.editingComp || table.dataSet.isChanged(true))
        {
            table.dataSet.postRow();
        }
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.invoke*/
    invoke:function(event)
    {
        return this.getInvokeParam(event,true);
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.getInvokeParam*/
    getInvokeParam:function(event,autoSave)
    {
        var genRclmParams = event.checkData.genRclmParams;
        if(genRclmParams != null)
        {
            var invokeParam = this.buildInvokeParam(null);
            invokeParam.mains.forEach((main)=>{
            return main.sheetcode = this.mainDataSet.getValue("sheetcode");
        });
            genRclmParams.autoSave = autoSave;
            invokeParam.genRclmParams = genRclmParams;
            return invokeParam;
        }
        return null;
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        if((this.refreshMode & 1) != 0)
        {
            if(event.invokeRtnVal)
            {
                snsoft.ft.utils.FTUtils.replaceValueAndAutoRefresh(event.table,event.invokeRtnVal.tableRowValues);
                var keepSelStatus = event.invokeRtnVal.keepSelStatus;
                if(!keepSelStatus)
                {
                    event.dataSet.selAllRowSelected(event.table._getRowSelid(),false);
                }
            }
            if((this.refreshMode & 2) != 0)
            {
                var ftRecRclm = Xjs.util.TableUtils.getTable_$Panel_$String(event.table,"ft_rec_rclm");
                ftRecRclm.dataSet.refresh();
            }
        } else if((this.refreshMode & 4) != 0)
        {
            event.table.refreshTable();
        }
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.newEditDialog*/
    newEditDialog:function(e)
    {
        var dlg = Xjs.ui.UIUtil.loadDialog(this.muiid);
        dlg.title = snsoft.ft.utils.FTUtils.getBtnTitle(e.table,e.cfg.cmd);
        var dlgtbl = snsoft.ft.utils.FTUtils.getTableFromPanel(dlg);
        dlgtbl.dataSet.refresh();
        dlgtbl.dataSet.insertRow(3);
        dlgtbl.dataSet.setRowChanged(true);
        this.copySrcSelRowValue(dlgtbl);
        dlg.showModal();
        this.requestFocus(dlgtbl);
        return dlg;
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.dataChanged*/
    dataChanged:function(tbl,ignoreCols)
    {
        var tcs = tbl.getColumns(),
            hasModified = false;
        for(var i=0;i < tcs.length;i++)
        {
            if(ignoreCols != null && String.isStrIn(ignoreCols,tcs[i].name))
                continue;
            if(!tcs[i].isVisible())
                continue;
            if(tbl.dataSet.getValue(tcs[i].name) == this.dataSet.getValue(tcs[i].name))
            {
                continue;
            } else if(!hasModified)
            {
                hasModified = true;
            }
        }
        return hasModified;
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.copySrcSelRowValue*/
    copySrcSelRowValue:function(dlgtbl)
    {
        var dcs = dlgtbl.dataSet.columns;
        for(var i=0;i < dcs.length;i++)
        {
            var c = this.dataSet.columnAt(dcs[i].name);
            if(c < 0)
                continue;
            dlgtbl.dataSet.setValue(dcs[i].name,this.getSelOneRowValue(this.table,dcs[i].name));
        }
    },
    /*snsoft.ft.rec.comm.invoker.GenRecClaimInvoker.requestFocus*/
    requestFocus:function(dlgtbl)
    {
        var t = dlgtbl;
        setTimeout(()=>{
            var tcs = t.getColumns(),
                c = 0;
            for(;c < tcs.length;c++)
            {
                if(tcs[c].isVisible() && !tcs[c].readOnly)
                {
                    break;
                }
            }
            t.setSelectedColumn(c,true);
        },1);
    }
});
Xjs.apply(snsoft.ft.rec.comm.invoker.GenRecClaimInvoker,{
    cmd_okAndSubmit:"okAndSubmit"
});
/*snsoft/ft/rec/comm/invoker/BatchRclmGenRecClaimInvoker.java*/
snsoft.ft.rec.comm.invoker.BatchRclmGenRecClaimInvoker=function(parameter){
    snsoft.ft.rec.comm.invoker.BatchRclmGenRecClaimInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.rec.comm.invoker.BatchRclmGenRecClaimInvoker,snsoft.ft.rec.comm.invoker.GenRecClaimInvoker,{
  _js$className_:"snsoft.ft.rec.comm.invoker.BatchRclmGenRecClaimInvoker",
    /*snsoft.ft.rec.comm.invoker.BatchRclmGenRecClaimInvoker.onDlgShow*/
    onDlgShow:function(e)
    {
        var innerColumn = this.table.dataSet.getKeyColumnNames()[0],
            p = {};
        p[innerColumn] = "@" + this.getSelRowInnercodes(e.table);
        if(!this.refreshParams)
            this.refreshParams = {};
        Xjs.apply(this.refreshParams,p);
        return snsoft.ft.rec.comm.invoker.BatchRclmGenRecClaimInvoker.superclass.onDlgShow.call(this,e);
    },
    /*snsoft.ft.rec.comm.invoker.BatchRclmGenRecClaimInvoker.resetOnShowing*/
    resetOnShowing:function(dlg,dlgtbl)
    {
        snsoft.ft.rec.comm.invoker.BatchRclmGenRecClaimInvoker.superclass.resetOnShowing.call(this,dlg,dlgtbl);
        dlgtbl.dataSet.selAllRowSelected(dlgtbl._getRowSelid(),true);
    },
    /*snsoft.ft.rec.comm.invoker.BatchRclmGenRecClaimInvoker.doCheckBeforeOk*/
    doCheckBeforeOk:function(e,dlg)
    {
        var dlgTable = snsoft.ft.utils.FTUtils.getTableFromPanel(dlg),
            rclmTable = dlg.getItemByName("ft_rec_rclm");
        rclmTable.dataSet.postRow();
        if(rclmTable.dataSet.getRowCount() == 0)
        {
            rclmTable.dataSet.insertRow(0);
            throw new Error(Xjs.ResBundle.getResVal("SN-PLAT.IsNotNull",Xjs.ResBundle.getResVal("title.F.tab.rclmg")));
        }
        this.checkNonBlank(rclmTable,1 + 0x10 + 0x40);
        this.checkRefMatchCol(dlgTable,rclmTable,"fcy",rclmTable.getColumn("recfcy").title,false);
        return snsoft.ft.rec.comm.invoker.BatchRclmGenRecClaimInvoker.superclass.doCheckBeforeOk.call(this,e,dlg);
    },
    /*snsoft.ft.rec.comm.invoker.BatchRclmGenRecClaimInvoker.buildGenRclmParams*/
    buildGenRclmParams:function(genRclmParams,e,dlg)
    {
        var rclmTable = dlg.getItemByName("ft_rec_rclm");
        genRclmParams.matchDatas = this.getAllRowValues(rclmTable);
    },
    /*snsoft.ft.rec.comm.invoker.BatchRclmGenRecClaimInvoker.doCheckBeforeShow*/
    doCheckBeforeShow:function(event)
    {
        this.checkSelectRowGtTwo();
        if(this.fromFlag == 1)
        {
            this.checkSelRowValid();
            this.checkSelRowFundType10();
        }
        this.checkSelRowRclmfcyingGtZero();
        this.checkSelRowSameCol(event.table);
    },
    /*snsoft.ft.rec.comm.invoker.BatchRclmGenRecClaimInvoker.checkSelectRowGtTwo*/
    checkSelectRowGtTwo:function()
    {
        var rows = this.table.getSelectedRowNumbers();
        if(rows.length < 2)
        {
            throw new Error(Xjs.ResBundle.getResVal("FT-REC.00000001",Xjs.ResBundle.getResVal("title.F.tab.rclmg")));
        }
    }
});
/*snsoft/ft/rec/comm/invoker/GenAdjvRecOffInvoker.java*/
snsoft.ft.rec.comm.invoker.GenAdjvRecOffInvoker=function(parameter){
    snsoft.ft.rec.comm.invoker.GenAdjvRecOffInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.rec.comm.invoker.GenAdjvRecOffInvoker,snsoft.ft.rec.comm.invoker.GenRecClaimInvoker,{
  _js$className_:"snsoft.ft.rec.comm.invoker.GenAdjvRecOffInvoker",
    /*snsoft.ft.rec.comm.invoker.GenAdjvRecOffInvoker.beforeCheck*/
    beforeCheck:function(event)
    {
        event.table.dataSet.postRow();
        this.checkSelRow(event.table);
        var invokeParam = this.buildInvokeParam(null),
            genRclmParams = this.getGenRclmParams(event);
        invokeParam.genRclmParams = genRclmParams;
        invokeParam.genSheetCode = this.genSheetCode;
        invokeParam.invokeMethod = "beforeCheck";
        return invokeParam;
    },
    /*snsoft.ft.rec.comm.invoker.GenAdjvRecOffInvoker.check*/
    check:function(event)
    {
        var recRegNewDialog = this.showRecRegNewDialog(event),
            offcodes = event.beforeCheckRtnVal.offcodes;
        if(offcodes)
        {
            var btnTitle = snsoft.ft.utils.FTUtils.getBtnTitle(event.table,event.cfg.cmd),
                message = Xjs.ResBundle.getResVal("FT-REC.00000037",Xjs.ResBundle.getResVal(this.genSheetTipKey),offcodes),
                confirmDialog = this.showConfirmDialog(event,btnTitle,message);
            if(recRegNewDialog == null)
            {
                return confirmDialog;
            } else 
            {
                var confirmRequests = [];
                confirmRequests.push(confirmDialog);
                confirmRequests.push(recRegNewDialog);
                return confirmRequests;
            }
        }
        return recRegNewDialog;
    },
    /*snsoft.ft.rec.comm.invoker.GenAdjvRecOffInvoker.showRecRegNewDialog*/
    showRecRegNewDialog:function(event)
    {
        var recregist = event.beforeCheckRtnVal.recregist;
        if(recregist == "Y")
        {
            event.checkData.showNewDlg = true;
            return this.newDialogWithFunc(event,(e)=>{
            var dlg = Xjs.ui.UIUtil.loadDialog(this.muiid);
            dlg.title = this.cfg.title;
            dlg.showModal();
            var tables = Xjs.util.TableUtils.getAllTablesFromComponent(dlg,false),
                dlgTable = tables[0];
            dlg.getItemByName("corpbcode").setValue(this.mainDataSet.getValue("corpbcode"));
            dlg.getItemByName("corpbcode").setReadonly(true);
            dlg.getItemByName("ccode").setValue(this.mainDataSet.getValue("ccode"));
            dlg.getItemByName("ccode").setReadonly(true);
            dlg.getItemByName("rpadjtype").setValue("10");
            dlg.getItemByName("rpadjtype").setReadonly(true);
            return dlg;
        },(e,d)=>{
            var tables = Xjs.util.TableUtils.getAllTablesFromComponent(d,false);
            if(tables && tables.length > 0)
            {
                var t = tables[0];
                t.postPending();
                if(!t.dataSet.isChanged(false))
                    t.dataSet.setRowChanged(true);
                t.checkNonBlankForSubmit();
                event.checkData[snsoft.ft.rec.comm.invoker.GenAdjvRecOffInvoker.UrldefvaluesCol] = Xjs.util.DataSetUtils.getValuesTo(t.dataSet,t.dataSet.rowAt,null);
            }
        });
        }
        return null;
    },
    /*snsoft.ft.rec.comm.invoker.GenAdjvRecOffInvoker.invoke*/
    invoke:function(event)
    {
        var invokeParam = snsoft.ft.rec.comm.invoker.GenAdjvRecOffInvoker.superclass.invoke.call(this,event);
        if(invokeParam == null)
        {
            invokeParam = this.buildInvokeParam(null);
            invokeParam.mains.forEach((main)=>{
            return main.sheetcode = this.mainDataSet.getValue("sheetcode");
        });
            invokeParam.genRclmParams = this.getGenRclmParams(event);
        }
        invokeParam.genSheetCode = this.genSheetCode;
        invokeParam.copycode = this.copycode;
        invokeParam.invokeMethod = "invoke";
        invokeParam.setRpadjtype = this.setRpadjtype;
        if(event.checkData && snsoft.ft.rec.comm.invoker.GenAdjvRecOffInvoker.UrldefvaluesCol in event.checkData)
            invokeParam[snsoft.ft.rec.comm.invoker.GenAdjvRecOffInvoker.UrldefvaluesCol] = event.checkData[snsoft.ft.rec.comm.invoker.GenAdjvRecOffInvoker.UrldefvaluesCol];
        return invokeParam;
    },
    /*snsoft.ft.rec.comm.invoker.GenAdjvRecOffInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        snsoft.plat.bas.viewdetail.ViewDetailListener.invokeViewDetail(event.invokeRtnVal,"offcode");
    }
});
Xjs.apply(snsoft.ft.rec.comm.invoker.GenAdjvRecOffInvoker,{
    UrldefvaluesCol:"urlDefValues"
});
/*snsoft/ft/rec/comm/invoker/PurcodeRefundGenRecClaimInvoker.java*/
snsoft.ft.rec.comm.invoker.PurcodeRefundGenRecClaimInvoker=function(parameter){
    snsoft.ft.rec.comm.invoker.PurcodeRefundGenRecClaimInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.rec.comm.invoker.PurcodeRefundGenRecClaimInvoker,snsoft.ft.rec.comm.invoker.GenRecClaimInvoker,{
  _js$className_:"snsoft.ft.rec.comm.invoker.PurcodeRefundGenRecClaimInvoker",
    /*snsoft.ft.rec.comm.invoker.PurcodeRefundGenRecClaimInvoker.beforeCheck*/
    beforeCheck:function(event)
    {
        this.checkSelRow(event.table);
        this.checkSelectOne(event.cfg.title);
        return null;
    },
    /*snsoft.ft.rec.comm.invoker.PurcodeRefundGenRecClaimInvoker.doCheckBeforeOk*/
    doCheckBeforeOk:function(e,dlg)
    {
        var dlgTable = snsoft.ft.utils.FTUtils.getTableFromPanel(dlg);
        this.postRow(dlgTable);
        this.verifySelectErr(dlgTable);
        if(this.sameFcodeMatch(e.table,dlgTable))
        {
            this.checkRefMatchCol(e.table,dlgTable,"curfcy",Xjs.ResBundle.getResVal("ft_rec_rclm.rclmfcy"),true);
        }
        return snsoft.ft.rec.comm.invoker.PurcodeRefundGenRecClaimInvoker.superclass.doCheckBeforeOk.call(this,e,dlg);
    },
    /*snsoft.ft.rec.comm.invoker.PurcodeRefundGenRecClaimInvoker.buildGenRclmParams*/
    buildGenRclmParams:function(genRclmParams,e,dlg)
    {
        var dlgTable = snsoft.ft.utils.FTUtils.getTableFromPanel(dlg);
        genRclmParams.matchCol = this.matchCol;
        var selectRows = dlgTable.getSelectedRowNumbers(dlgTable.rowMutiSelectable ? 0 : 1),
            allSelected = snsoft.ft.utils.FTUtils.getAllDataSetRow(dlgTable,selectRows,dlgTable.getDataSet().columnAt(this.matchCol)),
            srcvals = snsoft.ft.utils.FTUtils.getSrcMatchCodes(dlgTable,allSelected,this.matchCol,this.extSrcValueCols);
        genRclmParams.matchCodesMap = srcvals;
    },
    /*snsoft.ft.rec.comm.invoker.PurcodeRefundGenRecClaimInvoker.doCheckBeforeShow*/
    doCheckBeforeShow:function(event)
    {
        if(this.fromFlag == 1)
        {
            this.checkSelRowValid();
            this.checkSelRowFundType10();
        }
        this.checkSelRowRclmfcyingGtZero();
        this.checkSelRowSameCol(event.table);
    }
});
/*snsoft/ft/rec/comm/invoker/RecClaimBmpCheckInvoker.java*/
snsoft.ft.rec.comm.invoker.RecClaimBmpCheckInvoker=function(parameter){
    snsoft.ft.rec.comm.invoker.RecClaimBmpCheckInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.rec.comm.invoker.RecClaimBmpCheckInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.rec.comm.invoker.RecClaimBmpCheckInvoker",
    /*snsoft.ft.rec.comm.invoker.RecClaimBmpCheckInvoker.checkSelRow*/
    checkSelRow:function(table)
    {
        var rows = table.getSelectedRowNumbers();
        if(rows == null || rows.length == 0)
        {
            throw new Error(Xjs.ResBundle.getResVal("FT.00000048"));
        }
    },
    /*snsoft.ft.rec.comm.invoker.RecClaimBmpCheckInvoker.beforeCheck*/
    beforeCheck:function(event)
    {
        event.table.dataSet.postRow();
        this.mainTable.saveChanges();
        this.checkSelRow(event.table);
        event.table.checkNonBlankForSubmit(8);
        var invokeParam = this.buildInvokeParam(null),
            rows = this.table.getSelectedRowNumbers(0),
            innercodes = Xjs.util.DataSetUtils.getColumnStrValues_DataSet_int_String(this.dataSet,"rclmicode",rows,",");
        invokeParam.innercodes = innercodes;
        invokeParam.sheetcode = this.sheetcode;
        return invokeParam;
    },
    /*snsoft.ft.rec.comm.invoker.RecClaimBmpCheckInvoker.check*/
    check:function(event)
    {
        if(event.beforeCheckRtnVal)
        {
            snsoft.ft.rec.comm.invoker.RecClaimBmpCheckInvoker.showBmpChkDlg(event.beforeCheckRtnVal,this.cfg.title);
        }
        return null;
    }
});
Xjs.apply(snsoft.ft.rec.comm.invoker.RecClaimBmpCheckInvoker,{
    /*snsoft.ft.rec.comm.invoker.RecClaimBmpCheckInvoker.showBmpChkDlg*/
    showBmpChkDlg:function(rtnVal,title)
    {
        var bmpcodes = rtnVal.bmpcodes,
            nobmpdatas = rtnVal.nobmpdatas,
            tip = rtnVal.tip;
        if(bmpcodes || nobmpdatas)
        {
            var muiid = "SNA-ACC.RelaBmpChkPop",
                initVals = {param:{bmpcodes:bmpcodes,nobmpdatas:nobmpdatas,tip:tip}},
                dlg = Xjs.ui.UIUtil.loadDialog(muiid,0,Xjs.ui.Panel.newCloseButton(),null,null,initVals);
            dlg.title = title;
            dlg.showModal();
            throw {dummy:true};
        }
    }
});
/*snsoft/ft/rec/comm/invoker/RecClaimCommInvoker.java*/
snsoft.ft.rec.comm.invoker.RecClaimCommInvoker=function(parameter){
    snsoft.ft.rec.comm.invoker.RecClaimCommInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.rec.comm.invoker.RecClaimCommInvoker,snsoft.ft.rec.comm.invoker.GenRecClaimInvoker,{
  _js$className_:"snsoft.ft.rec.comm.invoker.RecClaimCommInvoker"
});
/*snsoft/ft/rec/comm/invoker/RecClaimRedInvoker.java*/
snsoft.ft.rec.comm.invoker.RecClaimRedInvoker=function(parameter){
    snsoft.ft.rec.comm.invoker.RecClaimRedInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.rec.comm.invoker.RecClaimRedInvoker,snsoft.ft.rec.comm.invoker.GenRecClaimInvoker,{
  _js$className_:"snsoft.ft.rec.comm.invoker.RecClaimRedInvoker",
    /*snsoft.ft.rec.comm.invoker.RecClaimRedInvoker.invoke*/
    invoke:function(event)
    {
        var params = snsoft.ft.rec.comm.invoker.RecClaimRedInvoker.superclass.invoke.call(this,event);
        params.checkSameYM = this.checkSameYM;
        return params;
    }
});
/*snsoft/ft/rec/comm/invoker/RecClaimRefresh.java*/
snsoft.ft.rec.comm.invoker.RecClaimRefresh=function(parameter){
    snsoft.ft.rec.comm.invoker.RecClaimRefresh.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.rec.comm.invoker.RecClaimRefresh,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.rec.comm.invoker.RecClaimRefresh",
    /*snsoft.ft.rec.comm.invoker.RecClaimRefresh.invoke*/
    invoke:function(event)
    {
        this.table.oncmd_trefresh();
        return null;
    }
});
/*snsoft/ft/rec/comm/invoker/RecClaimSheetExchangeEmbedInvoker.java*/
snsoft.ft.rec.comm.invoker.RecClaimSheetExchangeEmbedInvoker=function(parameter){
    snsoft.ft.rec.comm.invoker.RecClaimSheetExchangeEmbedInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.rec.comm.invoker.RecClaimSheetExchangeEmbedInvoker,snsoft.plat.sheet.ex.SheetExchangeEmbedInvoker,{
  _js$className_:"snsoft.ft.rec.comm.invoker.RecClaimSheetExchangeEmbedInvoker",
    /*snsoft.ft.rec.comm.invoker.RecClaimSheetExchangeEmbedInvoker.invoke*/
    invoke:function(event)
    {
        var param = this.dataSet.getKeyValues();
        param.modifydate = this.dataSet.getValue("modifydate");
        param.sheetcode = this.sheetcode;
        var innerfld = this.dataSet.getCuInnerfld(),
            innercode = this.dataSet.getValue(innerfld);
        param.innercode = innercode;
        return param;
    }
});
/*snsoft/ft/rec/comm/invoker/RecClaimSubmitInvoker.java*/
snsoft.ft.rec.comm.invoker.RecClaimSubmitInvoker=function(parameter){
    snsoft.ft.rec.comm.invoker.RecClaimSubmitInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.rec.comm.invoker.RecClaimSubmitInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.rec.comm.invoker.RecClaimSubmitInvoker",
    /*snsoft.ft.rec.comm.invoker.RecClaimSubmitInvoker.checkSelRow*/
    checkSelRow:function(table)
    {
        var rows = table.getSelectedRowNumbers();
        if(rows == null || rows.length == 0)
        {
            throw new Error(Xjs.ResBundle.getResVal("FT.00000048"));
        }
    },
    /*snsoft.ft.rec.comm.invoker.RecClaimSubmitInvoker.invoke*/
    invoke:function(event)
    {
        event.table.dataSet.postRow();
        this.mainTable.saveChanges();
        this.checkSelRow(event.table);
        event.table.checkNonBlankForSubmit(8);
        var invokeParam = this.buildInvokeParam(null),
            subDatas = [],
            rows = this.table.getSelectedRowNumbers(0);
        for(var r=0;r < rows.length;r++)
        {
            var keyVals = this.table.dataSet.getKeyValues(rows[r]);
            keyVals.modifydate = this.table.dataSet.getValue("modifydate",rows[r]);
            subDatas.push(keyVals);
        }
        invokeParam.rclms = subDatas;
        invokeParam.sheetCode = this.sheetCode;
        return invokeParam;
    },
    /*snsoft.ft.rec.comm.invoker.RecClaimSubmitInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        if(event.invokeRtnVal)
            snsoft.ft.utils.FTUtils.replaceValueAndAutoRefresh(event.table,event.invokeRtnVal.tableRowValues);
        event.dataSet.selAllRowSelected(event.table._getRowSelid(),false);
    }
});
/*snsoft/ft/rec/comm/invoker/SelRecLrpGenRecClaimInvoker.java*/
snsoft.ft.rec.comm.invoker.SelRecLrpGenRecClaimInvoker=function(parameter){
    snsoft.ft.rec.comm.invoker.SelRecLrpGenRecClaimInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.rec.comm.invoker.SelRecLrpGenRecClaimInvoker,snsoft.ft.rec.comm.invoker.GenRecClaimInvoker,{
  _js$className_:"snsoft.ft.rec.comm.invoker.SelRecLrpGenRecClaimInvoker",
    /*snsoft.ft.rec.comm.invoker.SelRecLrpGenRecClaimInvoker.setInitParasVales*/
    setInitParasVales:function(queryParam)
    {
        if(this.fromFlag == 1)
        {
            var selectedRowNumbers = this.table.getSelectedRowNumbers(),
                add = selectedRowNumbers.length > 1;
            this.addInitValCols("fcode",add);
            this.addRdonlyCols("fcode",add);
        }
        snsoft.ft.rec.comm.invoker.SelRecLrpGenRecClaimInvoker.superclass.setInitParasVales.call(this,queryParam);
    },
    /*snsoft.ft.rec.comm.invoker.SelRecLrpGenRecClaimInvoker.doCheckBeforeOk*/
    doCheckBeforeOk:function(e,dlg)
    {
        var dlgTable = snsoft.ft.utils.FTUtils.getTableFromPanel(dlg);
        this.postRow(dlgTable);
        this.verifySelectErr(dlgTable);
        if(this.sameFcodeMatch(e.table,dlgTable))
        {
            this.checkRefMatchCol(e.table,dlgTable,"curfcy",Xjs.ResBundle.getResVal("ft_rec_rclm.rclmfcy"),true);
        }
        return snsoft.ft.rec.comm.invoker.SelRecLrpGenRecClaimInvoker.superclass.doCheckBeforeOk.call(this,e,dlg);
    },
    /*snsoft.ft.rec.comm.invoker.SelRecLrpGenRecClaimInvoker.buildGenRclmParams*/
    buildGenRclmParams:function(genRclmParams,e,dlg)
    {
        var dlgTable = snsoft.ft.utils.FTUtils.getTableFromPanel(dlg);
        genRclmParams.matchCol = this.matchCol;
        var selectRows = dlgTable.getSelectedRowNumbers(dlgTable.rowMutiSelectable ? 0 : 1),
            allSelected = snsoft.ft.utils.FTUtils.getAllDataSetRow(dlgTable,selectRows,dlgTable.getDataSet().columnAt(this.matchCol)),
            srcvals = snsoft.ft.utils.FTUtils.getSrcMatchCodes(dlgTable,allSelected,this.matchCol,this.extSrcValueCols);
        genRclmParams.matchCodesMap = srcvals;
    },
    /*snsoft.ft.rec.comm.invoker.SelRecLrpGenRecClaimInvoker.doCheckBeforeShow*/
    doCheckBeforeShow:function(event)
    {
        if(this.fromFlag == 1)
        {
            this.checkSelRowValid();
            this.checkSelRowFundType10();
        }
        this.checkSelRowRclmfcyingGtZero();
        this.checkSelRowSameCol(event.table);
    }
});
