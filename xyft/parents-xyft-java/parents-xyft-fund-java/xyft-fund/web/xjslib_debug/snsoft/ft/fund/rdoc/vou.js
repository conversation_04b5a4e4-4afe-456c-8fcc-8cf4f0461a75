Xjs.loadedXjs.push("snsoft/ft/fund/rdoc/vou");
/*snsoft/ft/rdoc/vou/dist/invoker/RdocDistVouBackATSJSInvoker.java*/
Xjs.namespace("snsoft.ft.rdoc.vou.dist.invoker");
snsoft.ft.rdoc.vou.dist.invoker.RdocDistVouBackATSJSInvoker=function(parameter){
    snsoft.ft.rdoc.vou.dist.invoker.RdocDistVouBackATSJSInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.rdoc.vou.dist.invoker.RdocDistVouBackATSJSInvoker,snsoft.plat.bas.sheet.cmd.red.CommandRed,{
  _js$className_:"snsoft.ft.rdoc.vou.dist.invoker.RdocDistVouBackATSJSInvoker",
    /*snsoft.ft.rdoc.vou.dist.invoker.RdocDistVouBackATSJSInvoker.beforeCheck*/
    beforeCheck:function(event)
    {
        var rows = this.table.getSelectedRowNumbers();
        if(!rows || rows.length == 0)
        {
            throw new Error(Xjs.ResBundle.getResVal("FT.00000048"));
        }
        if(rows.length > 1)
        {
            throw new Error(Xjs.ResBundle.getResVal("FT.00000087"));
        }
        var redflag = this.dataSet.getValue("redflag",rows[0]);
        if(redflag != 0)
        {
            throw new Error(Xjs.ResBundle.getResVal("FT-RDOC.00000006"));
        }
        return null;
    },
    /*snsoft.ft.rdoc.vou.dist.invoker.RdocDistVouBackATSJSInvoker.invoke*/
    invoke:function(event)
    {
        var rows = this.table.getSelectedRowNumbers();
        return {rdocdistsicode:this.mainDataSet.getValue("rdocdistsicode",rows[0]),vidflag:this.mainDataSet.getValue("vidflag",rows[0])};
    },
    /*snsoft.ft.rdoc.vou.dist.invoker.RdocDistVouBackATSJSInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        var invokeRtnVal = event.invokeRtnVal;
        if(invokeRtnVal)
        {
            this.mainTable.refreshTable(true);
            var showErr = invokeRtnVal.showErr;
            if(showErr)
            {
                var err = {message:showErr};
                Xjs.alertErr(err);
                throw err;
            }
        }
    }
});
/*snsoft/ft/rdoc/vou/dist/invoker/RdocDistVouCreateAmortInvoker.java*/
snsoft.ft.rdoc.vou.dist.invoker.RdocDistVouCreateAmortInvoker=function(parameter){
    snsoft.ft.rdoc.vou.dist.invoker.RdocDistVouCreateAmortInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.rdoc.vou.dist.invoker.RdocDistVouCreateAmortInvoker,snsoft.plat.bas.sheet.cmd.red.CommandRed,{
  _js$className_:"snsoft.ft.rdoc.vou.dist.invoker.RdocDistVouCreateAmortInvoker",
    /*snsoft.ft.rdoc.vou.dist.invoker.RdocDistVouCreateAmortInvoker.beforeCheck*/
    beforeCheck:function(event)
    {
        var rows = this.table.getSelectedRowNumbers();
        if(!rows || rows.length == 0)
        {
            throw new Error(Xjs.ResBundle.getResVal("FT.00000048"));
        }
        if(rows.length > 1)
        {
            throw new Error(Xjs.ResBundle.getResVal("FT.00000087"));
        }
        var redflag = this.dataSet.getValue("redflag",rows[0]);
        if(redflag != 0)
        {
            throw new Error(Xjs.ResBundle.getResVal("FT-RDOC.00000046"));
        }
        var isrecourse = this.dataSet.getValue("isrecourse",rows[0]);
        if(isrecourse != "Y")
        {
            throw new Error(Xjs.ResBundle.getResVal("FT-RDOC.00000044"));
        }
        return null;
    },
    /*snsoft.ft.rdoc.vou.dist.invoker.RdocDistVouCreateAmortInvoker.invoke*/
    invoke:function(event)
    {
        var rows = this.table.getSelectedRowNumbers();
        return {rdocdistsicode:this.mainDataSet.getValue("rdocdistsicode",rows[0]),vidflag:this.mainDataSet.getValue("vidflag",rows[0])};
    },
    /*snsoft.ft.rdoc.vou.dist.invoker.RdocDistVouCreateAmortInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        snsoft.plat.bas.viewdetail.ViewDetailListener.invokeViewDetail(event.invokeRtnVal,"feeamtcode");
    }
});
