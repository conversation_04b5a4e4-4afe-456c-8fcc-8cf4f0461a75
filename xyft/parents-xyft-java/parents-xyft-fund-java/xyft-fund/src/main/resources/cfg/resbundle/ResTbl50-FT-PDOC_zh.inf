# 应付票据申请
ft_pdoc_pdocap=应付票据申请
ft_pdoc_pdocap.pdocapicode=${code}
ft_pdoc_pdocap.pdocapcode=${pdocapcode}
ft_pdoc_pdocap.appdocnum=${appdocnum}
ft_pdoc_pdocap.doctype=${doctype}
ft_pdoc_pdocap.opdocstatus=开票状态
ft_pdoc_pdocap.opdocstatus:=开票状态:
ft_pdoc_pdocap.fcy=${paydocfcy}
ft_pdoc_pdocap.adate=${adate}
ft_pdoc_pdocap.ccode=${recccode}
ft_pdoc_pdocap.bankaccount=${recbankacc}
ft_pdoc_pdocap.bankname=${recbankcode}
ft_pdoc_pdocap.fcode=${fcode}
ft_pdoc_pdocap.vpdatetype=${docvpdatetype}
ft_pdoc_pdocap.duedate=${docduedate}
ft_pdoc_pdocap.name=${bankaccname}
ft_pdoc_pdocap.remark=${remark}
ft_pdoc_pdocap.wcode=${wcode}
ft_pdoc_pdocap.bcode=${bcode}
ft_pdoc_pdocap.deptbcode=${bubcode}
ft_pdoc_pdocap.corpbcode=${corpbcode}
ft_pdoc_pdocap.cuicode=${cuicode}
ft_pdoc_pdocap.status=${status}
ft_pdoc_pdocap.status:=${status}:
ft_pdoc_pdocap.ratifydate=${ratifydate}
ft_pdoc_pdocap.submitdate=${submitdate}
ft_pdoc_pdocap.performdate=${performdate}
ft_pdoc_pdocap.sheetcode=${sheetcode}
ft_pdoc_pdocap.vprepare=${vprepare}
ft_pdoc_pdocap.predate=${predate}
ft_pdoc_pdocap.modifier=${modifier}
ft_pdoc_pdocap.modifydate=${modifydate}
ft_pdoc_pdocap.wfcode=${wfcode}
ft_pdoc_pdocap.wfuid=${wfuid}
queryResult.succnum=成功总笔数
queryResult.succfcy=成功总金额
queryResultDetail.doccode=票据号
queryResultDetail.docstatus=当前票据状态

# 应付票据申请采购合同信息
ft_pdoc_pdocapg=应付票据申请采购合同信息
ft_pdoc_pdocapg.pdocapgicode=${code}
ft_pdoc_pdocapg.purordssicode=采购合同分摊付款方式内码
ft_pdoc_pdocapg.pdocapicode=${code}
ft_pdoc_pdocapg.purordcode=${purordcode}
ft_pdoc_pdocapg.purordicode=采购合同内码
ft_pdoc_pdocapg.outordcode=${puroutordcode}
ft_pdoc_pdocapg.fcying=${unexecfcy}
ft_pdoc_pdocapg.fcy=${paydocfcy}
#ft_pdoc_pdocapg.prjicode=业务编号内码
ft_pdoc_pdocapg.prjcode=${busicode}

# 应付票据池
ft_pdoc_pdoc=应付票据池
ft_pdoc_pdoc.pdocicode=${code}
ft_pdoc_pdoc.doctype=${doctype}
ft_pdoc_pdoc.docno=${docno}
ft_pdoc_pdoc.fcy=${docfcy}
ft_pdoc_pdoc.isdircon=${isdirectconn}
ft_pdoc_pdoc.subdocfm=${subdocfm}
ft_pdoc_pdoc.subdocto=${subdocto}
ft_pdoc_pdoc.docdate=${docdate}
ft_pdoc_pdoc.duedate=${docduedate}
ft_pdoc_pdoc.corpbcode=${drawer}
ft_pdoc_pdoc.drawbankaccount=${drawbankaccount}
ft_pdoc_pdoc.drawbankcode=${drawbankcode}
ft_pdoc_pdoc.acpmccode=${acpmccode}
ft_pdoc_pdoc.acpccode=${acpccode}
ft_pdoc_pdoc.acpcorpbcode=${acpcorpbcode}
ft_pdoc_pdoc.accepter=${accepter}
ft_pdoc_pdoc.acpbankaccount=${acpbankaccount}
ft_pdoc_pdoc.acpbankcode=${acpbankcode}
ft_pdoc_pdoc.ccode=${recccode}
ft_pdoc_pdoc.bankname=${recbankcode}
ft_pdoc_pdoc.bankaccount=${recbankacc}
ft_pdoc_pdoc.docstatus=${docstatus}
ft_pdoc_pdoc.srcsheetcode=${srcsheetcode}
ft_pdoc_pdoc.srccode=${srccode}
ft_pdoc_pdoc.srcicode=${srcicode}
ft_pdoc_pdoc.bcode=${bcode}
ft_pdoc_pdoc.isageacp=${isageacp}
ft_pdoc_pdoc.deptbcode=${bubcode}
ft_pdoc_pdoc.validdays=${docvaliddays}
ft_pdoc_pdoc.srcsyscode=${srcsyscode}
ft_pdoc_pdoc.srcsys=${srcsys}
ft_pdoc_pdoc.settdate=${settdate}
ft_pdoc_pdoc.tcaptime=${tcaptime}
ft_pdoc_pdoc.bankno=${bankno}
ft_pdoc_pdoc.vprepare=${vprepare}
ft_pdoc_pdoc.predate=${predate}
ft_pdoc_pdoc.modifier=${modifier}
ft_pdoc_pdoc.modifydate=${modifydate}
ft_pdoc_pdoc.duedatefm=${docduedatefm}
ft_pdoc_pdoc.duedateto=${dateto}
ft_pdoc_pdoc.corpinnerfcy=内部客商贴现金额
ft_pdoc_pdoc.isdiscount=是否贴现
ft_pdoc_pdoc.discountamount=贴现金额
ft_pdoc_pdoc.iscorpinner=是否内部客商（股份内）
ft_pdoc_pdoc.isinit=是否初始化

# 应付票据结清分摊明细表
ft_pdoc_pdocg.pdocgicode=应付票据结清分摊明细表内码
ft_pdoc_pdocg.pdocicode=应付票据池内码
ft_pdoc_pdocg.purprjcode=采购业务编号
ft_pdoc_pdocg.purprjicode=采购业务编号内码
ft_pdoc_pdocg.purordcode=采购合同号
ft_pdoc_pdocg.purordicode=采购合同内码
ft_pdoc_pdocg.purshipcoder=到货单号
ft_pdoc_pdocg.purshipicoder=到货单号内码
ft_pdoc_pdocg.fcode=${fcode}
ft_pdoc_pdocg.sfcode=${sfcode}
ft_pdoc_pdocg.fserate=${fserate}
ft_pdoc_pdocg.scerate=${scerate}
ft_pdoc_pdocg.suerate=${suerate}
ft_pdoc_pdocg.fcy=${fcy}
ft_pdoc_pdocg.bcode=${bcode}
ft_pdoc_pdocg.wcode=${wcode}

# 采购合同待开票据金额核销对象余额表
ft_pdoc_pdocap_cb=采购合同待开票据金额核销对象余额表
ft_pdoc_pdocap_cb.purordssicode=采购合同分摊付款方式内码
ft_pdoc_pdocap_cb.purordicode=采购合同主表内码
ft_pdoc_pdocap_cb.corflag=标识
ft_pdoc_pdocap_cb.fcy=${planexecutefcy}
ft_pdoc_pdocap_cb.fcyed=${executefcy}
ft_pdoc_pdocap_cb.fcying=${unexecfcy}
ft_pdoc_pdocap_cb.purordcode=${purordcode}
ft_pdoc_pdocap_cb.outordcode=${puroutordcode}
ft_pdoc_pdocap_cb.purccode=${purcode}
ft_pdoc_pdocap_cb.signdate=${signdate}
ft_pdoc_pdocap_cb.prjicode=业务编号内码
ft_pdoc_pdocap_cb.prjcode=${busicode}
ft_pdoc_pdocap_cb.corpbcode=${corpbcode}
ft_pdoc_pdocap_cb.wcode=${wcode}
ft_pdoc_pdocap_cb.bcode=${bcode}
ft_pdoc_pdocap_cb.vprepare=${vprepare}
ft_pdoc_pdocap_cb.predate=${predate}
ft_pdoc_pdocap_cb.status=${status}
ft_pdoc_pdocap_cb.modifydate=${modifydate}
ft_pdoc_pdocap_cb.fcode=${fcode}
ft_pdoc_pdocap_cb.deptbcode=${bubcode}
ft_pdoc_pdocap_cb.paymode=${paymode}

# 采购合同待开票据金额核销对象记录表
ft_pdoc_pdocap_ct=采购合同待开票据金额核销对象记录表
ft_pdoc_pdocap_ct.tgticode=目标表主键
ft_pdoc_pdocap_ct.tgttbl=目标表名
ft_pdoc_pdocap_ct.purordssicode=采购合同分摊付款方式内码
ft_pdoc_pdocap_ct.fcy=${paydocfcy}

# 应付票据结清凭证视图
ft_pdoc_sett_view.pdocicode=应付票据池内码
ft_pdoc_sett_view.vcorpbcode=${corpbcode}
ft_pdoc_sett_view.vidflag=制证选项
ft_pdoc_sett_view.doctype=票据类型
ft_pdoc_sett_view.docno=票据号
ft_pdoc_sett_view.corpbcode=出具人
ft_pdoc_sett_view.accepter=承兑人
ft_pdoc_sett_view.ccode=${recccode}
ft_pdoc_sett_view.duedatefm=票据到期日从
ft_pdoc_sett_view.duedateto=到
ft_pdoc_sett_view.viddesc=${vid}
ft_pdoc_sett_view.duedate=${docduedate}
ft_pdoc_sett_view.bcode=${b_wcode}
ft_pdoc_sett_view.vcorpbcode=${corpbcode}
ft_pdoc_sett_view.isdiscount=是否贴现






