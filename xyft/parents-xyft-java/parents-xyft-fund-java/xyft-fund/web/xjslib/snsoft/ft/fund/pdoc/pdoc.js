Xjs.loadedXjs.push("snsoft/ft/fund/pdoc/pdoc");
Xjs.namespace("snsoft.ft.pdoc.app.invoker");snsoft.ft.pdoc.app.invoker.PayDocAppSendSerCancelInvoker=function(parameter){snsoft.ft.pdoc.app.invoker.PayDocAppSendSerCancelInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.pdoc.app.invoker.PayDocAppSendSerCancelInvoker,snsoft.ext.cmd.CommandInvoker,{check:function(event){return {type:2,showConfirm:new Xjs.FuncCall((fcall,ie)=>{var funcCall=new Xjs.FuncCall(this.confirmDeal,this,[fcall,ie]);Xjs.ui.UIUtil.showConfirmDialog(this.getTitle(),this.getResVal("FT-PDOC.10000001"),funcCall,["yes:是","no:否"]);},this,[event])};},confirmDeal:function(dialog,command,funcCall,event){if("no"==command){var status=this.mainDataSet.getValue("status");if(!(status==70)){var statusCodeData=Xjs.ui.aid.AidInfo.createAidInfo(new Xjs.ui.aid.AidInfoService$AidInfoParam("#SN-PLAT.status",null)).toCodeData(),statusEnabledName=statusCodeData.getCodeName1("70");throw new Error(Xjs.ResBundle.getResVal("FT.00000060",statusEnabledName));}var opdocstatus=this.mainDataSet.getValue("opdocstatus");if(!(opdocstatus=="10")){throw new Error(this.getResVal("FT-PDOC.00000003"));}funcCall.call(null,"ok");}},afterInvoke:function(event){this.mainTable.refreshTable();}});