Xjs.loadedXjs.push("snsoft/ft/fund/comm/comm");
/*snsoft/ft/comm/busi/FundAidAddTgbControlJSListener.java*/
Xjs.namespace("snsoft.ft.comm.busi");
snsoft.ft.comm.busi.FundAidAddTgbControlJSListener=function(params){
    snsoft.ft.comm.busi.FundAidAddTgbControlJSListener.superclass.constructor.call(this,params);
    this.columnNames = params.columnNames.split(",");
};
Xjs.extend(snsoft.ft.comm.busi.FundAidAddTgbControlJSListener,snsoft.plat.bas.busi.SystemFunctionListener,{
  _js$className_:"snsoft.ft.comm.busi.FundAidAddTgbControlJSListener",
    /*snsoft.ft.comm.busi.FundAidAddTgbControlJSListener.itemAidInputing*/
    itemAidInputing:function(table,e)
    {
        snsoft.ft.comm.busi.FundAidAddTgbControlJSListener.superclass.itemAidInputing.call(this,table,e);
        if(this.columnNames.indexOf(e.forTblColumn.name) > -1)
        {
            var aidInputer = e.forTblColumn.aidInputer;
            if(aidInputer instanceof Xjs.ui.SelectTableDataDialog)
            {
                var dialog = aidInputer,
                    dlgParam = dialog.dlgParam;
                if(!dlgParam)
                {
                    dlgParam = {};
                }
                dlgParam.status = "@26,70";
            }
        }
    }
});
/*snsoft/ft/comm/busi/JudgeNullFldsCopy.java*/
snsoft.ft.comm.busi.JudgeNullFldsCopy=function(params){
    snsoft.ft.comm.busi.JudgeNullFldsCopy.superclass.constructor.call(this,params);
    this.toFld = params.toFld;
    this.defaultFld = params.defaultFld;
    this.secondFld = params.secondFld;
};
Xjs.extend(snsoft.ft.comm.busi.JudgeNullFldsCopy,Xjs.ui.util.JudgeFldsCopy,{
  _js$className_:"snsoft.ft.comm.busi.JudgeNullFldsCopy",
    /*snsoft.ft.comm.busi.JudgeNullFldsCopy.judge*/
    judge:function(dataSet,row,fromName)
    {
        var judge = snsoft.ft.comm.busi.JudgeNullFldsCopy.superclass.judge.call(this,dataSet,row,fromName);
        if(judge && fromName == this.defaultFld)
        {
            if(dataSet.columnAt(this.defaultFld) > -1 && !dataSet.getValue(this.defaultFld))
            {
                if(dataSet.columnAt(this.secondFld) > -1)
                {
                    var secondFldValue = dataSet.getValue(this.secondFld,row),
                        table = dataSet.getTables()[0],
                        _$AidInputer = table._forColumnAidInputer,
                        t = _$AidInputer.parentComponent;
                    t.dataSet.setValue(this.toFld,secondFldValue);
                    return false;
                }
            }
        }
        return judge;
    }
});
/*snsoft/ft/comm/busi/JudgeSwiftCodeFldsCopy.java*/
snsoft.ft.comm.busi.JudgeSwiftCodeFldsCopy=function(params){
    snsoft.ft.comm.busi.JudgeSwiftCodeFldsCopy.superclass.constructor.call(this,params);
    this.defaultFld = params.defaultFld;
};
Xjs.extend(snsoft.ft.comm.busi.JudgeSwiftCodeFldsCopy,Xjs.ui.util.JudgeFldsCopy,{
  _js$className_:"snsoft.ft.comm.busi.JudgeSwiftCodeFldsCopy",
    /*snsoft.ft.comm.busi.JudgeSwiftCodeFldsCopy.judge*/
    judge:function(dataSet,row,fromName)
    {
        var judge = snsoft.ft.comm.busi.JudgeSwiftCodeFldsCopy.superclass.judge.call(this,dataSet,row,fromName);
        if(judge && fromName == this.defaultFld)
        {
            var swiftcode = dataSet.getValue("depositbanktype");
            if(!(swiftcode == "YHLB002"))
            {
                var table = dataSet.getTables()[0],
                    _$AidInputer = table._forColumnAidInputer,
                    t = _$AidInputer.parentComponent;
                t.dataSet.setValue("swiftcode",null);
                return false;
            }
        }
        return judge;
    }
});
/*snsoft/ft/comm/invoker/AddFundsrcInvoker.java*/
Xjs.namespace("snsoft.ft.comm.invoker");
snsoft.ft.comm.invoker.AddFundsrcInvoker=function(parameter){
    snsoft.ft.comm.invoker.AddFundsrcInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.comm.invoker.AddFundsrcInvoker,snsoft.ext.cmd.com.biz.ClickPullDownInvoker,{
  _js$className_:"snsoft.ft.comm.invoker.AddFundsrcInvoker",
    fundSrcMatch:"50,60",
    filterBcodeName:"bcode",
    /*snsoft.ft.comm.invoker.AddFundsrcInvoker.check*/
    check:function(event)
    {
        return this.newDialogWithFunc(event,(e)=>{
            if(this.autoSave)
            {
                if(this.updateModifydate && this.mainDataSet.isNewRow() && this.mainDataSet !== this.dataSet && !this.mainDataSet.isChanged(true))
                {
                    this.mainDataSet.updateLastModifyDate();
                }
                this.mainDataSet.saveChanges();
            }
            var indexOfColumn = this.table.indexOfColumn(this.tCell);
            if(indexOfColumn < 0)
            {
                return null;
            }
            this.dataSet.postRow();
            this.table.setSelectedColumn(indexOfColumn,true);
            var tColumn = this.table.getColumn(this.tCell),
                com = tColumn.getEditComponent(this.table);
            com.forTblColumn = tColumn;
            var aid = com.aidInputer;
            this.setValueSelectfunc(aid);
            var codeData = tColumn.selectOptions;
            this.filterCodeData(codeData);
            com.doAidInput({});
            aid.setTitle(this.getTitle());
            return aid;
        },(e,d)=>{
            var ts = Xjs.util.TableUtils.getAllTablesFromComponent(d,false);
            if(ts ? ts[0].getSelectedRowNumbers().length == 0 : !d.getValue())
            {
                throw this.getResVal("FT.00000048");
            }
            this.setSelectedRowNumberVals(e,d);
            event.checkData.showdlg = d;
        });
    },
    /*snsoft.ft.comm.invoker.AddFundsrcInvoker.filterCodeData*/
    filterCodeData:function(codeData)
    {
        var rootTable = this.table.getRootTable();
        if(!(rootTable.getValue("sheetcode") == "FT-DOCU.DocuAccOrd"))
        {
            return;
        }
        codeData.setLoadParameter("JSONFILTER",null);
        var paymode = rootTable.dataSet.getValue("paymode"),
            romode = rootTable.dataSet.getValue("romode");
        if(String.isStrIn("0075,0030",paymode,","))
        {
            codeData.setLoadParameter("JSONFILTER","{n:'code',op:'!=',v:'60'}");
        }
        if(String.isStrIn("0070,0025",paymode,","))
        {
            if(String.isStrIn("10",romode,","))
            {
                codeData.setLoadParameter("JSONFILTER","{n:'code',op:'!=',v:'60'}");
            }
        }
        if(String.isStrIn("0060",paymode,","))
        {
            if(String.isStrIn("60",romode,","))
            {
                codeData.setLoadParameter("JSONFILTER","{n:'code',op:'!=',v:'50'}");
            }
        } else if(String.isStrIn("0065",paymode,","))
        {
            codeData.setLoadParameter("JSONFILTER","{n:'code',op:'!=',v:'50'}");
        }
    },
    /*snsoft.ft.comm.invoker.AddFundsrcInvoker.buildSaveDatas*/
    buildSaveDatas:function(ike,d,showValues)
    {
        var saveMainData = this.buildSaveMainData();
        if(this.mainDataSet !== this.dataSet)
        {
            var gData = {tableName:this.dataSet.getTableName(),uiname:this.table.name,rows:[]},
                joinMasterCols = this.dataSet.joinMasterColumns,
                copyFMasterValues = this.dataSet.copyFMasterValues,
                grows = [],
                autoIncSet = {};
            this.createDataOverall(false,showValues,ike,grows,this.dataSet,joinMasterCols,copyFMasterValues,autoIncSet);
            if(grows.length == 0)
            {
                return null;
            }
            gData.rows = grows;
            saveMainData.SaveDatas.push(gData);
        }
        return saveMainData;
    },
    /*snsoft.ft.comm.invoker.AddFundsrcInvoker.insertDatas*/
    insertDatas:function(ive,d,showValues)
    {
        this.createDataOverall(true,showValues,null,null,null,null,null,null);
    },
    /*snsoft.ft.comm.invoker.AddFundsrcInvoker.addItem*/
    addItem:function(showValues,bcodes)
    {
        this.dataSet.insertRow(3);
        this.dataSet.setValue(this.tCell,showValues);
        this.dataSet.setValue(this.filterBcodeName,bcodes);
    },
    /*snsoft.ft.comm.invoker.AddFundsrcInvoker.repetition*/
    repetition:function(fundSrc,bcode)
    {
        for(var r=0;r < this.dataSet.getRowCount();r++)
        {
            if(fundSrc==this.dataSet.getValue(this.tCell,r) && bcode==this.dataSet.getValue(this.filterBcodeName,r))
            {
                return true;
            }
        }
        return false;
    },
    /*snsoft.ft.comm.invoker.AddFundsrcInvoker.collectBcode*/
    collectBcode:function()
    {
        var bcodes = [],
            filterTable = Xjs.util.TableUtils.getTable_$Panel_$String(this.mainTable,this.filterTableName);
        if(filterTable)
        {
            var filterTableDataSet = filterTable.getDataSet();
            for(var r=0;r < filterTableDataSet.getRowCount();r++)
            {
                var bcode = filterTableDataSet.getValue(this.filterBcodeName,r);
                if(bcode && bcodes.indexOf(bcode) < 0)
                {
                    bcodes.push(bcode);
                }
            }
        }
        return bcodes;
    },
    /*snsoft.ft.comm.invoker.AddFundsrcInvoker.buildInsertRows*/
    buildInsertRows:function(e,rows,dataSet,masterCols,copyFMasterValues,v,autoIncSet,bcode)
    {
        var o = {};
        this.setCellVal(o,v);
        o.bcode = bcode;
        var set = (ov,k,d)=>{
            for(var tgt in k)
            {
                var tc1 = k[tgt];
                ov[tgt] = d.getValue(tc1);
            }
        },
            tcs = e.table.getColumns().map((tc)=>{
            return tc.name;
        });
        tcs.remove(this.tCell);
        var keyColumnNames = e.dataSet.getKeyColumnNames();
        this.initCuInnerfldVal(e.table,Array.addElements(tcs,keyColumnNames),o,autoIncSet);
        set(o,masterCols,e.mainDataSet);
        set(o,copyFMasterValues,e.dataSet.fmaster);
        rows.push(null,o);
    },
    /*snsoft.ft.comm.invoker.AddFundsrcInvoker.createDataOverall*/
    createDataOverall:function(isClient,showValues,e,rows,dataSet,masterCols,copyFMasterValues,autoIncSet)
    {
        for(var i=0;i < showValues.length;i++)
        {
            var defulatBcode = this.dataSet.getColumn(this.filterBcodeName).defaultValue,
                bcodes = this.collectBcode();
            if(bcodes.length == 0)
            {
                bcodes = [defulatBcode];
            }
            if(this.table.getRootTable().getValue("sheetcode") == "FT-DOCU.DocuAccOrd")
            {
                for(var b=0;b < bcodes.length;b++)
                {
                    if(!this.repetition(showValues[i],bcodes[b]) || this.insertdup)
                    {
                        if(isClient)
                        {
                            this.addItem(showValues[i],bcodes[b]);
                        } else 
                        {
                            this.buildInsertRows(e,rows,dataSet,masterCols,copyFMasterValues,showValues[i],autoIncSet,bcodes[b]);
                        }
                    }
                }
            } else 
            {
                if(!(Xjs.util.DataSetUtils.getRowByValue_DataSet_String_Object(this.dataSet,this.tCell,showValues[i]) > -1) || this.insertdup)
                {
                    if(isClient)
                    {
                        this.addItem(showValues[i],bcodes[0]);
                    } else 
                    {
                        this.buildInsertRows(e,rows,dataSet,masterCols,copyFMasterValues,showValues[i],autoIncSet,bcodes[0]);
                    }
                }
            }
        }
    }
});
/*snsoft/ft/comm/invoker/CheckTblNotEmptyInvoker.java*/
snsoft.ft.comm.invoker.CheckTblNotEmptyInvoker=function(parameter){
    snsoft.ft.comm.invoker.CheckTblNotEmptyInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.comm.invoker.CheckTblNotEmptyInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.comm.invoker.CheckTblNotEmptyInvoker",
    errSrcNoDefault:"FT-RPADJ.10000006",
    /*snsoft.ft.comm.invoker.CheckTblNotEmptyInvoker.beforeCheck*/
    beforeCheck:function(event)
    {
        if(!this.dataSet.isOpen())
        {
            throw new Error(Xjs.ResBundle.getResVal(this.errSrcNo ? this.errSrcNo : this.errSrcNoDefault,event.table.title));
        }
        var checkTable = Xjs.util.TableUtils.getTable_$Panel_$String(this.mainTable,this.tableName);
        if(checkTable)
        {
            if(checkTable.dataSet.getRowCount() == 0)
            {
                throw new Error(Xjs.ResBundle.getResVal(this.errSrcNo ? this.errSrcNo : this.errSrcNoDefault,checkTable.title));
            }
        }
        return null;
    }
});
/*snsoft/ft/comm/invoker/DataShowChangeInvoker.java*/
snsoft.ft.comm.invoker.DataShowChangeInvoker=function(parameter){
    snsoft.ft.comm.invoker.DataShowChangeInvoker.superclass.constructor.call(this,parameter);
    this.ischeckstatus = parameter.ischeckstatus !== undefined ? parameter.ischeckstatus : true;
};
Xjs.extend(snsoft.ft.comm.invoker.DataShowChangeInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.comm.invoker.DataShowChangeInvoker",
    /*snsoft.ft.comm.invoker.DataShowChangeInvoker.beforeCheck*/
    beforeCheck:function(event)
    {
        if(this.ischeckstatus)
        {
            var status = this.mainDataSet.getValue("status");
            if(!(status == "70"))
            {
                var statusCodeData = Xjs.ui.aid.AidInfo.createAidInfo(new Xjs.ui.aid.AidInfoService$AidInfoParam("#SN-PLAT.status",null)).toCodeData(),
                    statusEnabledName = statusCodeData.getCodeName1("70");
                throw new Error(Xjs.ResBundle.getResVal("FT.00000060",statusEnabledName));
            }
        }
        return this.buildInvokeParam(null);
    },
    /*snsoft.ft.comm.invoker.DataShowChangeInvoker.invoke*/
    invoke:function(event)
    {
        this.showorigin = !this.showorigin;
        this.dataSet.setRefreshParameter("showorigin",this.showorigin);
        this.table.refreshTable();
        return null;
    }
});
/*snsoft/ft/comm/invoker/DeleteFundSrcOprateInvoker.java*/
snsoft.ft.comm.invoker.DeleteFundSrcOprateInvoker=function(parameter){
    snsoft.ft.comm.invoker.DeleteFundSrcOprateInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.comm.invoker.DeleteFundSrcOprateInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.comm.invoker.DeleteFundSrcOprateInvoker",
    /*snsoft.ft.comm.invoker.DeleteFundSrcOprateInvoker.beforeCheck*/
    beforeCheck:function(event)
    {
        var srcismanual = this.dataSet.get("srcismanual"),
            srccode = this.dataSet.get("srccode");
        if(srcismanual == "N" && srccode)
        {
            throw this.getResVal("FT-PAY.00000013");
        }
        return null;
    }
});
/*snsoft/ft/comm/invoker/ForceRefreshTableInvoker.java*/
snsoft.ft.comm.invoker.ForceRefreshTableInvoker=function(parameter){
    snsoft.ft.comm.invoker.ForceRefreshTableInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.comm.invoker.ForceRefreshTableInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.comm.invoker.ForceRefreshTableInvoker",
    /*snsoft.ft.comm.invoker.ForceRefreshTableInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        event.table.refreshTable();
    }
});
/*snsoft/ft/comm/invoker/FundCreditInfoRefreshInvoker.java*/
snsoft.ft.comm.invoker.FundCreditInfoRefreshInvoker=function(parameter){
    snsoft.ft.comm.invoker.FundCreditInfoRefreshInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.comm.invoker.FundCreditInfoRefreshInvoker,snsoft.ft.comm.cmdreg.CmdCommInvoker,{
  _js$className_:"snsoft.ft.comm.invoker.FundCreditInfoRefreshInvoker",
    /*snsoft.ft.comm.invoker.FundCreditInfoRefreshInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        snsoft.ft.comm.invoker.FundCreditInfoRefreshInvoker.superclass.afterInvoke.call(this,event);
        var fundCredit = Xjs.util.TableUtils.getTable_$Panel_$String(this.mainTable,"ft_fund_credit"),
            fundCreditInfo = Xjs.util.TableUtils.getTable_$Panel_$String(this.mainTable,"ft_fund_creditinfo");
        fundCredit.refreshTable(true);
        fundCreditInfo.refreshTable(true);
    }
});
/*snsoft/ft/comm/invoker/FundCredittypeEditInvoker.java*/
snsoft.ft.comm.invoker.FundCredittypeEditInvoker=function(parameter){
    snsoft.ft.comm.invoker.FundCredittypeEditInvoker.superclass.constructor.call(this,parameter);
    this.uiService = Xjs.RInvoke.newBean("FT-FUND.FundCreditUIService");
};
Xjs.extend(snsoft.ft.comm.invoker.FundCredittypeEditInvoker,snsoft.ft.comm.cmdreg.CellsModifyInvoker,{
  _js$className_:"snsoft.ft.comm.invoker.FundCredittypeEditInvoker",
    /*snsoft.ft.comm.invoker.FundCredittypeEditInvoker.onClick*/
    onClick:function(event)
    {
        var srccredittype = this.dataSet.getValue("srccredittype");
        if(srccredittype)
        {
            var creditctrlran = this.uiService.queryCreditctrlran(srccredittype);
            if(!("20" == creditctrlran))
            {
                throw new Error(Xjs.ResBundle.getResVal("FT-FUND.00000016"));
            }
        }
        return this.checkCreditInfo(this.dataSet,event);
    },
    /*snsoft.ft.comm.invoker.FundCredittypeEditInvoker.fireSVSave*/
    fireSVSave:function(event)
    {
        var saveParam = this.buildSaveMainData(),
            saveDatas = saveParam.SaveDatas;
        if(saveDatas)
        {
            var o = event.checkData.defvaluesCol,
                saveData = {rows:[]};
            saveData.tableName = event.dataSet.getTableName();
            saveData.uiname = event.dataSet.getTableName();
            var mainSaveData = saveDatas[0],
                mainRows = mainSaveData.rows;
            if(mainRows)
            {
                saveData.rows = mainRows;
                if(saveData.rows.length == 2)
                {
                    o.crediticode = event.dataSet.getValue("crediticode");
                    Xjs.apply(saveData.rows[1],o);
                }
            }
            this.replaceMode = 0;
            saveDatas.push(saveData);
            this.onOkSaveParam(saveParam,event);
        }
    },
    /*snsoft.ft.comm.invoker.FundCredittypeEditInvoker.afterSave*/
    afterSave:function(sp,event,rst)
    {
        snsoft.ft.comm.invoker.FundCredittypeEditInvoker.superclass.afterSave.call(this,sp,event,rst);
        var o = event.checkData.defvaluesCol,
            array = [o];
        event.dataSet.replaceValuesByTableName(event.dataSet.getTableName(),array);
        var ftFundCreditinfo = Xjs.util.TableUtils.getTable_$Panel_$String(this.mainTable,"ft_fund_creditinfo");
        ftFundCreditinfo.refreshTable();
        var ft_fund_financial = Xjs.util.TableUtils.getTable_$Panel_$String(this.mainTable,"ft_fund_credit");
        ft_fund_financial.refreshTable(true);
    },
    /*snsoft.ft.comm.invoker.FundCredittypeEditInvoker.checkCreditInfo*/
    checkCreditInfo:function(dataSet,event)
    {
        var crediticode = dataSet.getValue("crediticode"),
            types = this.uiService.hasCredittypeInfo(crediticode);
        if(types)
        {
            return {type:0,title:this.getTitle(),prompt:Xjs.ResBundle.getResVal("FT-FUND.00000015",types),perform:new Xjs.FuncCall(this._resolve,this,[event])};
        }
        return null;
    },
    /*snsoft.ft.comm.invoker.FundCredittypeEditInvoker._resolve*/
    _resolve:function(event)
    {
        event.flag = "yes";
    },
    /*snsoft.ft.comm.invoker.FundCredittypeEditInvoker.firePostOk*/
    firePostOk:function(e,d)
    {
        snsoft.ft.comm.invoker.FundCredittypeEditInvoker.superclass.firePostOk.call(this,e,d);
        var o = e.checkData.defvaluesCol,
            credittype = o.credittype,
            credittypes = credittype.split(",");
        if(credittypes.length == 1)
        {
            return;
        }
        var masterTable = this.table.masterTable,
            sheetcode = masterTable.getDataSet().getValue("sheetcode"),
            bcode = masterTable.getDataSet().getValue("bcode");
        if(sheetcode && bcode)
        {
            var isValid = this.uiService.checkCreditMixData(sheetcode,bcode,credittypes);
            if(!isValid)
            {
                throw new Error(Xjs.ResBundle.getResVal("FT-FUND.00000017"));
            }
        } else 
        {
            throw new Error(Xjs.ResBundle.getResVal("FT-FUND.00000018"));
        }
    }
});
/*snsoft/ft/comm/invoker/PushFundBatchInvoker.java*/
snsoft.ft.comm.invoker.PushFundBatchInvoker=function(parameter){
    snsoft.ft.comm.invoker.PushFundBatchInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.comm.invoker.PushFundBatchInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.comm.invoker.PushFundBatchInvoker",
    /*snsoft.ft.comm.invoker.PushFundBatchInvoker.invoke*/
    invoke:function(event)
    {
        return this.buildInvokeParam(null);
    },
    /*snsoft.ft.comm.invoker.PushFundBatchInvoker.check*/
    check:function(event)
    {
        var rows = event.table.getSelectedRowNumbers();
        if(rows.length < 1)
        {
            throw new Error(this.getResVal("FT.00000048"));
        }
        for(var i=0;i < rows.length;i++)
        {
            var r = rows[i],
                status = this.dataSet.getValue("status",r);
            if(!(status == "60"))
            {
                throw new Error(Xjs.ResBundle.getResVal("FT-FOREX.00000010"));
            }
            var isneedpfund = this.dataSet.getValue("isneedpfund",r);
            if("Y"!=isneedpfund)
            {
                throw new Error(Xjs.ResBundle.getResVal("FT-FOREX.00000052"));
            }
        }
        return {type:0,title:this.getTitle(),prompt:Xjs.ResBundle.getResVal("FT-FOREX.00000011")};
    },
    /*snsoft.ft.comm.invoker.PushFundBatchInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        this.mainTable.refreshTable(true,0,null);
        var invokeRtnVal = event.invokeRtnVal;
        if(invokeRtnVal)
        {
            var showErr = invokeRtnVal.showErr;
            if(showErr)
            {
                var err = {message:showErr};
                Xjs.alertErr(err);
                throw err;
            }
        }
    }
});
/*snsoft/ft/comm/invoker/PushFundBatchJSInvoker.java*/
snsoft.ft.comm.invoker.PushFundBatchJSInvoker=function(parameter){
    snsoft.ft.comm.invoker.PushFundBatchJSInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.comm.invoker.PushFundBatchJSInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.comm.invoker.PushFundBatchJSInvoker",
    /*snsoft.ft.comm.invoker.PushFundBatchJSInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        this.mainTable.refreshTable(true,0,null);
        var invokeRtnVal = event.invokeRtnVal;
        if(invokeRtnVal)
        {
            var showErr = invokeRtnVal.showErr;
            if(showErr)
            {
                var err = {message:showErr};
                Xjs.alertErr(err);
                throw err;
            }
        }
    }
});
/*snsoft/ft/comm/invoker/QueryResultBtnInvoker.java*/
snsoft.ft.comm.invoker.QueryResultBtnInvoker=function(param){
    snsoft.ft.comm.invoker.QueryResultBtnInvoker.superclass.constructor.call(this,param);
};
Xjs.extend(snsoft.ft.comm.invoker.QueryResultBtnInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.comm.invoker.QueryResultBtnInvoker",
    /*snsoft.ft.comm.invoker.QueryResultBtnInvoker.beforeCheck*/
    beforeCheck:function(event)
    {
        var rows = this.table.getSelectedRowNumbers();
        if(rows == null || rows.length == 0)
        {
            throw new Error(Xjs.ResBundle.getResVal("FT.00000048"));
        } else if(rows.length > 1)
        {
            throw new Error(Xjs.ResBundle.getResVal("FT.00000087"));
        }
        var status = this.dataSet.getValue("status",rows[0]);
        if(!(status == "70"))
        {
            var statusCodeData = Xjs.ui.aid.AidInfo.createAidInfo(new Xjs.ui.aid.AidInfoService$AidInfoParam("#SN-PLAT.status",null)).toCodeData(),
                statusEnabledName = statusCodeData.getCodeName1("70");
            throw Xjs.ResBundle.getResVal("FT.00000060",statusEnabledName);
        }
        return this.buildInvokeParam((rs,row)=>{
            rs.innercode = this.dataSet.get(this.dataSet.getKeyColumnNames()[0]);
            rs.sheetcode = this.dataSet.get("sheetcode");
        });
    },
    /*snsoft.ft.comm.invoker.QueryResultBtnInvoker.invoke*/
    invoke:function(event)
    {
        var dialog = Xjs.ui.UIUtil.loadDialog("FT-PDOC.PayDocAppQueryResultDlg");
        dialog.title = "票据开票进度查询";
        dialog.setWidth("auto");
        dialog.setHeight("70%");
        var docResult = event.beforeCheckRtnVal.docResult,
            details = docResult.details,
            queryResultTable = Xjs.util.TableUtils.getTable_$Panel_$String(dialog,"ft_pdoc_queryResult"),
            queryResultDataSet = queryResultTable.getDataSet();
        queryResultDataSet.insertRow(3);
        queryResultDataSet.set("succnum",docResult.succnum);
        queryResultDataSet.set("succfcy",docResult.succfcy);
        var queryResultDetailTable = Xjs.util.TableUtils.getTable_$Panel_$String(dialog,"ft_pdoc_queryResultDetail"),
            queryResultDetailDataSet = queryResultDetailTable.getDataSet();
        details.forEach((detail)=>{
            queryResultDetailDataSet.insertRow(3);
            queryResultDetailDataSet.set("doctype",detail.doctype);
            queryResultDetailDataSet.set("doccode",detail.doccode);
            queryResultDetailDataSet.set("docno",detail.docno);
            queryResultDetailDataSet.set("subdocfm",detail.subdocfm);
            queryResultDetailDataSet.set("subdocto",detail.subdocto);
            queryResultDetailDataSet.set("fcy",detail.fcy);
            queryResultDetailDataSet.set("docstatus",detail.docstatus);
        });
        dialog.showModal();
        return null;
    }
});
/*snsoft/ft/comm/invoker/SendATSInvoker.java*/
snsoft.ft.comm.invoker.SendATSInvoker=function(parameter){
    snsoft.ft.comm.invoker.SendATSInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.comm.invoker.SendATSInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.comm.invoker.SendATSInvoker",
    /*snsoft.ft.comm.invoker.SendATSInvoker.beforeCheck*/
    beforeCheck:function(event)
    {
        if(this.dataSet.getRowCount() < 1)
        {
            throw new Error(Xjs.ResBundle.getResVal("FT.00000089",this.getResVal("title.F.tab.fpsrc")));
        }
        this.table.checkNonBlankForSubmit();
        return {innercode:this.mainDataSet.get(this.mainDataSet.getCuInnerfld()),sheetcode:this.mainDataSet.get("sheetcode")};
    },
    /*snsoft.ft.comm.invoker.SendATSInvoker.invoke*/
    invoke:function(event)
    {
        return this.buildInvokeParam(null);
    },
    /*snsoft.ft.comm.invoker.SendATSInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        this.mainTable.refreshTable();
    }
});
/*snsoft/ft/comm/invoker/SendSerCancelInvoker.java*/
snsoft.ft.comm.invoker.SendSerCancelInvoker=function(parameter){
    snsoft.ft.comm.invoker.SendSerCancelInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.comm.invoker.SendSerCancelInvoker,snsoft.plat.bas.sheet.cmd.CommandSheet,{
  _js$className_:"snsoft.ft.comm.invoker.SendSerCancelInvoker",
    /*snsoft.ft.comm.invoker.SendSerCancelInvoker.invoke*/
    invoke:function(event)
    {
        var innercode = this.getInnercode();
        if(innercode == null)
            return null;
        var parameter = this.buildSheetRecordParameter();
        parameter.statusMatchRules = this.statusMatchRules;
        return parameter;
    },
    /*snsoft.ft.comm.invoker.SendSerCancelInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        if(event.invokeRtnVal)
            snsoft.ft.utils.FTUtils.replaceValueAndAutoRefresh(event.table,event.invokeRtnVal.tableRowValues);
    }
});
/*snsoft/ft/comm/invoker/SupFundsrcInvoker.java*/
snsoft.ft.comm.invoker.SupFundsrcInvoker=function(parameter){
    snsoft.ft.comm.invoker.SupFundsrcInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.comm.invoker.SupFundsrcInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.comm.invoker.SupFundsrcInvoker",
    /*snsoft.ft.comm.invoker.SupFundsrcInvoker.check*/
    check:function(event)
    {
        return this.newDialogWithFunc(event,(e)=>{
            var innerfld = this.mainDataSet.getCuInnerfld(),
                inner = this.mainDataSet.get(innerfld),
                param = {};
            param[innerfld] = inner;
            var initVals = {param:param},
                dialogPane = Xjs.ui.UIUtil.loadDialog(this.muiid,0,null,{width:800,height:450},null,initVals),
                srcTable = dialogPane.getItemByName(this.fundSrcTableName);
            srcTable.getColumn("srcsheetcode").setVisible(false);
            dialogPane.showModal();
            return dialogPane;
        },(e,d)=>{
            d.getItemByName(this.dialogTableName).checkNonBlankForSubmit();
            var dialogTable = d.getItemByName(this.dialogTableName);
            dialogTable.saveChanges();
            this.mainTable.refreshTable();
        });
    }
});
/*snsoft/ft/comm/lis/FundChkFldCodeDataJSListener.java*/
Xjs.namespace("snsoft.ft.comm.lis");
snsoft.ft.comm.lis.FundChkFldCodeDataJSListener=function(params){
    snsoft.ft.comm.lis.FundChkFldCodeDataJSListener.superclass.constructor.call(this,params);
};
Xjs.extend(snsoft.ft.comm.lis.FundChkFldCodeDataJSListener,snsoft.plat.bas.busi.SystemFunctionListener,{
  _js$className_:"snsoft.ft.comm.lis.FundChkFldCodeDataJSListener",
    refSheetcodeCol:"sheetcode",
    /*snsoft.ft.comm.lis.FundChkFldCodeDataJSListener.itemAidInputing*/
    itemAidInputing:function(table,e)
    {
        snsoft.ft.comm.lis.FundChkFldCodeDataJSListener.superclass.itemAidInputing.call(this,table,e);
        if(this.chkFldNames && String.isStrIn(this.chkFldNames,e.forTblColumn.name))
        {
            if(this.canFilter(table))
            {
                this.setChkFldCodeDataLoadParams(table,e.forTblColumn.name);
            }
        }
    },
    /*snsoft.ft.comm.lis.FundChkFldCodeDataJSListener.canFilter*/
    canFilter:function(table)
    {
        if(this.noFilterSysRptype)
        {
            var rptype = table.dataSet.getValue("rptype"),
                noFilterRptypes = window.EnvParameter[this.noFilterSysRptype];
            if(noFilterRptypes)
            {
                return !String.isStrIn(noFilterRptypes,rptype);
            }
        }
        if(this.refSheetcode)
        {
            var refTable = Xjs.util.TableUtils.getTable_$Panel_$String(table,this.refTblName);
            return refTable.dataSet.getValue(this.refSheetcodeCol) == this.refSheetcode;
        }
        if(this.filterSysRptype)
        {
            var rptype = table.dataSet.getValue("rptype"),
                filterRptypes = window.EnvParameter[this.filterSysRptype];
            if(filterRptypes)
            {
                return String.isStrIn(filterRptypes,rptype);
            }
        }
        return true;
    },
    /*snsoft.ft.comm.lis.FundChkFldCodeDataJSListener.setChkFldCodeDataLoadParams*/
    setChkFldCodeDataLoadParams:function(table,colname)
    {
        var column = table.getColumn(colname);
        if(column)
        {
            var aidInputer = column.aidInputer;
            if(aidInputer instanceof Xjs.ui.SelectTableDataDialog)
            {
                var selectTableDataDialog = aidInputer;
                selectTableDataDialog.addDlgParam(this.filterColName,this.refTblName + "." + this.refColName);
                if(this.isRdonly)
                {
                    selectTableDataDialog.addParamItemsCtrl(this.filterColName,{readOnly:true});
                }
            }
        }
    }
});
/*snsoft/ft/comm/lis/FundCreditFinancialInfoJSListener.java*/
snsoft.ft.comm.lis.FundCreditFinancialInfoJSListener=function(params){
    snsoft.ft.comm.lis.FundCreditFinancialInfoJSListener.superclass.constructor.call(this,params);
};
Xjs.extend(snsoft.ft.comm.lis.FundCreditFinancialInfoJSListener,snsoft.plat.bas.busi.SystemFunctionListener,{
  _js$className_:"snsoft.ft.comm.lis.FundCreditFinancialInfoJSListener",
    /*snsoft.ft.comm.lis.FundCreditFinancialInfoJSListener.dataSetRefreshing*/
    dataSetRefreshing:function(dataSet,e)
    {
        var sheetcode = dataSet.master.getValue("sheetcode");
        if(!(sheetcode == "FT-PAY.PayApp"))
        {
            return;
        }
        var rdate = dataSet.master.getValue("rdate"),
            innercode = dataSet.master.getKeyValues(),
            refreshParam = e.refreshParam;
        if(!refreshParam)
        {
            refreshParam = {};
            e.refreshParam = refreshParam;
        }
        refreshParam.sheetcode = sheetcode;
        refreshParam.rdate = rdate;
        for(var k in innercode)
            refreshParam.innercode = innercode[k];
        snsoft.ft.comm.lis.FundCreditFinancialInfoJSListener.superclass.dataSetRefreshing.call(this,dataSet,e);
    }
});
/*snsoft/ft/comm/lis/FundCreditInfoCtrlJSListener.java*/
snsoft.ft.comm.lis.FundCreditInfoCtrlJSListener=function(params){
    snsoft.ft.comm.lis.FundCreditInfoCtrlJSListener.superclass.constructor.call(this,params);
};
Xjs.extend(snsoft.ft.comm.lis.FundCreditInfoCtrlJSListener,snsoft.plat.bas.busi.SystemFunctionListener,{
  _js$className_:"snsoft.ft.comm.lis.FundCreditInfoCtrlJSListener",
    /*snsoft.ft.comm.lis.FundCreditInfoCtrlJSListener.initComponent*/
    initComponent:function(table,values)
    {
        snsoft.ft.comm.lis.FundCreditInfoCtrlJSListener.superclass.initComponent.call(this,table,values);
        if(this.t == null)
        {
            this.t = table;
        }
    },
    /*snsoft.ft.comm.lis.FundCreditInfoCtrlJSListener.onDataLoad*/
    onDataLoad:function(dataSet,e)
    {
        snsoft.ft.comm.lis.FundCreditInfoCtrlJSListener.superclass.onDataLoad.call(this,dataSet,e);
        this.ctrlCreditInfoTab();
    },
    /*snsoft.ft.comm.lis.FundCreditInfoCtrlJSListener.ctrlCreditInfoTab*/
    ctrlCreditInfoTab:function()
    {
        var r = this.t.getRootTable(),
            creditinfo = Xjs.util.TableUtils.getTable_$Panel_$String(r,"ft_fund_credit"),
            panel = this.getTabPanel(creditinfo);
        if(panel)
        {
            var uiService = Xjs.RInvoke.newBean("FT-FUND.FundCreditUIService"),
                dataSet = r.dataSet,
                sheetcode = dataSet.getValue("sheetcode"),
                innercode = dataSet.getValue(dataSet.getCuInnerfld()),
                flag = false;
            if(sheetcode && innercode)
            {
                flag = uiService.ctrlCreditInfoVisible(sheetcode,innercode);
            }
            panel.parent.setTabVisible(panel,flag);
            var gridTable = panel.getItemByName("ft_fund_credit");
            gridTable.dataSet.refresh();
        }
    },
    /*snsoft.ft.comm.lis.FundCreditInfoCtrlJSListener.getTabPanel*/
    getTabPanel:function(c)
    {
        if(!c)
        {
            return null;
        }
        if(c instanceof Xjs.ui.Panel && c.name == "creditinfo")
        {
            return c;
        }
        return this.getTabPanel(c.parent);
    },
    /*snsoft.ft.comm.lis.FundCreditInfoCtrlJSListener.dataSetSaved*/
    dataSetSaved:function(dataSet,e)
    {
        snsoft.ft.comm.lis.FundCreditInfoCtrlJSListener.superclass.dataSetSaved.call(this,dataSet,e);
        this.ctrlCreditInfoTab();
    }
});
/*snsoft/ft/comm/lis/FundCreditInfoJSListener.java*/
snsoft.ft.comm.lis.FundCreditInfoJSListener=function(params){
    snsoft.ft.comm.lis.FundCreditInfoJSListener.superclass.constructor.call(this,params);
};
Xjs.extend(snsoft.ft.comm.lis.FundCreditInfoJSListener,snsoft.plat.bas.busi.SystemFunctionListener,{
  _js$className_:"snsoft.ft.comm.lis.FundCreditInfoJSListener",
    /*snsoft.ft.comm.lis.FundCreditInfoJSListener.dataSetRefreshing*/
    dataSetRefreshing:function(dataSet,e)
    {
        var sheetcode = dataSet.master.getValue("sheetcode"),
            innercode = dataSet.master.getKeyValues(),
            refreshParam = e.refreshParam;
        if(!refreshParam)
        {
            refreshParam = {};
            e.refreshParam = refreshParam;
        }
        refreshParam.sheetcode = sheetcode;
        for(var k in innercode)
            refreshParam.innercode = innercode[k];
        snsoft.ft.comm.lis.FundCreditInfoJSListener.superclass.dataSetRefreshing.call(this,dataSet,e);
    },
    /*snsoft.ft.comm.lis.FundCreditInfoJSListener.dataSetRowNavigated*/
    dataSetRowNavigated:function(dataSet,e)
    {
        snsoft.ft.comm.lis.FundCreditInfoJSListener.superclass.dataSetRowNavigated.call(this,dataSet,e);
        var tables = dataSet.getTables(),
            gridTable = tables[0],
            isguaranteeColumn = gridTable.getColumn("isguarantee"),
            guaranteedescColumn = gridTable.getColumn("guaranteedesc"),
            mainccode = dataSet.getValue("mainccode"),
            creditcretatemode = dataSet.getValue("creditcretatemode"),
            isguarantee = dataSet.getValue("isguarantee"),
            isguaranteeReadOnly = false,
            guaranteedescReadOnly = false,
            isguaranteeNoblank = false,
            guaranteedescNoblank = false;
        if(!("15" == creditcretatemode) || "15" == creditcretatemode && mainccode)
        {
            isguaranteeReadOnly = true;
            guaranteedescReadOnly = true;
        }
        if("15" == creditcretatemode && !mainccode)
        {
            isguaranteeNoblank = true;
            if("Y" == isguarantee)
            {
                guaranteedescNoblank = true;
            } else if("N" == isguarantee)
            {
                var guaranteedesc = dataSet.getValue("guaranteedesc");
                if(guaranteedesc)
                {
                    dataSet.setValue("guaranteedesc",null);
                }
                guaranteedescReadOnly = true;
            }
        }
        isguaranteeColumn.readOnly = isguaranteeReadOnly;
        isguaranteeColumn.nonBlankOnSubmit = isguaranteeNoblank;
        guaranteedescColumn.readOnly = guaranteedescReadOnly;
        guaranteedescColumn.nonBlankOnSubmit = guaranteedescNoblank;
        gridTable.render(2);
    },
    /*snsoft.ft.comm.lis.FundCreditInfoJSListener.dataSetFieldPosted*/
    dataSetFieldPosted:function(dataSet,event)
    {
        snsoft.ft.comm.lis.FundCreditInfoJSListener.superclass.dataSetFieldPosted.call(this,dataSet,event);
        if(!("isguarantee" == event.columnName))
        {
            return;
        }
        var table = dataSet.getTables()[0],
            guaranteedesc = table.getColumn("guaranteedesc"),
            isguarantee = dataSet.getValue("isguarantee"),
            creditcretatemode = dataSet.getValue("creditcretatemode"),
            mainccode = dataSet.getValue("mainccode");
        if("15" == creditcretatemode && !mainccode)
        {
            if("Y" == isguarantee)
            {
                guaranteedesc.nonBlankOnSubmit = true;
                guaranteedesc.readOnly = false;
            } else 
            {
                dataSet.setValue("guaranteedesc",null);
                guaranteedesc.readOnly = true;
                guaranteedesc.nonBlankOnSubmit = false;
            }
        }
        table.render(2);
    },
    /*snsoft.ft.comm.lis.FundCreditInfoJSListener.dataSetRowDeleted*/
    dataSetRowDeleted:function(dataSet,e)
    {
        snsoft.ft.comm.lis.FundCreditInfoJSListener.superclass.dataSetRowDeleted.call(this,dataSet,e);
        var table = dataSet.master.getTables()[0],
            fundCreditInfo = Xjs.util.TableUtils.getTable_$Panel_$String(table,"ft_fund_credit");
        fundCreditInfo.refreshTable(true);
    }
});
/*snsoft/ft/comm/lis/FundCreditInfoRefreshJSListener.java*/
snsoft.ft.comm.lis.FundCreditInfoRefreshJSListener=function(params){
    snsoft.ft.comm.lis.FundCreditInfoRefreshJSListener.superclass.constructor.call(this,params);
};
Xjs.extend(snsoft.ft.comm.lis.FundCreditInfoRefreshJSListener,snsoft.plat.bas.busi.SystemFunctionListener,{
  _js$className_:"snsoft.ft.comm.lis.FundCreditInfoRefreshJSListener",
    /*snsoft.ft.comm.lis.FundCreditInfoRefreshJSListener.initComponent*/
    initComponent:function(table,values)
    {
        snsoft.ft.comm.lis.FundCreditInfoRefreshJSListener.superclass.initComponent.call(this,table,values);
    },
    /*snsoft.ft.comm.lis.FundCreditInfoRefreshJSListener.dataSetSaved*/
    dataSetSaved:function(dataSet,e)
    {
        snsoft.ft.comm.lis.FundCreditInfoRefreshJSListener.superclass.dataSetSaved.call(this,dataSet,e);
        var tableRowValuesArray = snsoft.ft.utils.FTUtils.buildTableRowValues(e.saveInfo);
        if(tableRowValuesArray && tableRowValuesArray.length > 0)
        {
            snsoft.ft.utils.FTUtils.replaceValueAndAutoRefresh(dataSet.getTables()[0],tableRowValuesArray.filter((tr)=>{
            return tr.replaceValues.some((r)=>{
            return "$op$" in r;
        });
        }));
        }
    }
});
/*snsoft/ft/comm/lis/FundCreditMainInfoJSListener.java*/
snsoft.ft.comm.lis.FundCreditMainInfoJSListener=function(params){
    snsoft.ft.comm.lis.FundCreditMainInfoJSListener.superclass.constructor.call(this,params);
};
Xjs.extend(snsoft.ft.comm.lis.FundCreditMainInfoJSListener,snsoft.plat.bas.busi.SystemFunctionListener,{
  _js$className_:"snsoft.ft.comm.lis.FundCreditMainInfoJSListener",
    /*snsoft.ft.comm.lis.FundCreditMainInfoJSListener.initComponent*/
    initComponent:function(table,values)
    {
        snsoft.ft.comm.lis.FundCreditMainInfoJSListener.superclass.initComponent.call(this,table,values);
        if(this.mainTable == null)
        {
            this.mainTable = table;
        }
    },
    /*snsoft.ft.comm.lis.FundCreditMainInfoJSListener.dataSetRefreshing*/
    dataSetRefreshing:function(dataSet,e)
    {
        var sheetcode = dataSet.master.getValue("sheetcode"),
            innercode = dataSet.master.getKeyValues(),
            refreshParam = e.refreshParam;
        if(!refreshParam)
        {
            refreshParam = {};
            e.refreshParam = refreshParam;
        }
        refreshParam.sheetcode = sheetcode;
        for(var k in innercode)
            refreshParam.innercode = innercode[k];
        snsoft.ft.comm.lis.FundCreditMainInfoJSListener.superclass.dataSetRefreshing.call(this,dataSet,e);
    },
    /*snsoft.ft.comm.lis.FundCreditMainInfoJSListener.onCreateRGridRecord*/
    onCreateRGridRecord:function(table,gr,e)
    {
        snsoft.ft.comm.lis.FundCreditMainInfoJSListener.superclass.onCreateRGridRecord.call(this,table,gr,e);
        var gRecords = table.gRecords;
        for(var gRecord of gRecords)
        {
            var rdom = gRecord.dom;
            if(rdom)
            {
                var fun = (mounsre,f,grd)=>{
            for(var column of grd.columns)
            {
                var t = mounsre.srcElement || mounsre.target;
                if(column instanceof Xjs.ui.InputField)
                {
                    if(Xjs.DOM.contains(column.dom,t))
                    {
                        table.postPending(true);
                        var data = table.dataSet.getRowColValues(null,grd.row),
                            cfsheetcode = data.cfsheetcode,
                            cfinnercode = data.cfinnercode,
                            serv = Xjs.RInvoke.newBean("SN-Busi.SheetService"),
                            busiObject = serv.getBusiObject(cfsheetcode),
                            json = {};
                        json.sheetcode = cfsheetcode;
                        json[busiObject.innerfld] = cfinnercode;
                        snsoft.plat.bas.viewdetail.ViewDetailListener.invokeViewDetail(json,busiObject.outerfld);
                        break;
                    }
                }
            }
        };
                rdom.ondblclick = Function.bindAsEventListener(fun.createDelegate(fun,[gRecord],true),this,0,true);
            }
        }
    },
    /*snsoft.ft.comm.lis.FundCreditMainInfoJSListener.onRGridRecordRender*/
    onRGridRecordRender:function(table,gr,e)
    {
        snsoft.ft.comm.lis.FundCreditMainInfoJSListener.superclass.onRGridRecordRender.call(this,table,gr,e);
        var menuNodes = table.getMenuNodes();
        for(var i=0;i < menuNodes.length;i++)
        {
            var menuNode = menuNodes[i],
                command = menuNode.node.command,
                btnDOM = Xjs.DOM.findById("UI_" + table.name + "_btn_" + command,gr.dom);
            if(btnDOM)
            {
                var fn = this._onClickRGridTableRowBtn.createDelegate(this,[table,gr,menuNode.node],true);
                btnDOM.onclick = fn;
            }
        }
        this.titleReplace(table,gr);
        this.ctrlCreditBtn(table,gr);
        this.ctrlFinancial(table,gr);
    },
    /*snsoft.ft.comm.lis.FundCreditMainInfoJSListener.ctrlFinancial*/
    ctrlFinancial:function(table,gr)
    {
        var rootTable = table.getRootTable(),
            sheetcode = rootTable.getDataSet().getValue("sheetcode"),
            btnDOM = Xjs.DOM.findById("UIA_ft_fund_credit_financialinfo",gr.dom);
        if(!(sheetcode == "FT-PAY.PayApp"))
        {
            if(btnDOM)
            {
                Xjs.DOM.addOrRemoveClass(btnDOM,Xjs.ui.TableCellBtnHtml.hiddenCls,true);
            }
            return;
        }
        var arr = this.getCreditinfoMainccodes(table,gr),
            dataSet = table.dataSet;
        if(dataSet)
        {
            var credittype = table.dataSet.getValue("credittype",gr.row),
                flag = false;
            if(credittype && (credittype.indexOf("160") != -1 || credittype.indexOf("100") != -1))
            {
                flag = true;
            }
            if(arr.length == 0)
            {
                flag = false;
            } else 
            {
                for(var i=0;i < arr.length;i++)
                {
                    var mainccode = arr[i];
                    if(mainccode)
                    {
                        flag = false;
                        break;
                    }
                }
            }
            if(btnDOM)
            {
                Xjs.DOM.addOrRemoveClass(btnDOM,Xjs.ui.TableCellBtnHtml.hiddenCls,!flag);
            }
        }
    },
    /*snsoft.ft.comm.lis.FundCreditMainInfoJSListener.getCreditinfoMainccodes*/
    getCreditinfoMainccodes:function(table,gr)
    {
        var arr = [],
            block = table.getSubGridTableBlock(gr.row,"ft_fund_creditinfo"),
            crediticodeMain = table.dataSet.getValue("crediticode",gr.row),
            creditinfoDataset = block.gTbl.getDataSet(),
            row = creditinfoDataset.getRowCount();
        for(var i=0;i < row;i++)
        {
            var string = creditinfoDataset.getValue("crediticode",i),
                mainccode = creditinfoDataset.getValue("mainccode",i);
            if(crediticodeMain == string)
            {
                arr.push(mainccode);
            }
        }
        return arr;
    },
    /*snsoft.ft.comm.lis.FundCreditMainInfoJSListener.ctrlCreditBtn*/
    ctrlCreditBtn:function(table,gr)
    {
        var rootTable = table.getRootTable(),
            status = rootTable.dataSet.getValue("status"),
            hasLimit = snsoft.ft.utils.FTUtils.hasLimit(this.mainTable,this.mainTable.dataSet.getRow()),
            btnDOM = Xjs.DOM.findById("UI_" + table.name + "_btn_" + "queryCreditInfo",gr.dom),
            flag = false;
        if(status && status > 20 || !hasLimit)
        {
            flag = true;
        }
        Xjs.DOM.addOrRemoveClass(btnDOM,Xjs.ui.TableCellBtnHtml.disabledCls,flag);
    },
    /*snsoft.ft.comm.lis.FundCreditMainInfoJSListener.titleReplace*/
    titleReplace:function(table,gr)
    {
        var dataSet = table.dataSet;
        if(dataSet)
        {
            var cfcode = table.dataSet.getValue("cfcode",gr.row),
                cfcodeCodeData = Xjs.ui.aid.AidInfo.createAidInfo(new Xjs.ui.aid.AidInfoService$AidInfoParam("#FT-BCFG.Checkfld",null)).toCodeData(),
                title = cfcodeCodeData.getCodeName1(cfcode),
                columns = gr.columns;
            for(var i=0;i < columns.length;i++)
            {
                var column = columns[i];
                if(column.name == "cfoutercode" && title)
                {
                    column.setLabelText(title);
                    break;
                }
            }
        }
    },
    /*snsoft.ft.comm.lis.FundCreditMainInfoJSListener._onClickRGridTableRowBtn*/
    _onClickRGridTableRowBtn:function(e,table,gr,node)
    {
        try
        {
            table.dataSet.gotoRow(gr.row);
            table.performCommand(node,node.command);
        }catch(ex)
        {
            Xjs.alertErr(ex);
        }
    },
    /*snsoft.ft.comm.lis.FundCreditMainInfoJSListener.oncmd_queryCreditInfo*/
    oncmd_queryCreditInfo:function(event)
    {
        event.getRootTable().saveChanges();
        var crediticode = event.getDataSet().getValue("crediticode"),
            fundCreditUIService = Xjs.RInvoke.newBean("FT-FUND.FundCreditUIService"),
            container = snsoft.ft.utils.FTUtils.initModifyVContainer(event.getRootTable().dataSet);
        fundCreditUIService.checkQueryCreditInfoBtn(container.innercode,container.sheetcode,crediticode,container);
        var creditInfoTable = event.getRootTable().parent.getItemByName("ft_fund_creditinfo"),
            creditInfoDataSet = creditInfoTable.dataSet,
            columns = creditInfoTable.getColumns(),
            rowDatas = [];
        for(var i=0;i < creditInfoTable.dataSet.getRowCount();i++)
        {
            var rowData = creditInfoDataSet.getRowData(i),
                map = {};
            for(var j=0;j < columns.length;j++)
            {
                map[columns[j].name] = rowData[j];
            }
            rowDatas.push(map);
        }
        var sumCredit = 0;
        for(var i=0;i < rowDatas.length;i++)
        {
            var object = rowDatas[i];
            if(object.crediticode==crediticode)
            {
                sumCredit++;
            }
        }
        if(sumCredit > 0)
        {
            Xjs.ui.UIUtil.showConfirmDialog(Xjs.ResBundle.getResVal("FT.cmd_queryCreditInfo"),Xjs.ResBundle.getResVal("FT-ORD.00000064"),new Xjs.FuncCall(this.creditInfoDataSetRefreshing,this,[event]),null,2);
        } else 
        {
            this.creditInfoDataSetRefreshing(null,null,null,event);
        }
    },
    /*snsoft.ft.comm.lis.FundCreditMainInfoJSListener.creditInfoDataSetRefreshing*/
    creditInfoDataSetRefreshing:function(dlgPane,btn,command,table)
    {
        if(dlgPane != null)
        {
            dlgPane.closeModal();
        }
        var rootTable = table.getRootTable(),
            crediticode = table.getDataSet().getValue("crediticode"),
            innercode = rootTable.dataSet.getValue(table.getRootTable().dataSet.getCuInnerfld()),
            sheetcode = rootTable.dataSet.getValue("sheetcode"),
            bcode = rootTable.dataSet.getValue("bcode"),
            fundCreditUIService = Xjs.RInvoke.newBean("FT-FUND.FundCreditUIService"),
            resultObject = fundCreditUIService.queryCreditInfoByCreditMainInfo(sheetcode,innercode,crediticode),
            creditInfos = resultObject.creditInfos;
        if(creditInfos != null && creditInfos.length > 0)
        {
            var params = resultObject.params,
                dlg = Xjs.ui.UIUtil.loadDialog("FT-FUND.CreditInfoAddPop");
            dlg.setWidth(1100);
            dlg.setHeight(680);
            var creditInfoAdd = dlg.getItemByName("ft_fund_creditinfo");
            creditInfoAdd.refreshTable();
            var queryPane = dlg.getItemByName("param");
            queryPane.setItemValue("bcode",bcode);
            queryPane.setItemValue("sheetcode",sheetcode);
            queryPane.setItemValue("innercode",innercode);
            var limitTypeList = params.limitTypeList;
            if(limitTypeList != null && limitTypeList.length > 0)
            {
                queryPane.setItemValue("credittype",limitTypeList.join(","));
            }
            queryPane.setItemValue("creditapcode",params.limitReqNumber);
            queryPane.setItemValue("specialname",params.specialProjectType);
            queryPane.setItemValue("creditclass",params.creditTypeCd);
            var custVendorList = params.custVendorList;
            if(custVendorList != null && custVendorList.length > 0)
            {
                queryPane.setItemValue("ccode",custVendorList.join(","));
            }
            queryPane.setItemValue("isrquotatype",params.creditLimitType);
            queryPane.setItemValue("domabr",params.overseasTypeCd);
            var authSmallKindList = params.authSmallKindList;
            if(authSmallKindList != null && authSmallKindList.length > 0)
            {
                queryPane.setItemValue("authvtscode",authSmallKindList.join(","));
            }
            var creditInfoAddDataSet = creditInfoAdd.dataSet;
            for(var i=0;i < creditInfos.length;i++)
            {
                creditInfoAddDataSet.insertRow(3);
                var object = creditInfos[i];
                for(var k in creditInfos[i])
                {
                    if(creditInfoAddDataSet.columnAt(k) > -1)
                    {
                        creditInfoAddDataSet.setValue(k,object[k]);
                    }
                }
                if(object.select)
                {
                    creditInfoAdd.setRowSelected(creditInfoAddDataSet.getRow(),true);
                }
            }
            creditInfoAdd.gotoNextRow();
            dlg.addListener("onOk",new Xjs.FuncCall(this.onOkAction,this,[table]));
            dlg.showModal();
        } else if(resultObject.tableRowValues != null)
        {
            snsoft.ft.utils.FTUtils.replaceValueAndAutoRefresh(table.getRootTable(),resultObject.tableRowValues);
            var fundCreditInfo = Xjs.util.TableUtils.getTable_$Panel_$String(this.mainTable,"ft_fund_credit");
            if(fundCreditInfo)
            {
                fundCreditInfo.refreshTable(true);
            }
            var financialInfo = Xjs.util.TableUtils.getTable_$Panel_$String(this.mainTable,"ft_fund_financial");
            if(financialInfo)
            {
                financialInfo.refreshTable(true);
            }
        }
    },
    /*snsoft.ft.comm.lis.FundCreditMainInfoJSListener.onOkAction*/
    onOkAction:function(dlgPane,btn,command,table)
    {
        var gridTable = dlgPane.getItemByName("ft_fund_creditinfo"),
            dataSet = gridTable.dataSet,
            columns = gridTable.getColumns(),
            selectRows = gridTable.getSelectedRowNumbers();
        if(selectRows.length == 0)
        {
            throw Xjs.ResBundle.getResVal("FT.00000048");
        }
        var rowDatas = [];
        for(var i=0;i < selectRows.length;i++)
        {
            var rowData = dataSet.getRowData(selectRows[i]),
                map = {};
            for(var j=0;j < columns.length;j++)
            {
                map[columns[j].name] = rowData[j];
            }
            rowDatas.push(map);
        }
        var keys = new Set();
        for(var i=0;i < rowDatas.length;i++)
        {
            var object = rowDatas[i],
                creditType = object.credittype,
                isrquotatype = object.isrquotatype,
                key = creditType + isrquotatype;
            if(keys.has(key))
            {
                throw Xjs.ResBundle.getResVal("FT-FUND.00000010");
            }
            keys.add(key);
        }
        var crediticode = table.getDataSet().getValue("crediticode"),
            innercode = table.getRootTable().dataSet.getValue(table.getRootTable().dataSet.getCuInnerfld()),
            sheetcode = table.getRootTable().dataSet.getValue("sheetcode"),
            fundCreditUIService = Xjs.RInvoke.newBean("FT-FUND.FundCreditUIService"),
            tableRowValues = fundCreditUIService.updateCreditInfo(sheetcode,innercode,crediticode,rowDatas);
        snsoft.ft.utils.FTUtils.replaceValueAndAutoRefresh(table.getRootTable(),tableRowValues);
    },
    /*snsoft.ft.comm.lis.FundCreditMainInfoJSListener.dataSetRowNavigated*/
    dataSetRowNavigated:function(dataSet,e)
    {
        snsoft.ft.comm.lis.FundCreditMainInfoJSListener.superclass.dataSetRowNavigated.call(this,dataSet,e);
        var cfcode = dataSet.getValue("cfcode"),
            credittype = this.mainTable.getColumn("credittype"),
            selectOptions = credittype.selectOptions;
        if(selectOptions instanceof Xjs.dx.CodeData)
        {
            var codeData = selectOptions,
                defaultFilter = "{n:'status',v:'70',op:'='}",
                fix,
                masterTable = this.mainTable.masterTable,
                name = masterTable.name;
            if("S0009" == cfcode && "ft_pay_app" == name)
            {
                var creditmodeFilter = "{n:'creditmode',v:'%NONC%',op:'like'}";
                fix = "{and:[{n:'status',v:'70',op:'='}" + "," + creditmodeFilter + "]}";
            } else 
            {
                fix = defaultFilter;
            }
            codeData.setLoadParameter("JSONFILTER_FIX",fix);
        }
    }
});
/*snsoft/ft/comm/lis/FundSrcJSListener.java*/
snsoft.ft.comm.lis.FundSrcJSListener=function(params){
    snsoft.ft.comm.lis.FundSrcJSListener.superclass.constructor.call(this,params);
    this.fundSrcTableName = String.obj2str(params.fundSrcTableName,"ft_pay_appsrc");
    this.filterTableName = String.obj2str(params.filterTableName,"");
    this.filterBcodeFieldName = String.obj2str(params.filterBcodeFieldName,"bcode");
    this.fundsrctypeFilterFlag = String.obj2str(params.fundsrctypeFilterFlag,"N");
};
Xjs.extend(snsoft.ft.comm.lis.FundSrcJSListener,snsoft.plat.bas.busi.SystemFunctionListener,{
  _js$className_:"snsoft.ft.comm.lis.FundSrcJSListener",
    /*snsoft.ft.comm.lis.FundSrcJSListener.initComponent*/
    initComponent:function(table,values)
    {
        snsoft.ft.comm.lis.FundSrcJSListener.superclass.initComponent.call(this,table,values);
        this.filterTable = this.getTable(table,this.filterTableName);
        if(this.filterTable)
        {
            this.filterDataset = this.filterTable.getDataSet();
        }
        this.fundSrcTable = this.getTable(table,this.fundSrcTableName);
        if(this.fundSrcTable)
        {
            this.fundSrcDataset = this.fundSrcTable.getDataSet();
        }
        this.mainTable = table.getRootTable();
        if(this.mainTable)
        {
            this.mainDataset = this.mainTable.getDataSet();
        }
    },
    /*snsoft.ft.comm.lis.FundSrcJSListener.dataLoaded*/
    dataLoaded:function(dataSet,e)
    {
        snsoft.ft.comm.lis.FundSrcJSListener.superclass.dataLoaded.call(this,dataSet,e);
        this.setBcodeDefaultValue();
        if("Y" == this.fundsrctypeFilterFlag)
        {
            var fundsrctype = this.fundSrcTable.getColumn("fundsrctype"),
                data = fundsrctype.selectOptions;
            data.loadParameter.JSONFILTER = "{n:'code', op:'in', v:['10','20','30','40']}";
        }
    },
    /*snsoft.ft.comm.lis.FundSrcJSListener.itemAidInputing*/
    itemAidInputing:function(table,e)
    {
        snsoft.ft.comm.lis.FundSrcJSListener.superclass.itemAidInputing.call(this,table,e);
        if(this.fundSrcTableName == table.name && "bcode" == e.item.name)
        {
            var bcodes = "";
            if(this.filterTable)
            {
                this.filterDataset = this.filterTable.getDataSet();
                for(var r=0;r < this.filterDataset.getRowCount();r++)
                {
                    var bcode = this.filterDataset.getValue("bcode",r);
                    bcodes += bcode + "','";
                }
                if(bcodes)
                {
                    bcodes = bcodes.substring(0,bcodes.length - 3);
                }
                var codedata = this.fundSrcTable.getColumn("bcode").selectOptions;
                codedata.loadParameter.JSONFILTER = "{n:'bcode',v:['" + bcodes + "'],op:'in'}";
            }
        }
    },
    /*snsoft.ft.comm.lis.FundSrcJSListener.setBcodeDefaultValue*/
    setBcodeDefaultValue:function()
    {
        var bcode = "";
        if(this.mainDataset)
        {
            bcode = this.mainDataset.getValue("bcode");
        }
        this.fundSrcDataset.getColumn("bcode").defaultValue = bcode;
    }
});
/*snsoft/ft/comm/match/CreditmodeMatchValue.java*/
Xjs.namespace("snsoft.ft.comm.match");
snsoft.ft.comm.match.CreditmodeMatchValue=function(config){
    snsoft.ft.comm.match.CreditmodeMatchValue.superclass.constructor.call(this,config);
};
Xjs.extend(snsoft.ft.comm.match.CreditmodeMatchValue,Xjs.table.sample.TableOptsCtrlListener$MatchValue,{
  _js$className_:"snsoft.ft.comm.match.CreditmodeMatchValue",
    /*snsoft.ft.comm.match.CreditmodeMatchValue.match*/
    match:function(value,row)
    {
        var cfcode = this.table.dataSet.getValue("cfcode",row);
        if(!cfcode)
        {
            return false;
        }
        var srccredittype = this.table.dataSet.getValue("srccredittype",row);
        if("S0024" == cfcode)
        {
            return true;
        }
        if(("S0009" == cfcode || "S0010" == cfcode) && "50" == srccredittype)
        {
            var cfinnercode = this.table.dataSet.getValue("cfinnercode",row),
                cfsheetcode = this.table.dataSet.getValue("cfsheetcode",row);
            if(!cfinnercode || !cfsheetcode)
            {
                return false;
            }
            var uiService = Xjs.RInvoke.newBean("FT-FUND.FundCreditUIService");
            return uiService.isLongOrd(cfsheetcode,cfinnercode);
        }
        return false;
    },
    /*snsoft.ft.comm.match.CreditmodeMatchValue.matchColumn*/
    matchColumn:function(col)
    {
        if(!(this.table.dataSet.name == "ft_fund_credit"))
        {
            return false;
        }
        return snsoft.ft.comm.match.CreditmodeMatchValue.superclass.matchColumn.call(this,col);
    }
});
/*snsoft/ft/comm/match/RpModeMatchValue.java*/
snsoft.ft.comm.match.RpModeMatchValue=function(config){
    snsoft.ft.comm.match.RpModeMatchValue.superclass.constructor.call(this,config);
};
Xjs.extend(snsoft.ft.comm.match.RpModeMatchValue,Xjs.table.sample.TableOptsCtrlListener$MatchValue,{
  _js$className_:"snsoft.ft.comm.match.RpModeMatchValue",
    /*snsoft.ft.comm.match.RpModeMatchValue.match*/
    match:function(value,row)
    {
        if(value)
        {
            var rpModes = window.EnvParameter._RPModes,
                colValues = value.split(",");
            for(var rpMode of rpModes)
            {
                for(var i=0;i < colValues.length;i++)
                {
                    if(rpMode.rpcode == colValues[i])
                    {
                        for(var j=0;j < this.values.length;j++)
                        {
                            if(this.values[j] == rpMode.rptype)
                            {
                                return this.negmatch ? !true : true;
                            }
                        }
                    }
                }
            }
        }
        return this.negmatch;
    }
});
/*snsoft/ft/comm/service/FundCreditUIService.java*/
Xjs.RInvoke.beansDef["FT-FUND.FundCreditUIService"]={queryCreditInfoByCreditMainInfo:{},updateCreditInfo:{},checkQueryCreditInfoBtn:{},queryCreditctrlran:{},hasCredittypeInfo:{},checkCreditMixData:{},ctrlCreditInfoVisible:{},isLongOrd:{}};
