Xjs.loadedXjs.push("snsoft/ft/fund/comm/comm-client");
Xjs.namespace("snsoft.ft.comm.lis");snsoft.ft.comm.lis.FundCreditInfoOperateJSListener$FundCreditInfoOperateRender=function(sortCmds,sortTitles,cmdClass){snsoft.ft.comm.lis.FundCreditInfoOperateJSListener$FundCreditInfoOperateRender.superclass.constructor.call(this,sortCmds,sortTitles,cmdClass);};Xjs.extend(snsoft.ft.comm.lis.FundCreditInfoOperateJSListener$FundCreditInfoOperateRender,snsoft.plat.sheet.comm.OperateJSListener$OperateRender,{cellRender:function(value,info){info.html=true;var d,rootTable=info.cell.table.getRootTable(),status=rootTable.getDataSet().getValue("status"),flag=snsoft.ft.utils.FTUtils.hasLimit(rootTable,rootTable.getDataSet().getRow());if(info.cellDOM&&(d=info.cellDOM.firstChild)&&Xjs.DOM.hasClass(d,Xjs.ui.TableCellBtnHtml.rootCls)){for(var i=0;i<this.cmds.length;i++){var cmd=this.cmds[i],id="operate_"+cmd,optDOM=Xjs.DOM.findById(id,d),disableFlag=false;if(optDOM&&(!flag||status>20))disableFlag=true;Xjs.DOM.addOrRemoveClass(optDOM,Xjs.ui.TableCellBtnHtml.disabledCls,disableFlag);}return d;}return snsoft.ft.comm.lis.FundCreditInfoOperateJSListener$FundCreditInfoOperateRender.superclass.cellRender.call(this,value,info);}});snsoft.ft.comm.lis.FundCreditInfoOperateJSListener=function(params){snsoft.ft.comm.lis.FundCreditInfoOperateJSListener.superclass.constructor.call(this,params);};Xjs.extend(snsoft.ft.comm.lis.FundCreditInfoOperateJSListener,snsoft.plat.bas.share.ShareDialogListener,{newOperateRender:function(){return new snsoft.ft.comm.lis.FundCreditInfoOperateJSListener$FundCreditInfoOperateRender(this.cmds,this.titles,this.cmdClass);}});