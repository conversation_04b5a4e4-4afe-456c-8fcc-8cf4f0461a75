Xjs.loadedXjs.push("snsoft/ft/fund/rdoc/lock");
/*snsoft/ft/rdoc/lock/invoker/RdocCancleLockInvoker.java*/
Xjs.namespace("snsoft.ft.rdoc.lock.invoker");
snsoft.ft.rdoc.lock.invoker.RdocCancleLockInvoker=function(parameter){
    snsoft.ft.rdoc.lock.invoker.RdocCancleLockInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.rdoc.lock.invoker.RdocCancleLockInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.rdoc.lock.invoker.RdocCancleLockInvoker",
    /*snsoft.ft.rdoc.lock.invoker.RdocCancleLockInvoker.beforeCheck*/
    beforeCheck:function(event)
    {
        var rows = this.table.getSelectedRowNumbers();
        if(!rows || rows.length == 0)
        {
            throw new Error(Xjs.ResBundle.getResVal("FT.00000048"));
        }
        for(var i=0;i < rows.length;i++)
        {
            var locktype = this.dataSet.getValue("locktype",rows[i]),
                rdoccancleicode = this.dataSet.getValue("rdoccancleicode",rows[i]);
            if(!("1"==locktype && !rdoccancleicode))
            {
                throw new Error(Xjs.ResBundle.getResVal("FT-RDOC.00000019"));
            }
        }
        return null;
    },
    /*snsoft.ft.rdoc.lock.invoker.RdocCancleLockInvoker.check*/
    check:function(event)
    {
        var rows = this.table.getSelectedRowNumbers();
        return this.newDialogWithFunc(event,(e)=>{
            var dialogPane = Xjs.ui.UIUtil.loadDialog("FT-RDOC.RdocCancelLockDialog");
            dialogPane.title = this.cfg.title;
            dialogPane.showModal();
            dialogPane.getItemByName("remark").setValue(Xjs.ResBundle.getResVal("FT-RDOC.10000002"));
            return dialogPane;
        },(e,d)=>{
            var param = {},
                gicodes = [];
            for(var i=0;i < rows.length;i++)
            {
                var gicode = {};
                gicode.rdoclockicode = this.dataSet.getValue("rdoclockicode",rows[i]);
                gicodes.push(gicode);
            }
            param.gicodes = gicodes;
            var mains = [],
                main = {};
            main.rdocicode = this.mainDataSet.getValue("rdocicode");
            main.sheetcode = this.mainDataSet.getValue("sheetcode");
            main.modifydate = this.mainDataSet.getValue("modifydate");
            mains.push(main);
            param.mains = mains;
            param.rdocicode = this.mainDataSet.getValue("rdocicode");
            param.remark = d.getItemByName("remark").getValue();
            event.checkData.param = param;
        });
    },
    /*snsoft.ft.rdoc.lock.invoker.RdocCancleLockInvoker.invoke*/
    invoke:function(event)
    {
        return event.checkData.param;
    },
    /*snsoft.ft.rdoc.lock.invoker.RdocCancleLockInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        this.table.refreshTable();
        var refreshRows = new Xjs.util.DataSetUtils$RefreshRows(this.mainDataSet,this.mainTable.getSelectedRowNumbers(),null,null);
        Xjs.util.DataSetUtils.refreshRow_prefix(refreshRows,null,true);
    }
});
/*snsoft/ft/rdoc/lock/invoker/RdocLockInvoker.java*/
snsoft.ft.rdoc.lock.invoker.RdocLockInvoker=function(parameter){
    snsoft.ft.rdoc.lock.invoker.RdocLockInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.rdoc.lock.invoker.RdocLockInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.rdoc.lock.invoker.RdocLockInvoker",
    /*snsoft.ft.rdoc.lock.invoker.RdocLockInvoker.beforeCheck*/
    beforeCheck:function(event)
    {
        var rows = this.table.getSelectedRowNumbers();
        if(!rows || rows.length == 0)
        {
            throw new Error(Xjs.ResBundle.getResVal("FT.00000048"));
        }
        if(rows.length > 1)
        {
            throw new Error(Xjs.ResBundle.getResVal("FT.00000087"));
        }
        var fcying = this.dataSet.getValue("fcying",rows[0]);
        if(fcying <= 0)
        {
            throw new Error(Xjs.ResBundle.getResVal("FT-RDOC.00000011"));
        }
        return null;
    },
    /*snsoft.ft.rdoc.lock.invoker.RdocLockInvoker.check*/
    check:function(event)
    {
        var rows = this.table.getSelectedRowNumbers();
        return this.newDialogWithFunc(event,(e)=>{
            var dialogPane = Xjs.ui.UIUtil.loadDialog("FT-RDOC.RdocLockDialog");
            dialogPane.title = this.cfg.title;
            dialogPane.showModal();
            dialogPane.getItemByName("rdocicode").setValue(this.dataSet.getValue("rdocicode",rows[0]));
            var fcying = this.dataSet.getValue("fcying",rows[0]);
            dialogPane.getItemByName("fcying").setValue(fcying);
            dialogPane.getItemByName("lockduedate").setValue(Xjs.util.DateUtils.nowDay());
            var iscansplit = this.dataSet.getValue("iscansplit",rows[0]);
            dialogPane.getItemByName("iscansplit").setValue(iscansplit);
            if("N"==iscansplit)
            {
                dialogPane.getItemByName("lockfcy").setValue(fcying);
                dialogPane.getItemByName("lockfcy").setReadonly(true);
            }
            return dialogPane;
        },(e,d)=>{
            var dlgTable = d.getItemByName("ft_rdoc_rdoc");
            dlgTable.postPending();
            if(!dlgTable.dataSet.isChanged(false))
            {
                dlgTable.dataSet.setRowChanged(true);
            }
            dlgTable.checkNonBlankForSubmit();
            var param = {},
                mains = [],
                main = {};
            main.rdocicode = this.dataSet.getValue("rdocicode",rows[0]);
            main.sheetcode = this.dataSet.getValue("sheetcode",rows[0]);
            main.modifydate = this.dataSet.getValue("modifydate",rows[0]);
            mains.push(main);
            param.mains = mains;
            param.rdocicode = dlgTable.getDataSet().getValue("rdocicode");
            param.lockfcy = dlgTable.getDataSet().getValue("lockfcy");
            param.lockpurpose = dlgTable.getDataSet().getValue("lockpurpose");
            param.lockduedate = dlgTable.getDataSet().getValue("lockduedate");
            param.remark = dlgTable.getDataSet().getValue("remark");
            event.checkData.param = param;
            this.remoteInvoke("beforeInvoke",param);
        });
    },
    /*snsoft.ft.rdoc.lock.invoker.RdocLockInvoker.invoke*/
    invoke:function(event)
    {
        return event.checkData.param;
    },
    /*snsoft.ft.rdoc.lock.invoker.RdocLockInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        var refreshRows = new Xjs.util.DataSetUtils$RefreshRows(this.dataSet,this.table.getSelectedRowNumbers(),null,null);
        Xjs.util.DataSetUtils.refreshRow_prefix(refreshRows,null,true);
    }
});
