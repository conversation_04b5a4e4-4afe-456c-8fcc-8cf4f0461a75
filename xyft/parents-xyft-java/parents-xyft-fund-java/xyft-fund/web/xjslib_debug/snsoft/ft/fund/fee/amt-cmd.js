Xjs.loadedXjs.push("snsoft/ft/fund/fee/amt-cmd");
/*snsoft/ft/fee/amt/invoker/CreateAmortgInvoker.java*/
Xjs.namespace("snsoft.ft.fee.amt.invoker");
snsoft.ft.fee.amt.invoker.CreateAmortgInvoker=function(parameter){
    snsoft.ft.fee.amt.invoker.CreateAmortgInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.fee.amt.invoker.CreateAmortgInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.fee.amt.invoker.CreateAmortgInvoker",
    /*snsoft.ft.fee.amt.invoker.CreateAmortgInvoker.invoke*/
    invoke:function(event)
    {
        var param = {},
            feeamticode = this.mainDataSet.get("feeamticode"),
            startdate = this.mainDataSet.get("sdate"),
            msg = null;
        if(startdate == null)
        {
            msg = msg == null ? "起始日期" : msg + "," + "起始日期";
        }
        var amttype = this.mainDataSet.get("amttype");
        if(amttype == null)
        {
            msg = msg == null ? "摊销方式" : msg + "," + "摊销方式";
        }
        if("10" == amttype)
        {
            var sharemonth = this.mainDataSet.get("sharemonth");
            if(sharemonth == null)
            {
                msg = msg == null ? "分摊月份" : msg + "," + "分摊月份";
            }
            param.sharemonth = sharemonth;
        }
        if("20" == amttype)
        {
            var enddate = this.mainDataSet.get("enddate");
            if(enddate == null)
            {
                msg = msg == null ? "结束日期" : msg + "," + "结束日期";
            }
            param.duedate = enddate;
        }
        var fcy = this.mainDataSet.get("fcy");
        if(fcy == null)
        {
            msg = msg == null ? "摊销金额" : msg + "," + "摊销金额";
        }
        if(msg != null)
        {
            throw new Error(Xjs.ResBundle.getResVal("FT-FEE.00000008",msg));
        }
        param.startdate = startdate;
        param.feeamticode = feeamticode;
        param.amttype = amttype;
        param.fcy = fcy;
        return param;
    },
    /*snsoft.ft.fee.amt.invoker.CreateAmortgInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        var array = event.invokeRtnVal.tableRowValues;
        snsoft.ft.utils.FTUtils.replaceValueAndAutoRefresh(event.table,array);
    }
});
