# 内部借款利息计提单
ft_loan_innirt=内部借款利息计提单
ft_loan_innirt.innirtcode=${innirtcode}
ft_loan_innirt.innirticode=内部借款利息计提单内码
ft_loan_innirt.corpbcode=${loanoutcorpbcode}
ft_loan_innirt.ccode=${loanincorpbcode}
ft_loan_innirt.nstype=${scmcode}
ft_loan_innirt.predatefm=${predatefm}
ft_loan_innirt.vprepare=${vprepare}
ft_loan_innirt.year=${year}
ft_loan_innirt.month=${month}
ft_loan_innirt.predate=${predate}
ft_loan_innirt.modifier=${modifier}
ft_loan_innirt.modifydate=${modifydate}
ft_loan_innirt.wfcode=${wfcode}
ft_loan_innirt.wfuid=${wfuid}
ft_loan_innirt.sheetcode=${outercode}
ft_loan_innirt.fcode=${fcode}
ft_loan_innirt.sfcode=${sfcode}
ft_loan_innirt.fserate=${fserate}
ft_loan_innirt.suerate=${suerate}
ft_loan_innirt.scerate=${scerate}
ft_loan_innirt.tcaptime=${tcaptime}
ft_loan_innirt.ispushinv=${ispushinv}
ft_loan_innirt.isapp=是否已申开
ft_loan_innirt.ownfundirtfcy=自有资金利息|${fcy}
ft_loan_innirt.ownfundirtrmb=自有资金利息|${scy}
ft_loan_innirt.unifiedloanirtfcy=统借统贷利息|${fcy}
ft_loan_innirt.unifiedloanirtrmb=统借统贷利息|${scy}
ft_loan_innirt.operator=${operator}
ft_loan_innirt.operatetime=${operatetime}
##隱藏
ft_loan_innirt.fratedate=系统汇率日期
ft_loan_innirtg=内部借款利息计提单明细
ft_loan_innirtg.loanfcy=${loanfcy}
ft_loan_innirtg.loanordcode=${loanordcode}
ft_loan_innirtg.balfcy=${loanbalfcy}
ft_loan_innirtg.fcy=${loansfcy}
ft_loan_innirtg.loadstartdate=${loadstartdate}
ft_loan_innirtg.irtstartdate=${irtstartdate}
ft_loan_innirtg.irtduedate=${irtduedate}
ft_loan_innirtg.irtrate=${irtrate}
ft_loan_innirtg.fundsrctype=${fstype}
ft_loan_innirtg.irtfcy=${irtfcy}
ft_loan_innirtg.irtbasfcy=${irtbasfcy}
ft_loan_innirtg.finaordcode=${finaordcode}
ft_loan_innirtg.irtcalcdays=${irtcalcdays}
ft_loan_innirtg.vsntype=${vsntype}
##隱藏
ft_loan_innirtg.irtscy=本位币利息
ft_loan_innirtg.irtzcny=折人民币利息
ft_loan_innirtg.irtzusd=折美元利息

###接入接口
ft_acs.innirt.xxx=[CODE:xxx]


# 赎单融资还款申请单
ft_loan_acdfar=赎单融资还款申请单
ft_loan_acdfar.acdfarcode=赎单融资还款申请单号
ft_loan_acdfar.acdfacode=赎单融资申请单号
ft_loan_acdfar.acdcode=承兑单号
ft_loan_acdfar.bankcode=融资银行
ft_loan_acdfar.bcode=${bcode}
ft_loan_acdfar.wcode=${wcode}
ft_loan_acdfar.corpbcode=${corpbcode}
ft_loan_acdfar.vprepare=${vprepare}
ft_loan_acdfar.predatefm=${predatefm}
ft_loan_acdfar.predateto=${dateto}
ft_loan_acdfar.sheetcode=单据类型
ft_loan_acdfar.acdfaricode=融资还款申请单内码
ft_loan_acdfar.fcode=融资币种
ft_loan_acdfar.remark=${remark}
ft_loan_acdfar.predate=${predate}
ft_loan_acdfar.cuicode=${cuicode}
ft_loan_acdfar.modifydate=${modifydate}
ft_loan_acdfar.modifier=${modifier}
ft_loan_acdfar.finfcy=融资金额
ft_loan_acdfar.fcy=融资还款金额
ft_loan_acdfar.repdate=申请还款日期
ft_loan_acdfar.isreferable=未生效时可引用
ft_loan_acdfar.remark=${remark}
ft_loan_acdfar.sfcode=${sfcode}
ft_loan_acdfar.deptbcode=${bubcode}
ft_loan_acdfar.fundsrctype=资金来源
ft_loan_acdfar.isinit=是否初始化
# 融资还款信息
ft_loan_acdfarg.bcode=${bcode}
ft_loan_acdfarg.srcfcy=${planexecutefcy}
ft_loan_acdfarg.fcy=融资还款金额
# 赎单融资还款展期工作台
ft_loan_acdfag_cb_view.acdfacode=赎单融资申请单号
ft_loan_acdfag_cb_view.acdcode=承兑单号
ft_loan_acdfag_cb_view.bankcode=融资银行
ft_loan_acdfag_cb_view.fcode=融资币种
ft_loan_acdfag_cb_view.startdate=融资起始日
ft_loan_acdfag_cb_view.startdatefm=融资起始日从
ft_loan_acdfag_cb_view.complstatus=融资办理状态
ft_loan_acdfag_cb_view.predatefm=${predatefm}
ft_loan_acdfag_cb_view.predateto=${predateto}
ft_loan_acdfag_cb_view.fcyingflag=只查待执行金额>0
ft_loan_acdfag_cb_view.fcy=计划执行金额
ft_loan_acdfag_cb_view.fcyed=已执行金额
ft_loan_acdfag_cb_view.fcying=待执行金额
ft_loan_acdfag_cb_view.duedate=融资到期日
ft_loan_acdfag_cb_view.yrate=年利率
ft_loan_acdfag_cb_view.planrate=安排费率
ft_loan_acdfag_cb_view.remark=${remark}
ft_loan_acdfag_cb_view.wcode=${wcode}
ft_loan_acdfag_cb_view.bcode=${bcode}
ft_loan_acdfag_cb_view.corpbcode=${corpbcode}
ft_loan_acdfag_cb_view.vprepare=${vprepare}
ft_loan_acdfag_cb_view.predate=${predate}
ft_loan_acdfag_cb_view.isrepap=提前制作还款申请
ft_loan_acdfag_cb_view.acdfaicode=赎单融资申请单号主表内码
ft_loan_acdfag_cb_view.acdfagicode=部门融资明细内码
# 资金来源
ft_fund_fsrc.acdfaricode=内码
ft_fund_fsrc.acdfarsrcicode=付款申请资金来源内码
ft_fund_fsrc.fundsrctype=资金来源
ft_fund_fsrc.bcode=业务员部门
ft_fund_fsrc.callbcode=现汇调用部门
ft_fund_fsrc.fcy=金额
ft_fund_fsrc.srcicode=来源单据内码
ft_fund_fsrc.srccode=来源单据号
ft_fund_fsrc.srcsheetcode=来源单据类型
ft_fund_fsrc.srcismanual=来源单据是否手工创建
ft_fund_fsrc.fsrcicode=资金来源内码


# 赎单融资展期申请单
ft_loan_acdfae=赎单融资展期申请单
ft_loan_acdfae.acdfaecode=融资展期申请单号
ft_loan_acdfae.acdfaecode1=赎单融资展期申请单号
ft_loan_acdfae.acdfaeicode=融资展期申请单内码
ft_loan_acdfae.acdfacode=赎单融资申请单号
ft_loan_acdfae.acdfaicode=赎单融资申请单内码
ft_loan_acdfae.acdcode=承兑单号
ft_loan_acdfae.acdicode=承兑单内码
ft_loan_acdfae.bankcode=融资银行
ft_loan_acdfae.fcode=融资币种
ft_loan_acdfae.fcy=融资金额
ft_loan_acdfae.duedate=融资到期日
ft_loan_acdfae.exduedate=展期到期日
ft_loan_acdfae.exdays=展期天数
ft_loan_acdfae.exdate=展期到期日
ft_loan_acdfae.exfcy=展期金额
ft_loan_acdfae.performdate=审核时间
ft_loan_acdfae.sfcode=本位币币种
ft_loan_acdfae.isinit=是否初始化


# 融资展期信息表
ft_loan_acdfaeg=融资展期信息表
ft_loan_acdfaeg.acdfaegicode=融资展期信息内码
ft_loan_acdfaeg.acdfaeicode=融资展期信息申请内码
ft_loan_acdfaeg.fcy=计划执行金额
ft_loan_acdfaeg.exfcy=展期金额



#赎单融资申请单
ft_loan_acdfa=赎单融资申请单
ft_loan_acdfa.acdfacode=赎单融资申请单号
ft_loan_acdfa.acdfaicode=赎单融资申请单内码
ft_loan_acdfa.acdcode=承兑单号
ft_loan_acdfa.acdicode=承兑赎单内码
ft_loan_acdfa.lcaicode=信用证开证申请内码
ft_loan_acdfa.paymode=${paymode}
ft_loan_acdfa.corpbcode=${corpbcode}
ft_loan_acdfa.vprepare=${vprepare}
ft_loan_acdfa.predatefm=${predatefm}
ft_loan_acdfa.predateto=${dateto}
ft_loan_acdfa.isrepap=提前制作还款申请
ft_loan_acdfa.acdfcode=赎单币种
ft_loan_acdfa.fcode=融资币种
ft_loan_acdfa.wfcode=${wfcode}
ft_loan_acdfa.wfuid=${wfuid}
ft_loan_acdfa.sheetcode=${outercode}
ft_loan_acdfa.bankcode=融资银行
ft_loan_acdfa.lccode=${lccode}
ft_loan_acdfa.acdfcy=融资金额(赎单币种)
ft_loan_acdfa.remark=${remark}
ft_loan_acdfa.pfdate=付汇日期
ft_loan_acdfa.complstatus=融资办理状态
ft_loan_acdfa.sfcode=${sfcode}
ft_loan_acdfa.purccode=${purcode}
ft_loan_acdfa.isdiff=异币种融资
ft_loan_acdfa.adate=${adate}
ft_loan_acdfa.findays=融资天数
ft_loan_acdfa.duedate=融资到期日
ft_loan_acdfa.realduedate=实际融资到期日
ft_loan_acdfa.yrate=年利率
ft_loan_acdfa.planrate=安排费率
ft_loan_acdfa.acdsrcfcy=赎单金额
ft_loan_acdfa.isprodel=是否禁止删除
ft_loan_acdfa.realstartdate=实际融资起始日
ft_loan_acdfa.srcgicode=来源单据子表内码
ft_loan_acdfa.isinit=是否初始化

#融资申请明细表
ft_loan_acdfasg.purordcode=${purordcode}
ft_loan_acdfasg.prjcode=采购业务编号
ft_loan_acdfasg.purshipcoder=到货单号
ft_loan_acdfasg.acdsrcfcy=赎单金额
ft_loan_acdfasg.acdfcy=融资金额(赎单币种)
ft_loan_acdfasg.fcy=融资金额
ft_loan_acdfasg.startdate=融资起始日
ft_loan_acdfasg.fcode=融资币种
ft_loan_acdfasg.fserate=汇率
ft_loan_acdfasg.sfcode=${sfcode}
ft_loan_acdfasg.scy=折本位币金额
ft_loan_acdfasg.scerate=${scerate}
ft_loan_acdfasg.suerate=${suerate}
ft_loan_acdfasg.zcny=折人民币金额
ft_loan_acdfasg.zusd=折美元金额
ft_loan_acdfasg.acdfaicode=赎单融资申请单内码
ft_loan_acdfasg.acdfasgicode=赎单融资申请明细内码
ft_loan_acdfasg.acdfagicode=部门融资金额明细内码
ft_loan_acdfasg.acdgicode=承兑明细内码
ft_loan_acdfasg.purshipicoder=采购到货单内码
ft_loan_acdfasg.purordicode=采购合同内码
ft_loan_acdfasg.prjicode=采购业务内码

#部门融资金额明细表
ft_loan_acdfag.acdfcy=融资金额(赎单币种)
ft_loan_acdfag.fcy=融资金额
ft_loan_acdfag.fundsrctype=资金来源
ft_loan_acdfag.acdfaicode=赎单融资申请单内码
ft_loan_acdfag.acdfagicode=部门融资金额明细内码

#放款信息表
ft_loan_acdfali.acdfacode=赎单融资申请单号
ft_loan_acdfali.acdcode=承兑单号
ft_loan_acdfali.bankcode=融资银行
ft_loan_acdfali.bankacccode=融资银行账号
ft_loan_acdfali.isdiff=异币种融资
ft_loan_acdfali.acdfcode=赎单币种
ft_loan_acdfali.acdfcy=融资金额(赎单币种)
ft_loan_acdfali.fcode=融资币种
ft_loan_acdfali.fcy=融资金额
ft_loan_acdfali.fserate=汇率
ft_loan_acdfali.sfcode=${sfcode}
ft_loan_acdfali.scy=折本位币金额
ft_loan_acdfali.scerate=${scerate}
ft_loan_acdfali.suerate=${suerate}
ft_loan_acdfali.zcny=折人民币金额
ft_loan_acdfali.zusd=折美元金额
ft_loan_acdfali.startdate=融资起始日
ft_loan_acdfali.duedate=融资到期日
ft_loan_acdfali.yrate=年利率
ft_loan_acdfali.planrate=安排费率
ft_loan_acdfali.issimulate=是否模拟
ft_loan_acdfali.headbcode=本部部门
ft_loan_acdfali.bankno=${bankno}
ft_loan_acdfali.tcaptime=${tcaptime}
ft_loan_acdfali.corpbcode=${corpbcode}
ft_loan_acdfali.redflag#5=被红冲
ft_loan_acdfali.redflag#2=红冲
ft_loan_acdfali.acdfaicode=赎单融资申请单内码
ft_loan_acdfali.acdfaliicode=赎单融资放款信息内码
ft_loan_acdfali.operator=修改人
ft_loan_acdfali.operatetime=修改时间

#放款分摊明细表
ft_loan_acdfalig.acdfaicode=赎单融资申请单内码
ft_loan_acdfalig.acdcode=承兑单号
ft_loan_acdfalig.lccode=信用证号
ft_loan_acdfalig.purshipcoder=到货单单号
ft_loan_acdfalig.purordcode=采购合同号
ft_loan_acdfalig.prjcode=采购业务编号
ft_loan_acdfalig.fcy=融资金额
ft_loan_acdfalig.findate=融资日期
ft_loan_acdfalig.fcode=融资币种
ft_loan_acdfalig.bankcode=融资银行
ft_loan_acdfalig.fserate=汇率
ft_loan_acdfalig.sfcode=${sfcode}
ft_loan_acdfalig.scy=折本位币金额
ft_loan_acdfalig.scerate=${scerate}
ft_loan_acdfalig.suerate=${suerate}
ft_loan_acdfalig.zcny=折人民币金额
ft_loan_acdfalig.zusd=折美元金额
ft_lrp_lrp.lilrpdate=融资日期
ft_lrp_lrp.lifcode=融资币种
ft_lrp_lrp.loanfserate=汇率
ft_lrp_lrp.rifcy=还款金额
ft_lrp_lrp.rilrpdate=还款日期
ft_lrp_lrp.rifcode=还款币种
ft_lrp_lrp.lcaicode=信用证开证申请内码
ft_loan_acdfalig=放款分摊明细


#还款信息表
ft_loan_acdfari.srcsys=${srcsys}
ft_loan_acdfari.srcsyscode=${srcsyscode}
ft_loan_acdfari.acdfacode=赎单融资申请单号
ft_loan_acdfari.acdfacode=赎单融资申请单号
ft_loan_acdfari.acdcode=承兑单号
ft_loan_acdfari.repdate=还款日期
ft_loan_acdfari.bankacccode=还款银行账号
ft_loan_acdfari.bankcode=还款银行
ft_loan_acdfari.fcode=还款币种
ft_loan_acdfari.fcy=还款金额
ft_loan_acdfari.fserate=汇率
ft_loan_acdfari.sfcode=${sfcode}
ft_loan_acdfari.scy=折本位币金额
ft_loan_acdfari.scerate=${scerate}
ft_loan_acdfari.suerate=${suerate}
ft_loan_acdfari.zcny=折人民币金额
ft_loan_acdfari.zusd=折美元金额
ft_loan_acdfari.issimulate=是否模拟
ft_loan_acdfari.headbcode=本部部门
ft_loan_acdfari.bankno=${bankno}
ft_loan_acdfari.tcaptime=${tcaptime}
ft_loan_acdfari.acdfarcode=赎单融资还款申请单号
ft_loan_acdfari.acdfarcode=赎单融资还款申请单号
ft_loan_acdfari.acdfaricode=赎单融资还款申请单内码
ft_loan_acdfari.acdfaicode=赎单融资申请单内码
ft_loan_acdfari.acdfariicode=赎单融资还款信息内码
ft_loan_acdfari.operator=修改人
ft_loan_acdfari.operatetime=修改时间

#还款分摊明细表
ft_loan_acdfarig.fserate=汇率
ft_loan_acdfarig.sfcode=${sfcode}
ft_loan_acdfarig.scy=折本位币金额
ft_loan_acdfarig.scerate=${scerate}
ft_loan_acdfarig.suerate=${suerate}
ft_loan_acdfarig.zcny=折人民币金额
ft_loan_acdfarig.zusd=折美元金额
ft_loan_acdfarig.acdcode=承兑单号
ft_loan_acdfarig.lccode=信用证号
ft_loan_acdfarig.purshipcoder=到货单单号
ft_loan_acdfarig.purordcode=采购合同号
ft_loan_acdfarig.prjcode=采购业务编号
ft_loan_acdfarig.fcy=还款金额
ft_loan_acdfarig.repdate=还款日期
ft_loan_acdfarig.fcode=还款币种
ft_loan_acdfarig=还款分摊明细

#赎单融资处理工作台
FT.AccOrdFinaHandleWorkBench.bankacccode=银行账号
FT.AccOrdFinaHandleWorkBench.bankcode=银行
FT.AccOrdFinaHandleWorkBench.fcode=币种
FT.AccOrdFinaHandleWorkBench.vidflag=制证选项
FT.AccOrdFinaHandleWorkBench.fundsrctype=融资类型

#赎单融资处理工作台-放款信息
ft_loan_acdfali_view.acdfacode=赎单融资申请单号
ft_loan_acdfali_view.acdcode=承兑单号
ft_loan_acdfali_view.bankcode=融资银行
ft_loan_acdfali_view.bankacccode=融资银行账号
ft_loan_acdfali_view.isdiff=异币种融资
ft_loan_acdfali_view.acdfcode=赎单币种
ft_loan_acdfali_view.acdfcy=融资金额(赎单币种)
ft_loan_acdfali_view.fcode=融资币种
ft_loan_acdfali_view.fcy=融资金额
ft_loan_acdfali_view.fserate=汇率
ft_loan_acdfali_view.sfcode=${sfcode}
ft_loan_acdfali_view.scy=折本位币金额
ft_loan_acdfali_view.scerate=${scerate}
ft_loan_acdfali_view.suerate=${suerate}
ft_loan_acdfali_view.zcny=折人民币金额
ft_loan_acdfali_view.zusd=折美元金额
ft_loan_acdfali_view.startdate=融资起始日
ft_loan_acdfali_view.duedate=融资到期日
ft_loan_acdfali_view.yrate=年利率
ft_loan_acdfali_view.planrate=安排费率
ft_loan_acdfali_view.issimulate=是否模拟
ft_loan_acdfali_view.headbcode=本部部门
ft_loan_acdfali_view.bankno=${bankno}
ft_loan_acdfali_view.tcaptime=${tcaptime}
ft_loan_acdfali_view.corpbcode=${corpbcode}
ft_loan_acdfali_view.redflag#5=被红冲
ft_loan_acdfali_view.redflag#2=红冲
ft_loan_acdfali_view.acdfaliicode=赎单融资放款信息内码
ft_loan_acdfali_view.acdfaicode=赎单融资申请单内码
ft_loan_acdfali_view.batchvid=凭证大号
ft_loan_acdfali_view.vmarkicode=凭证标记表内码
ft_loan_acdfali_view.cuicode=${cuicode}
ft_loan_acdfali_view.accounttype=账户类型
ft_loan_acdfali_view.isvirtual=是否虚拟账户
ft_loan_acdfali_view.modifier=修改人
ft_loan_acdfali_view.modifydate=修改时间

#赎单融资处理工作台-还款信息
ft_loan_acdfari_view.srcsys=${srcsys}
ft_loan_acdfari_view.srcsyscode=${srcsyscode}
ft_loan_acdfari_view.acdfacode=赎单融资申请单号
ft_loan_acdfari_view.acdfacode=赎单融资申请单号
ft_loan_acdfari_view.acdcode=承兑单号
ft_loan_acdfari_view.repdate=还款日期
ft_loan_acdfari_view.bankacccode=还款银行账号
ft_loan_acdfari_view.bankcode=还款银行
ft_loan_acdfari_view.fcode=还款币种
ft_loan_acdfari_view.fcy=还款金额
ft_loan_acdfari_view.fserate=汇率
ft_loan_acdfari_view.sfcode=${sfcode}
ft_loan_acdfari_view.scy=折本位币金额
ft_loan_acdfari_view.scerate=${scerate}
ft_loan_acdfari_view.suerate=${suerate}
ft_loan_acdfari_view.zcny=折人民币金额
ft_loan_acdfari_view.zusd=折美元金额
ft_loan_acdfari_view.issimulate=是否模拟
ft_loan_acdfari_view.headbcode=本部部门
ft_loan_acdfari_view.bankno=${bankno}
ft_loan_acdfari_view.tcaptime=${tcaptime}
ft_loan_acdfari_view.acdfarcode=赎单融资还款申请单号
ft_loan_acdfari_view.acdfarcode=赎单融资还款申请单号
ft_loan_acdfari_view.acdfaricode=赎单融资还款申请单内码
ft_loan_acdfari_view.acdfariicode=赎单融资还款信息内码
ft_loan_acdfari_view.acdfaicode=赎单融资申请单内码
ft_loan_acdfari_view.batchvid=凭证大号
ft_loan_acdfari_view.vmarkicode=凭证标记表内码
ft_loan_acdfari_view.cuicode=${cuicode}
ft_loan_acdfari_view.modifier=修改人
ft_loan_acdfari_view.modifydate=修改时间
#展期信息表
ft_loan_acdfaei.startdate=展期起始日
ft_loan_acdfaei.duedate=展期到期日
ft_loan_acdfaei.fcy=展期金额
ft_loan_acdfaei.planrate=安排费率
ft_loan_acdfaei.exrate=展期率
ft_loan_acdfaei.issimulate=是否模拟
ft_loan_acdfaei.acdfaecode=融资展期信息申请单号
ft_loan_acdfaei.acdfaeicode=融资展期信息申请内码
ft_loan_acdfaei.headbcode=本部部门
ft_loan_acdfaei.acdfaicode=赎单融资申请单内码
ft_loan_acdfaei.acdfaeiicode=赎单融资展期信息内码
ft_loan_acdfaei.operator=修改人
ft_loan_acdfaei.operatetime=修改时间

#赎单融资拷贝新建工作台
ft_loan_acdfa_cb_view.acdcode=承兑单号
ft_loan_acdfa_cb_view.lccode=${lccode}
ft_loan_acdfa_cb_view.fundsrctype=资金来源
ft_loan_acdfa_cb_view.paymode=${paymode}
ft_loan_acdfa_cb_view.purccode=${purcode}
ft_loan_acdfa_cb_view.bankcode=付汇银行
ft_loan_acdfa_cb_view.acdfcode=币种
ft_loan_acdfa_cb_view.sfcode=本位币币种
ft_loan_acdfa_cb_view.romode=赎单方式
ft_loan_acdfa_cb_view.predatefm=${predatefm}
ft_loan_acdfa_cb_view.predateto=${dateto}
ft_loan_acdfa_cb_view.sfcode=${sfcode}
ft_loan_acdfa_cb_view.acdfcy=金额
ft_loan_acdfa_cb_view.pfdate=付汇日期
ft_loan_acdfa_cb_view.corpbcode=${corpbcode}



# 交单融资入口
ft_loan_dlydfa=交单融资申请单
ft_loan_dlydfa.dlydfaicode=交单融资申请内码
ft_loan_dlydfa.dlydfacode=交单融资申请单号
ft_loan_dlydfa.fintype=交单融资类型
ft_loan_dlydfa.bankcode=融资银行
ft_loan_dlydfa.fcode=融资币种
ft_loan_dlydfa.settdoccode=议付发票号
ft_loan_dlydfa.fcy=申请融资金额
ft_loan_dlydfa.efndate=预计融资下款日
ft_loan_dlydfa.sfcode=本位币币种
ft_loan_dlydfa.payertype=付费人
ft_loan_dlydfa.dlydcode=议付交单申请号
ft_loan_dlydfa.dlydicode=议付交单主表内码
ft_loan_dlydfa.lccode=信用证号
ft_loan_dlydfa.lcregicode=到证登记及认领内码
ft_loan_dlydfa.rdate=预计收款日期
ft_loan_dlydfa.dlydbankacccode=交单银行/账号
ft_loan_dlydfa.adate=申请日期
ft_loan_dlydfa.dlydfcy=交单金额
ft_loan_dlydfa.abgoods=货前后
ft_loan_dlydfa.paymode=收款方式
ft_loan_dlydfa.remark=备注
ft_loan_dlydfa.status=状态
ft_loan_dlydfa.wfcode=审批编码
ft_loan_dlydfa.wfuid=审批节点
ft_loan_dlydfa.salccode=客户
ft_loan_dlydfa.bcode=业务员部门
ft_loan_dlydfa.wcode=业务员
ft_loan_dlydfa.corpbcode=公司
ft_loan_dlydfa.sheetcode=单据类型
ft_loan_dlydfa.cuicode=商户
ft_loan_dlydfa.ratifydate=生效时间
ft_loan_dlydfa.submitdate=提交时间
ft_loan_dlydfa.performdate=审核时间
ft_loan_dlydfa.vprepare=创建人
ft_loan_dlydfa.predate=创建时间
ft_loan_dlydfa.modifier=修改人
ft_loan_dlydfa.modifydate=修改时间
ft_loan_dlydfa.isfullfin=是否全额融资


# 交单融资明细表
ft_loan_dlydfag.dlydfagicode=交单融资子表内码
ft_loan_dlydfag.dlydfaicode=交单融资主表内码
ft_loan_dlydfag.idx=序号
ft_loan_dlydfag.salordcode=销售合同号
ft_loan_dlydfag.salordicode=销售合同号内码
ft_loan_dlydfag.salprjicode=销售业务编号内码
ft_loan_dlydfag.salprjcode=销售业务编号
ft_loan_dlydfag.salshipcoder=发货单号
ft_loan_dlydfag.salshipicoder=发货单内码
ft_loan_dlydfag.tsosicoder=出仓回单号内码
ft_loan_dlydfag.tsoscoder=出仓回单号
ft_loan_dlydfag.dlydfcy=交单金额
ft_loan_dlydfag.purordcode=采购合同号
ft_loan_dlydfag.purordicode=采购合同号内码
ft_loan_dlydfag.purprjicode=采购业务编号内码
ft_loan_dlydfag.purprjcode=采购业务编号
ft_loan_dlydfag.purshipicoder=到货单号内码
ft_loan_dlydfag.purshipcoder=到货单号
ft_loan_dlydfag.gcode=商品编码
ft_loan_dlydfag.cnamedesc=商品名称
ft_loan_dlydfag.enamedesc=商品英文名称
ft_loan_dlydfag.gvcode=商品细类
ft_loan_dlydfag.wcode=业务员
ft_loan_dlydfag.bcode=业务员部门


# 交单放款信息
ft_loan_dlydfali.dlydfaliicode=放款信息内码
ft_loan_dlydfali.dlydfacode=交单融资申请单号
ft_loan_dlydfali.dlydfaicode=交单融资申请内码
ft_loan_dlydfali.srcsys=来源系统
ft_loan_dlydfali.srcsyscode=来源系统唯一ID
ft_loan_dlydfali.dlydcode=议付交单申请单号
ft_loan_dlydfali.dlydicode=议付交单申请单内码
ft_loan_dlydfali.fintype=交单融资类型
ft_loan_dlydfali.bankacccode=融资银行账号
ft_loan_dlydfali.paymode=收款方式
ft_loan_dlydfali.lccode=信用证号
ft_loan_dlydfali.lcregicode=到证登记及认领内码
ft_loan_dlydfali.fcode=融资币种
ft_loan_dlydfali.tcaptime=事务处理日期
ft_loan_dlydfali.bankcode=融资银行
ft_loan_dlydfali.isrecourse=是否有追索权
ft_loan_dlydfali.fcy=申请融资金额
ft_loan_dlydfali.actualfcy=实际融资金额
ft_loan_dlydfali.startdate=融资起始日
ft_loan_dlydfali.duedate=融资到期日
ft_loan_dlydfali.docrate=融资利率
ft_loan_dlydfali.irtfcy=融资利息
ft_loan_dlydfali.feefcy=融资手续费
ft_loan_dlydfali.payertype=付费人
ft_loan_dlydfali.bankno=网银流水号
ft_loan_dlydfali.cuicode=商户
ft_loan_dlydfali.vprepare=创建人
ft_loan_dlydfali.modifier=修改人
ft_loan_dlydfali.predate=创建时间
ft_loan_dlydfali.modifydate=修改时间
ft_loan_dlydfali.bcode=业务员部门
ft_loan_dlydfali.wcode=业务员
ft_loan_dlydfali.corpbcode=公司
ft_loan_dlydfali.keptbcode=记账部门
ft_loan_dlydfali.fserate=汇率
ft_loan_dlydfali.sfcode=本位币币种
ft_loan_dlydfali.scy=折本位币申请融资金额
ft_loan_dlydfali.actualscy=折本位币实际融资金额
ft_loan_dlydfali.scerate=折人民币汇率
ft_loan_dlydfali.suerate=折美元汇率
ft_loan_dlydfali.irtscy=折本位币融资利息
ft_loan_dlydfali.feescy=折本位币融资手续费
ft_loan_dlydfali.redflag=红冲标记
ft_loan_dlydfali.dlydfaliicoder=放款信息红冲内码
ft_loan_dlydfali.tcaptimefm=事务处理日期从
ft_loan_dlydfali.tcaptimeto=到
ft_loan_dlydfali.batchvid=凭证号
ft_loan_dlydfali.salccode=客户
ft_loan_dlydfali.viddesc=凭证号

# 交单还款信息
ft_loan_dlydfari.dlydfariicode=还款信息内码
ft_loan_dlydfari.dlydfacode=交单融资申请单号
ft_loan_dlydfari.dlydfaicode=交单融资申请内码
ft_loan_dlydfari.srcsys=来源系统
ft_loan_dlydfari.srcsyscode=来源系统唯一ID
ft_loan_dlydfari.dlydcode=议付交单申请单号
ft_loan_dlydfari.dlydicode=议付交单申请单内码
ft_loan_dlydfari.repdate=还款日期
ft_loan_dlydfari.fcy=客户还款金额
ft_loan_dlydfari.bankacccode=还款银行账号
ft_loan_dlydfari.bankno=还款网银流水号
ft_loan_dlydfari.feefcy=客户还款手续费
ft_loan_dlydfari.loanbankacccode=还贷银行账号
ft_loan_dlydfari.loanbankno=还贷网银流水号
ft_loan_dlydfari.loanfcy=还贷金额
ft_loan_dlydfari.loanfeefcy=还贷手续费
ft_loan_dlydfari.loanirtfcy=还贷利息
ft_loan_dlydfari.recbankacccode=收款银行账号
ft_loan_dlydfari.recbankno=收款网银流水号
ft_loan_dlydfari.recfcy=收款金额
ft_loan_dlydfari.redflag=红冲
ft_loan_dlydfari.tcaptime=事务处理日期
ft_loan_dlydfari.bcode=业务员部门
ft_loan_dlydfari.corpbcode=公司
ft_loan_dlydfari.vprepare=创建人
ft_loan_dlydfari.modifier=修改人
ft_loan_dlydfari.keptbcode=记账部门
ft_loan_dlydfari.predate=创建时间
ft_loan_dlydfari.modifydate=修改时间
ft_loan_dlydfari.fserate=汇率
ft_loan_dlydfari.sfcode=本位币币种
ft_loan_dlydfari.scy=折本位币客户还款金额
ft_loan_dlydfari.scerate=折人民币汇率
ft_loan_dlydfari.suerate=折美元汇率
ft_loan_dlydfari.colscy=折本位币收款金额
ft_loan_dlydfari.feescy=折本位币还款手续费
ft_loan_dlydfari.loanscy=折本位币还贷金额
ft_loan_dlydfari.loanfeescy=折本位币还贷手续费
ft_loan_dlydfari.irtscy=折本位币还贷利息
ft_loan_dlydfari.dlydfariicoder=还款信息红冲内码
ft_loan_dlydfari.batchvid=凭证号
ft_loan_dlydfari.salccode=客户
ft_loan_dlydfari.viddesc=凭证号

# 交单融资拷贝工作台
ft_loan_dlydfa_cb_view.dlydicode=议付交单主表内码
ft_loan_dlydfa_cb_view.dlydcode=议付交单申请单号
ft_loan_dlydfa_cb_view.salccode=客户
ft_loan_dlydfa_cb_view.fcode=交单币种
ft_loan_dlydfa_cb_view.fcy=交单金额
ft_loan_dlydfa_cb_view.dlyddate=交单日期
ft_loan_dlydfa_cb_view.bankacccode=交单银行/账号
ft_loan_dlydfa_cb_view.lccode=信用证号
ft_loan_dlydfa_cb_view.lcregicode=到证登记及认领内码
ft_loan_dlydfa_cb_view.rdate=预计收款日期
ft_loan_dlydfa_cb_view.settdoccode=议付发票号
ft_loan_dlydfa_cb_view.acdate=承兑日期
ft_loan_dlydfa_cb_view.abgoods=货前后
ft_loan_dlydfa_cb_view.paymode=收款方式
ft_loan_dlydfa_cb_view.bcode=业务员部门
ft_loan_dlydfa_cb_view.wcode=业务员
ft_loan_dlydfa_cb_view.corpbcode=公司
ft_loan_dlydfa_cb_view.cuicode=商户
ft_loan_dlydfa_cb_view.rdatefm=预计收款日期从
ft_loan_dlydfa_cb_view.rdateto=到
ft_loan_dlydfa_cb_view.salccode=客户

#内部借款接口相关
ft_acs.innirt.innirtVos=接口参数[innirtVos]
ft_acs.innirt.corpbcode=借出公司[corpbcode]
ft_acs.innirt.ccode=借入公司[ccode]
ft_acs.innirt.fcode=币种[fcode]
ft_acs.innirt.tcaptime=事务处理时间[tcaptime]
ft_acs.innirt.bcode=业务员部门[bcode]
ft_acs.innirt.isred=是否红冲[isred]
ft_acs.innirt.ebsicode=EBS单据内码[ebsicode]
ft_acs.innirt.innirtSaveDetail=借款信息[innirtsavedetail]
ft_acs.innirt.loanordcode=借款合同号[loanordcode]
ft_acs.innirt.loanfcy=借款金额[loanfcy]
ft_acs.innirt.balfcy=借款余额[balfcy]
ft_acs.innirt.fcy=借款额[fcy]
ft_acs.innirt.loadstartdate=借款起始日期[loadstartdate]
ft_acs.innirt.irtstartdate=利息起始日期[irtstartdate]
ft_acs.innirt.irtduedate=利息到期日期[irtduedate]
ft_acs.innirt.irtcalcdays=计息天数[irtcalcdays]
ft_acs.innirt.irtrate=计息利率[irtrate]
ft_acs.innirt.fundsrctype=资金来源[fundsrctype]
ft_acs.innirt.irtfcy=利息[irtfcy]
ft_acs.innirt.irtscy=本位币利息[irtscy]
ft_acs.innirt.irtzusd=折美元利息[irtzusd]
ft_acs.innirt.irtzcny=折人民币利息[irtzcny]
ft_acs.innirt.irtbasfcy=计息基数[irtbasfcy]
ft_acs.innirt.finaordcode=融资合同编号[finaordcode]

# 赎单融资放款结果回传接口相关
ft_acs.acdfin.accOrdFinaLendInfoVos=赎单融资放款结果信息[accOrdFinaLendInfoVos]
ft_acs.acdfin.srcsys=来源系统[srcsys]
ft_acs.acdfin.srcsyscode=来源系统唯一ID[srcsyscode]
ft_acs.acdfin.acdicode=承兑赎单内码[acdicode]
ft_acs.acdfin.bankacccode=融资银行账号[bankacccode]
ft_acs.acdfin.bankcode=融资银行[bankcode]
ft_acs.acdfin.isdiff=异币种融资[isdiff]
ft_acs.acdfin.fcode=融资币种[fcode]
ft_acs.acdfin.fcy=融资金额[fcy]
ft_acs.acdfin.startdate=融资日期[startdate]
ft_acs.acdfin.duedate=融资到期日[duedate]
ft_acs.acdfin.yrate=年利率[yrate]
ft_acs.acdfin.planrate=安排费率[planrate]
ft_acs.acdfin.issimulate=是否模拟[issimulate]
ft_acs.acdfin.headbcode=本部部门[headbcode]
ft_acs.acdfin.operator=操作人[operator]
ft_acs.acdfin.operatetime=操作时间[operatetime]
# 赎单融资申请撤回结果接口相关
ft_acs.acdfin.back.retractresult=撤回是否成功[retractresult]
ft_acs.acdfin.back.confirmtype=撤回确认类型[confirmtype]
ft_acs.acdfin.back.srcicode=申请单内码[srcicode]
ft_acs.acdfin.back.operator=操作人[operator]
ft_acs.acdfin.back.accOrdLoanParams=赎单融资撤回结果信息[accOrdLoanParams]
# 融资展期结果回传接口相关
ft_acs.acdfin.extend.accOrdFinaExtendResultInfo=赎单融资展期结果信息[accOrdFinaExtendResultInfo]
ft_acs.acdfin.extend.srcSys=来源系统[srcSys]
ft_acs.acdfin.extend.srcSysCode=来源系统唯一ID[srcSysCode]
ft_acs.acdfin.extend.acdFaeicode=押汇展期申请内码[acdFaeicode]
ft_acs.acdfin.extend.acdicode=承兑赎单内码[acdicode]
ft_acs.acdfin.extend.startdate=展期起始日期[startdate]
ft_acs.acdfin.extend.duedate=展期到期日[duedate]
ft_acs.acdfin.extend.fcy=展期金额[fcy]
ft_acs.acdfin.extend.planrate=安排费率[planrate]
ft_acs.acdfin.extend.exrate=展期利率[exrate]
ft_acs.acdfin.extend.issimulate=是否模拟[issimulate]
ft_acs.acdfin.extend.headbcode=本部部门[headbcode]
ft_acs.acdfin.extend.operator=操作人[operator]
ft_acs.acdfin.extend.exDetails=融资展期信息[exDetails]
ft_acs.acdfin.extend.bcode=业务员部门[bcode]
ft_acs.acdfin.extend.exfcy=展期金额[exfcy]
#赎单融资工作台结果撤回接口
ft_acs.acdfin.workbench.accOrdFinaWorkbenchBackParams=融资工作台回传参数[accOrdFinaWorkbenchBackParams]
ft_acs.acdfin.workbench.srcsys=来源系统[srcsys]
ft_acs.acdfin.workbench.srcsyscode=来源系统唯一ID[srcsyscode]
ft_acs.acdfin.workbench.confirmtype=撤回类型[confirmtype]
ft_acs.acdfin.workbench.operator=操作人[operator]
#赎单融资申请还款结果回传接口
ft_acs.acdfin.back.ats.bcode=业务员部门[bcode]
ft_acs.acdfin.back.ats.fcy=还款金额(融资币种)[fcy]
ft_acs.acdfin.back.ats.srcSys=来源系统[srcSys]
ft_acs.acdfin.back.ats.srcSysCode=来源系统唯一ID[srcSysCode]
ft_acs.acdfin.back.ats.acdfaricode=押汇还款申请内码[acdfaricode]
ft_acs.acdfin.back.ats.acdicode=承兑单号内码[acdicode]
ft_acs.acdfin.back.ats.repdate=还款日期[repdate]
ft_acs.acdfin.back.ats.bankacccode=还款银行账号[bankacccode]
ft_acs.acdfin.back.ats.bankcode=还款银行[bankcode]
ft_acs.acdfin.back.ats.fcode=还款币种[fcode]
ft_acs.acdfin.back.ats.mfcy=还款金额[fcy]
ft_acs.acdfin.back.ats.issimulate=是否模拟[issimulate]
ft_acs.acdfin.back.ats.headbcode=本部部门[headbcode]
ft_acs.acdfin.back.ats.operator=操作人[operator]
ft_acs.acdfin.back.ats.details=部门融资金额明细
ft_acs.acdfin.back.ats.accordfinaBackAtsResultInfo=赎单还款结果信息[accordfinaBackAtsResultInfo]

# 银行费用
ft_fee_bfee.bfeeicode=银行费用内码
ft_fee_bfee.bfeeicoder=银行费用红冲核销内码
ft_fee_bfee.cuicode=商户
ft_fee_bfee.odate=业务发生日期
ft_fee_bfee.fromrptype=来源费用类型
ft_fee_bfee.sfcode=本位币币种
ft_fee_bfee.fcy=金额
ft_fee_bfee.scy=本位币金额
ft_fee_bfee.zcny=人民币金额
ft_fee_bfee.zusd=美元金额
ft_fee_bfee.fserate=折本位币汇率
ft_fee_bfee.scerate=折人民币汇率
ft_fee_bfee.suerate=折美元汇率
ft_fee_bfee.srcsheetcode=来源单据类型
ft_fee_bfee.srcicode=来源单据内码
ft_fee_bfee.srccode=来源单据号
ft_fee_bfee.srcgicode=来源单据子表内码
ft_fee_bfee.sheetcode=单据类型
ft_fee_bfee.status=状态
ft_fee_bfee.gvcode=商品类目
ft_fee_bfee.cfmode=认定方式
ft_fee_bfee.rptype=费用类型
ft_fee_bfee.feerptype=费用类型
ft_fee_bfee.corpbcode=公司
ft_fee_bfee.costbcode=核算组
ft_fee_bfee.bcode=部门
ft_fee_bfee.wcode=人员
ft_fee_bfee.fcode=币种
ft_fee_bfee.ccode=结算对象
ft_fee_bfee.ccodetrust=委托方
ft_fee_bfee.purprjcode=采购业务编号
ft_fee_bfee.purprjicode=采购业务编号内码
ft_fee_bfee.salprjcode=销售业务编号
ft_fee_bfee.salprjicode=销售业务编号内码
ft_fee_bfee.purordcode=采购合同号
ft_fee_bfee.purordicode=采购合同内码
ft_fee_bfee.salordcode=销售合同号
ft_fee_bfee.salordicode=销售合同号内码
ft_fee_bfee.salshipcoder=发货单号
ft_fee_bfee.salshipicoder=发货单号内码
ft_fee_bfee.purshipcoder=到货单号
ft_fee_bfee.purshipicoder=到货单号内码
ft_fee_bfee.tsosicoder=出仓回单内码
ft_fee_bfee.tsoscoder=出仓回单号
ft_fee_bfee.invcoder=发票号
ft_fee_bfee.invicoder=发票号内码
ft_fee_bfee.invsheetcode=发票单据号
ft_fee_bfee.ptcode=协议单号
ft_fee_bfee.pticode=协议内码
ft_fee_bfee.eclcode=报关单号



# 交单放款信息视图
ft_loan_dlydfali_view.dlydfaliicode=放款信息内码
ft_loan_dlydfali_view.dlydfacode=交单融资申请单号
ft_loan_dlydfali_view.dlydfaicode=交单融资申请内码
ft_loan_dlydfali_view.srcsys=来源系统
ft_loan_dlydfali_view.srcsyscode=来源系统唯一ID
ft_loan_dlydfali_view.dlydcode=议付交单申请单号
ft_loan_dlydfali_view.dlydicode=议付交单申请单内码
ft_loan_dlydfali_view.fintype=交单融资类型
ft_loan_dlydfali_view.bankacccode=融资银行账号
ft_loan_dlydfali_view.paymode=收款方式
ft_loan_dlydfali_view.lccode=信用证号
ft_loan_dlydfali_view.lcregicode=到证登记及认领内码
ft_loan_dlydfali_view.fcode=融资币种
ft_loan_dlydfali_view.tcaptime=事务处理日期
ft_loan_dlydfali_view.bankcode=融资银行
ft_loan_dlydfali_view.isrecourse=是否有追索权
ft_loan_dlydfali_view.fcy=申请融资金额
ft_loan_dlydfali_view.actualfcy=实际融资金额
ft_loan_dlydfali_view.startdate=融资起始日
ft_loan_dlydfali_view.duedate=融资到期日
ft_loan_dlydfali_view.docrate=融资利率
ft_loan_dlydfali_view.irtfcy=融资利息
ft_loan_dlydfali_view.feefcy=融资手续费
ft_loan_dlydfali_view.payertype=付费人
ft_loan_dlydfali_view.bankno=网银流水号
ft_loan_dlydfali_view.cuicode=商户
ft_loan_dlydfali_view.vprepare=创建人
ft_loan_dlydfali_view.modifier=修改人
ft_loan_dlydfali_view.predate=创建时间
ft_loan_dlydfali_view.modifydate=修改时间
ft_loan_dlydfali_view.bcode=业务员部门
ft_loan_dlydfali_view.wcode=业务员
ft_loan_dlydfali_view.corpbcode=公司
ft_loan_dlydfali_view.keptbcode=记账部门
ft_loan_dlydfali_view.fserate=汇率
ft_loan_dlydfali_view.sfcode=本位币币种
ft_loan_dlydfali_view.scy=折本位币申请融资金额
ft_loan_dlydfali_view.actualscy=折本位币实际融资金额
ft_loan_dlydfali_view.scerate=折人民币汇率
ft_loan_dlydfali_view.suerate=折美元汇率
ft_loan_dlydfali_view.irtscy=折本位币融资利息
ft_loan_dlydfali_view.feescy=折本位币融资手续费
ft_loan_dlydfali_view.redflag=红冲标记
ft_loan_dlydfali_view.dlydfaliicoder=放款信息红冲内码
ft_loan_dlydfali_view.tcaptimefm=事务处理日期从
ft_loan_dlydfali_view.tcaptimeto=到
ft_loan_dlydfali_view.batchvid=凭证号
ft_loan_dlydfali_view.salccode=客户
ft_loan_dlydfali_view.fcyfm=申请融资金额从
ft_loan_dlydfali_view.fcyto=到
ft_loan_dlydfali_view.viddesc=凭证号


# 交单还款信息视图
ft_loan_dlydfari_view.dlydfariicode=还款信息内码
ft_loan_dlydfari_view.dlydfacode=交单融资申请单号
ft_loan_dlydfari_view.dlydfaicode=交单融资申请内码
ft_loan_dlydfari_view.srcsys=来源系统
ft_loan_dlydfari_view.srcsyscode=来源系统唯一ID
ft_loan_dlydfari_view.dlydcode=议付交单申请单号
ft_loan_dlydfari_view.dlydicode=议付交单申请单内码
ft_loan_dlydfari_view.repdate=还款日期
ft_loan_dlydfari_view.fcy=客户还款金额
ft_loan_dlydfari_view.bankacccode=还款银行账号
ft_loan_dlydfari_view.bankno=还款网银流水号
ft_loan_dlydfari_view.feefcy=客户还款手续费
ft_loan_dlydfari_view.loanbankacccode=还贷银行账号
ft_loan_dlydfari_view.loanbankno=还贷网银流水号
ft_loan_dlydfari_view.loanfcy=还贷金额
ft_loan_dlydfari_view.loanfeefcy=还贷手续费
ft_loan_dlydfari_view.loanirtfcy=还贷利息
ft_loan_dlydfari_view.recbankacccode=收款银行账号
ft_loan_dlydfari_view.recbankno=收款网银流水号
ft_loan_dlydfari_view.recfcy=收款金额
ft_loan_dlydfari_view.redflag=红冲
ft_loan_dlydfari_view.tcaptime=事务处理日期
ft_loan_dlydfari_view.bcode=业务员部门
ft_loan_dlydfari_view.corpbcode=公司
ft_loan_dlydfari_view.vprepare=创建人
ft_loan_dlydfari_view.modifier=修改人
ft_loan_dlydfari_view.keptbcode=记账部门
ft_loan_dlydfari_view.predate=创建时间
ft_loan_dlydfari_view.modifydate=修改时间
ft_loan_dlydfari_view.fserate=汇率
ft_loan_dlydfari_view.sfcode=本位币币种
ft_loan_dlydfari_view.scy=折本位币客户还款金额
ft_loan_dlydfari_view.scerate=折人民币汇率
ft_loan_dlydfari_view.suerate=折美元汇率
ft_loan_dlydfari_view.colscy=折本位币收款金额
ft_loan_dlydfari_view.feescy=折本位币还款手续费
ft_loan_dlydfari_view.loanscy=折本位币还贷金额
ft_loan_dlydfari_view.loanfeescy=折本位币还贷手续费
ft_loan_dlydfari_view.irtscy=折本位币还贷利息
ft_loan_dlydfari_view.dlydfariicoder=还款信息红冲内码
ft_loan_dlydfari_view.batchvid=凭证号
ft_loan_dlydfari_view.salccode=客户
ft_loan_dlydfari_view.viddesc=凭证号

# 交单融资放款结果回传接口相关
ft_acs.dlydfin.li.dataParams=交单融资放款结果信息[dataParams]
ft_acs.dlydfin.li.isrecourse=是否有追索权[isrecourse]
ft_acs.dlydfin.li.startdate=融资起始日[startdate]
ft_acs.dlydfin.li.duedate=融资到期日[duedate]
ft_acs.dlydfin.li.docrate=融资利率[docrate]
ft_acs.dlydfin.li.irtfcy=融资利息[irtfcy]
ft_acs.dlydfin.li.feefcy=融资手续费[feefcy]
ft_acs.dlydfin.li.fcy=申请融资金额[fcy]
ft_acs.dlydfin.li.actualfcy=实际融资金额[actualfcy]
ft_acs.dlydfin.li.payertype=付费人[payertype]
ft_acs.dlydfin.li.fintype=交单融资类型[fintype]
ft_acs.dlydfin.li.bankno=网银流水号[bankno]
ft_acs.dlydfin.li.bankacccode=融资银行账号[bankacccode]
ft_acs.dlydfin.li.operator=操作人[operator]
ft_acs.dlydfin.li.srcsyscode=来源系统唯一ID[srcsyscode]
ft_acs.dlydfin.li.dlydfaicode=交单融资申请内码[dlydfaicode]


# 交单融资还款结果回传接口相关
ft_acs.dlydfin.ri.dataParams=交单融资还款结果信息[dataParams]
ft_acs.dlydfin.ri.repdate=还款日期[repdate]
ft_acs.dlydfin.ri.fcy=还款金额[fcy]
ft_acs.dlydfin.ri.bankacccode=还款银行账号[bankacccode]
ft_acs.dlydfin.ri.bankno=还款网银流水号[bankno]
ft_acs.dlydfin.ri.feefcy=还款手续费[feefcy]
ft_acs.dlydfin.ri.loanbankacccode=还贷银行账号[loanbankacccode]
ft_acs.dlydfin.ri.loanbankno=还贷网银流水号[loanbankno]
ft_acs.dlydfin.ri.loanfeefcy=还贷手续费[loanfeefcy]
ft_acs.dlydfin.ri.loanirtfcy=还贷利息[loanirtfcy]
ft_acs.dlydfin.ri.recbankacccode=收款银行账号[recbankacccode]
ft_acs.dlydfin.ri.recbankno=收款网银流水号[recbankno]
ft_acs.dlydfin.ri.recfcy=收款金额[recfcy]
ft_acs.dlydfin.ri.operator=操作人[operator]
ft_acs.dlydfin.ri.srcsyscode=来源系统唯一ID[srcsyscode]
ft_acs.dlydfin.ri.dlydfaicode=交单融资申请内码[dlydfaicode]

# 交单融资办理放款、还款取消接口相关
ft_acs.dlyd.finacancel.dataParams=交单融资放款/还款取消结果信息[dataParams]
ft_acs.dlyd.finacancel.canceltype=取消撤回类型[canceltype]
ft_acs.dlyd.finacancel.operator=取消撤回类型[operator]
ft_acs.dlyd.finacancel.dlydfaicode=取消撤回类型[dlydfaicode]
ft_acs.dlyd.finacancel.srcsyscode=取消撤回类型[srcsyscode]
# 交单融资申请撤回是否成功相关
ft_acs.dlyd.finaback.dataParams=交单融资申请撤回是否成功信息[dataParams]
ft_acs.dlyd.finaback.operator=操作人[operator]
ft_acs.dlyd.finaback.retractresult=撤回是否成功[retractresult]
ft_acs.dlyd.finaback.dlydfaicode=交单融资申请内码[dlydfaicode]

# 保理融资申请主表
ft_loan_factfa=保理融资申请主表
ft_loan_factfa.factfaicode=保理融资申请内码
ft_loan_factfa.factfacode=保理融资申请单号
ft_loan_factfa.factccode=保理商
ft_loan_factfa.isfreqccode=是否频发型保理商
ft_loan_factfa.ccode=客商
ft_loan_factfa.fcode=币种
ft_loan_factfa.fcy=申请融资金额
ft_loan_factfa.facttype=保理性质
ft_loan_factfa.isrecourse=是否有追索权
ft_loan_factfa.isbackown=客户是否还款我司
ft_loan_factfa.salprjicodelist=销售业务内码
ft_loan_factfa.salprjcodelist=销售业务编号
ft_loan_factfa.salordicodelist=销售合同内码
ft_loan_factfa.salordcodelist=销售合同号
ft_loan_factfa.tsosicoderlist=出仓回单内码
ft_loan_factfa.tsoscoderlist=出仓回单号

# 保理融资往来信息表
ft_loan_factfag=保理融资往来信息表
ft_loan_factfag.factfagicode=保理融资往来信息内码
ft_loan_factfag.factfaicode=保理融资申请内码
ft_loan_factfag.lrpicodex=往来应收付核销码
ft_loan_factfag.rptype=收付项目
ft_loan_factfag.tsosicoder=出仓回单内码
ft_loan_factfag.tsoscoder=出仓回单号
ft_loan_factfag.salshipicoder=发货单内码
ft_loan_factfag.salshipcoder=发货单号
ft_loan_factfag.salprjicode=销售业务内码
ft_loan_factfag.salprjcode=销售业务编号
ft_loan_factfag.salordicode=销售合同内码
ft_loan_factfag.salordcode=销售合同号
ft_loan_factfag.purprjicode=采购业务内码
ft_loan_factfag.purprjcode=采购业务编号
ft_loan_factfag.purordicode=采购合同内码
ft_loan_factfag.purordcode=采购合同号
ft_loan_factfag.purshipicoder=到货单内码
ft_loan_factfag.purshipcoder=到货单号
ft_loan_factfag.recfcy=应收金额
ft_loan_factfag.fcy=申请融资金额

# 保理融资费用信息表
ft_loan_factfaf=保理融资费用信息表
ft_loan_factfaf.factfaficode=保理融资费用信息内码
ft_loan_factfaf.factfaicode=保理融资申请内码
ft_loan_factfaf.rptype=费用类型
ft_loan_factfaf.startdate=起息日
ft_loan_factfaf.enddate=结息日
ft_loan_factfaf.fcy=金额
ft_loan_factfaf.invstatus=发票状态
ft_loan_factfaf.purfeeinvicode=发票登记内码
ft_loan_factfaf.purfeeinvcode=发票登记号
ft_loan_factfaf.paystatus=付款状态
ft_loan_factfaf.paicode=付款申请单内码
ft_loan_factfaf.pacode=付款申请单号

# 保理融资处理工作台
tcaptimefm=事务处理日期从
appfcyfm=申请融资金额从

# 保理融资放款信息表
ft_loan_factfali=保理融资放款信息表
ft_loan_factfali.factfaliicode=保理融资放款信息内码
ft_loan_factfali.factfaicode=保理融资申请内码
ft_loan_factfali.factfacode=保理融资申请单号
ft_loan_factfali.factfaliicoder=保理融资放款信息红冲内码
ft_loan_factfali.factfaliicodeo=保理融资放款信息原始内码
ft_loan_factfali.srcsys=来源系统
ft_loan_factfali.srcsyscode=来源系统唯一ID
ft_loan_factfali.fiordcode=融资合同号
ft_loan_factfali.bankacccode=融资银行账号
ft_loan_factfali.bankcode=融资银行
ft_loan_factfali.fcode=融资币种
ft_loan_factfali.sfcode=本位币币种
ft_loan_factfali.facttype=保理性质
ft_loan_factfali.isrecourse=是否有追索权
ft_loan_factfali.appfcy=申请融资金额
ft_loan_factfali.appscy=折本位币申请融资金额
ft_loan_factfali.appzcny=折人民币申请融资金额
ft_loan_factfali.appzusd=折美元申请融资金额
ft_loan_factfali.fcy=实际融资金额
ft_loan_factfali.scy=折本位币实际融资金额
ft_loan_factfali.zcny=折人民币实际融资金额
ft_loan_factfali.zusd=折美元实际融资金额
ft_loan_factfali.startdate=融资起始日
ft_loan_factfali.enddate=融资到期日
ft_loan_factfali.farate=融资利率
ft_loan_factfali.irtfcy=融资利息
ft_loan_factfali.irtscy=折本位币融资利息
ft_loan_factfali.irtzcny=折人民币融资利息
ft_loan_factfali.irtzusd=折美元融资利息
ft_loan_factfali.feefcy=融资手续费
ft_loan_factfali.feescy=折本位币融资手续费
ft_loan_factfali.feezcny=折人民币融资手续费
ft_loan_factfali.feezusd=折美元融资手续费
ft_loan_factfali.redflag=红冲标记
ft_loan_factfali.vsnflag=版本标记
ft_loan_factfali.fserate=汇率
ft_loan_factfali.scerate=折人民币汇率
ft_loan_factfali.suerate=折美元汇率
ft_loan_factfali_view.vmarkicode=凭证标记内码
ft_loan_factfali_view.batchvid=凭证大号

# 保理融资还款信息表
ft_loan_factfari=保理融资还款信息表
ft_loan_factfari.factfariicode=保理融资还款信息内码
ft_loan_factfari.factfaicode=保理融资申请内码
ft_loan_factfari.factfacode=保理融资申请单号
ft_loan_factfari.factfariicoder=保理融资还款信息红冲内码
ft_loan_factfari.factfariicodeo=保理融资还款信息原始内码
ft_loan_factfari.ccode=客商
ft_loan_factfari.srcsys=来源系统
ft_loan_factfari.srcsyscode=来源系统唯一ID
ft_loan_factfari.repdate=还款日期
ft_loan_factfari.fcode=还款币种
ft_loan_factfari.fcy=还款金额
ft_loan_factfari.scy=折本位币还款金额
ft_loan_factfari.zcny=折人民币还款金额
ft_loan_factfari.zusd=折美元还款金额
ft_loan_factfari.redflag=红冲标记
ft_loan_factfari.vsnflag=版本标记
ft_loan_factfari.rclmg.fcy=金额

# 保理融资放款结果回传接口相关
FactFinaLoanResultBack.dataparams=保理融资放款结果信息[dataparams]
FactFinaLoanResultDataParam.startdate=融资起始日[startdate]
FactFinaLoanResultDataParam.enddate=融资到期日[enddate]
FactFinaLoanResultDataParam.farate=融资利率[farate]
FactFinaLoanResultDataParam.irtfcy=融资利息[irtfcy]
FactFinaLoanResultDataParam.feefcy=融资手续费[feefcy]
FactFinaLoanResultDataParam.fcy=实际融资金额[fcy]
FactFinaLoanResultDataParam.bankacccode=融资银行账号[bankacccode]
FactFinaLoanResultDataParam.operator=创建人[operator]
FactFinaLoanResultDataParam.srcsys=来源系统[srcsys]
FactFinaLoanResultDataParam.srcsyscode=来源系统唯一ID[srcsyscode]
FactFinaLoanResultDataParam.factfaicode=保理融资申请内码[factfaicode]
FactFinaLoanResultDataParam.fiordcode=融资合同号[fiordcode]
FactFinaLoanResultDataParam.facttype=保理性质[facttype]
FactFinaLoanResultDataParam.isrecourse=是否有追索权[isrecourse]

# 保理融资还款结果回传接口相关
FactFinaRepayInfoResultBack.repaydata=还款信息[repaydata]
RepayData.factfaicode=保理融资申请内码[factfaicode]
RepayData.srcsys=来源系统[srcsys]
RepayData.srcsyscode=来源系统唯一ID[srcsyscode]
RepayData.repdate=还款日期[repdate]
RepayData.fcode=还款币种[fcode]
RepayData.fcy=还款金额[fcy]
RepayData.predate=创建时间[predate]

# ABS批次号登记
ft_loan_absfabr.fabrcode=ABS批次号
ft_loan_absfabr.fatype=融资类型
ft_loan_absfabr.faprod=融资产品
ft_loan_absfabr.faorgz=融资机构
ft_loan_absfabr.factccode=保理商
ft_loan_absfabr.status=状态


# 模拟融资利息计提工作台
ft_loan_sim_acdfa.fcode=融资币种
ft_loan_sim_acdfa.acdfaicode=赎单融资申请单内码
ft_loan_sim_acdfa.acdfacode=赎单融资申请单号
ft_loan_sim_acdfa.acdcode=承兑单号
ft_loan_sim_acdfa.startdate=融资起始日
ft_loan_sim_acdfa.duedate=融资到期日
ft_loan_sim_acdfa.fcy=融资金额
ft_loan_sim_acdfa.yrate=年利率
ft_loan_sim_acdfa.issettle=是否已结清
ft_loan_sim_acdfa.ofcy=模拟借款余额
ft_loan_sim_acdfa.duedatefm=融资起始日 从
ft_loan_sim_acdfa.duedateto=到
ft_loan_sim_acdfa.details_pane=变动信息
ft_loan_sim_acdfa_details.acdtype=变动类型
ft_loan_sim_acdfa_details.yrate=年利率
ft_loan_sim_acdfa_details.fcy=本金
ft_loan_sim_acdfa_details.exctype=借/还
ft_loan_sim_acdfa_details.duedate=到期日
ft_loan_sim_acdfa_details.headbcode=本部部门
ft_loan_sim_acdfa_details.redflag#5=被红冲
ft_loan_sim_acdfa_details.redflag#2=红冲
ft_loan_sim_acdfa_details.issim=是否模拟
ft_loan_sim_acdfa_details.acddate=变动日期
ft_loan_sim_acdfa.siminterest=模拟利息计提
ft_loan_sim_acdfa_dialog.interestyear=计息年份
ft_loan_sim_acdfa_dialog.interestmonth=计息月份
