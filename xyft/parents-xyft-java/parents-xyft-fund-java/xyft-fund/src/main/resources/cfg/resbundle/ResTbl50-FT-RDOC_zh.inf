# 应收票据签收工作台 ok
ft_rdoc_sign=应收票据签收工作台
ft_rdoc_sign.rdocsignicode=应收票据签收工作台内码
ft_rdoc_sign.rdocsignicodev=应收票据签收工作台原版本内码
ft_rdoc_sign.signstatus=签收状态
ft_rdoc_sign.ccode=来票客户名称
ft_rdoc_sign.fcy=票据金额
ft_rdoc_sign.fcyed=已签收金额
ft_rdoc_sign.fcying=未签收金额
ft_rdoc_sign.srcsyscode=来源系统单据唯一流水号
ft_rdoc_sign.draftid=电票唯一标识
ft_rdoc_sign.rdocno=票据号
ft_rdoc_sign.isdircon=是否直联
ft_rdoc_sign.isfundcorp=是否财务公司
ft_rdoc_sign.iscantrans=是否可转让
ft_rdoc_sign.iscansplit=是否可拆分
ft_rdoc_sign.rdocbacktype=循环流转票类型
ft_rdoc_sign.docdatefm=出票日期从
ft_rdoc_sign.rdocdate=收票日期
ft_rdoc_sign.rdocdatefm=收票日期从
ft_rdoc_sign.duedate=票据到期日
ft_rdoc_sign.duedatefm=票据到期日从
ft_rdoc_sign.endorccode=最后背书人
ft_rdoc_sign.accepterbwg=承兑人黑白灰名单
ft_rdoc_sign.acpheadbankcode=承兑银行大行
ft_rdoc_sign.rdocname=收票人
ft_rdoc_sign.rdocbankcode=收票人银行
ft_rdoc_sign.rdocbankacccode=收票人账户
ft_rdoc_sign.operatetypetac=操作类型
ft_rdoc_sign.locksys=锁票系统
ft_rdoc_sign.accepterbgfcy=非承兑人白名单可签收额度
ft_rdoc_sign.srcsyscodeo=原来源系统单据唯一流水号
ft_rdoc_sign.tgbicode=同审单内码
ft_rdoc_sign.tgbcode=同审单号

# 应收票据签收工作台 票据签收弹出框 ok
ft_rdoc_sign.dlgsignstatus=签收结果
ft_rdoc_sign.number=票据张数
ft_rdoc_sign.docfcy=票据总金额
ft_rdoc_sign.isallsign=是否全部签收
ft_rdoc_sign.dlgfcy=单张签收金额
ft_rdoc_sign.sumfcy=签收总金额

# 应收票据签收工作台 签收明细 ok
ft_rdoc_signg.rdocsigngicode=应收票据签收工作台子表内码
ft_rdoc_signg.rdocsignicode=应收票据签收工作台内码
ft_rdoc_signg.rdocsignicodev=应收票据签收工作台原版本内码
ft_rdoc_signg.rdocsigngicoder=应收票据签收工作台子表红冲核销内码
ft_rdoc_signg.rdocsigngicodev=应收票据签收工作台子表原版本内码
ft_rdoc_signg.rdocicode=应收票据池内码
ft_rdoc_signg.rdoccode=应收票据认领单号
ft_rdoc_signg.fcy=签收金额
ft_rdoc_signg.claimstatus=认领状态
ft_rdoc_signg.redflag#5=${bered}
ft_rdoc_signg.redflag#2=${red}

# 应收票据池 ok
ft_rdoc_rdoc=票据认领单
ft_rdoc_rdoc.rdocicode=应收票据池内码
ft_rdoc_rdoc.corflag=核销标识
ft_rdoc_rdoc.draftid=电票唯一标识
ft_rdoc_rdoc.rdocno=票据号
ft_rdoc_rdoc.rdoccode=应收票据认领单号
ft_rdoc_rdoc.rdocsigngicode=应收票据签收工作台子表内码
ft_rdoc_rdoc.rdocsignicode=应收票据签收工作台内码
ft_rdoc_rdoc.docstatus=票据认领状态
ft_rdoc_rdoc.rclmfcyed=已认领金额
ft_rdoc_rdoc.rclmfcying=未认领金额
ft_rdoc_rdoc.docfcy=票据总金额
ft_rdoc_rdoc.fcy=票据金额
ft_rdoc_rdoc.fcyed=票据已用金额|含锁定
ft_rdoc_rdoc.fcying=票据可用金额|不含锁定
ft_rdoc_rdoc.usefcy=票据可用金额|含锁定
ft_rdoc_rdoc.lockfcy=锁定金额
ft_rdoc_rdoc.distlockfcy=贴现锁定金额
ft_rdoc_rdoc.endorselockfcy=背书锁定金额
ft_rdoc_rdoc.distfcy=贴现金额
ft_rdoc_rdoc.endorsefcy=背书金额
ft_rdoc_rdoc.plefcy=质押金额
ft_rdoc_rdoc.collfcy=托收金额
ft_rdoc_rdoc.distfcying=申请贴现金额
ft_rdoc_rdoc.endorsefcying=申请背书金额
ft_rdoc_rdoc.collablefcy=可托收金额
ft_rdoc_rdoc.collfcyed=托收累计金额
ft_rdoc_rdoc.pleablefcy=可质押金额
ft_rdoc_rdoc.plefcyed=质押累计金额
ft_rdoc_rdoc.isdircon=是否直联
ft_rdoc_rdoc.iscansplit=是否可拆分
ft_rdoc_rdoc.iscantrans=是否可转让
ft_rdoc_rdoc.isfundcorp=是否财务公司
ft_rdoc_rdoc.rdocbacktype=循环流转票类型
ft_rdoc_rdoc.duedate=票据到期日
ft_rdoc_rdoc.ccode=来票客户名称
ft_rdoc_rdoc.endorccode=最后背书人
ft_rdoc_rdoc.accepterbwg=承兑人黑白灰名单
ft_rdoc_rdoc.acpheadbankcode=承兑银行大行
ft_rdoc_rdoc.rdocname=收票人
ft_rdoc_rdoc.rdocbankcode=收票人银行
ft_rdoc_rdoc.rdocbankacccode=收票人账户
ft_rdoc_rdoc.deptbcode=${bubcode}
ft_rdoc_rdoc.rdocdate=收票日期
ft_rdoc_rdoc.srcsyscode=来源系统单据唯一流水号
ft_rdoc_rdoc.operatetypetac=操作类型
ft_rdoc_rdoc.isinit=是否初始化

# 应收票据台账 ok
ft_rdoc_rdoc_ct.tgttbl=目标表名
ft_rdoc_rdoc_ct.tgticode=目标表主键
ft_rdoc_rdoc_ct.rdocicode=应收票据池内码
ft_rdoc_rdoc_ct.fcy=票据金额
ft_rdoc_rdoc_ct.useful=用途
ft_rdoc_rdoc_ct.odate=业务日期
ft_rdoc_rdoc_ct.srcsheetcode=关联业务单据类型
ft_rdoc_rdoc_ct.srccode=关联业务单据号
ft_rdoc_rdoc_ct.srcicode=关联业务单据内码

# 应收票据分摊来源 ok
ft_rdoc_rdocs.rdocsicode=应收票据分摊来源内码
ft_rdoc_rdocs.rdocicode=应收票据池内码
ft_rdoc_rdocs.fcy=金额
ft_rdoc_rdocs.rptype=收付项目
ft_rdoc_rdocs.purordicode=采购合同内码
ft_rdoc_rdocs.salordicode=销售合同内码
ft_rdoc_rdocs.purprjicode=采购业务内码
ft_rdoc_rdocs.salprjicode=销售业务内码
ft_rdoc_rdocs.salshipicoder=发货单内码
ft_rdoc_rdocs.purshipicoder=到货单内码
ft_rdoc_rdocs.purshipcoder=到货单号
ft_rdoc_rdocs.tsosicoder=出仓回单内码
ft_rdoc_rdocs.tsoscoder=出仓单号

# 应收票据分摊明细 ok
ft_rdoc_rdocg.rdocgicode=应收票据分摊内码
ft_rdoc_rdocg.srcgicode=来源单据子表内码
ft_rdoc_rdocg.rdocicode=应收票据池内码
ft_rdoc_rdocg.rdocsicode=应收票据分摊来源内码
ft_rdoc_rdocg.rdocgicoder=应收票据分摊明细红冲核销内码
ft_rdoc_rdocg.srcicoder=来源单据红冲核销内码
ft_rdoc_rdocg.srcgicoder=来源单据子表红冲核销内码
ft_rdoc_rdocg.fcy=关联金额
ft_rdoc_rdocg.fcy2=转出金额
ft_rdoc_rdocg.fcy3=金额
ft_rdoc_rdocg.feefcy=财务费用
ft_rdoc_rdocg.feescy=财务费用折本位币金额
ft_rdoc_rdocg.rptype=收付项目
ft_rdoc_rdocg.purordicode=采购合同内码
ft_rdoc_rdocg.purordcode=采购合同号
ft_rdoc_rdocg.salordicode=销售合同内码
ft_rdoc_rdocg.salordcode=销售合同号
ft_rdoc_rdocg.purprjicode=采购业务内码
ft_rdoc_rdocg.purprjcode=采购业务编号
ft_rdoc_rdocg.salprjicode=销售业务内码
ft_rdoc_rdocg.salprjcode=销售业务编号
ft_rdoc_rdocg.salshipicoder=发货单内码
ft_rdoc_rdocg.salshipcoder=发货单号
ft_rdoc_rdocg.purshipicoder=到货单内码
ft_rdoc_rdocg.purshipcoder=到货单号
ft_rdoc_rdocg.tsosicoder=出仓回单内码
ft_rdoc_rdocg.tsoscoder=出仓回单号
ft_rdoc_rdocg.tsoscoder1=出仓单号
ft_rdoc_rdocg.tsosicoder1=出仓单内码
ft_rdoc_rdocg.rdoccode=应收票据认领单号
ft_rdoc_rdocg.isrecourse=是否有追索权
ft_rdoc_rdocg.distpayer=贴现下款方(其他)
ft_rdoc_rdocg.rptypeinit=费用项目

# 贴现申请单 ok
ft_rdoc_dist=贴现申请单
ft_rdoc_dist.rdocdisticode=贴现申请单内码
ft_rdoc_dist.rdocdistcode=贴现申请单号
ft_rdoc_dist.isdircon=是否直联
ft_rdoc_dist.fcy=贴现金额
ft_rdoc_dist.predatefm=创建日期从
ft_rdoc_dist.isbackupflow=是否后补流程

# 应收票据贴现工作台 ok
ft_rdoc_rdoc.distlockfcyflag=只查提前锁定
ft_rdoc_rdoc.isdistlock=是否提前锁定

# 贴现申请单 贴现明细 ok
ft_rdoc_distg.rdocdistgicode=贴现申请单子表内码
ft_rdoc_distg.rdocdisticode=贴现申请单内码
ft_rdoc_distg.rdocicode=应收票据池内码
ft_rdoc_distg.payirtmode=付息方式
ft_rdoc_distg.iscansplit=是否可拆分
ft_rdoc_distg.fcy=贴现金额
ft_rdoc_distg.distlockfcy=贴现锁定金额
ft_rdoc_distg.rdoccode=应收票据认领单号
ft_rdoc_distg.diststatus=贴现状态

# 贴现结果记录 ok
ft_rdoc_dists.rdocdistsicode=票据贴现明细内码
ft_rdoc_dists.rdocdistsicoder=票据贴现明细红冲核销内码
ft_rdoc_dists.rdocicode=应收票据池内码
ft_rdoc_dists.distfcyscy=实际贴现金额折本币金额
ft_rdoc_dists.distirtscy=贴现利息折本位币金额
ft_rdoc_dists.distirtbankno=贴现利息网银流水号
ft_rdoc_dists.rdocdistcode=贴现申请单号
ft_rdoc_dists.rdocdisticode=贴现申请单内码
ft_rdoc_dists.rdocdistgicode=贴现申请单子表内码
ft_rdoc_dists.isrecourse=是否有追索权
ft_rdoc_dists.distfcy=实际贴现金额
ft_rdoc_dists.distpayer=贴现下款方(其他)
ft_rdoc_dists.distpayerbank=贴现下款方(银行)
ft_rdoc_dists.distbankcode=贴现银行
ft_rdoc_dists.distbankacccode=贴现银行账户
ft_rdoc_dists.irtpaytype=利息扣除方式
ft_rdoc_dists.distrate=贴现利率
ft_rdoc_dists.distinterest=贴现利息
ft_rdoc_dists.distdate=贴现日期
ft_rdoc_dists.payirtmode=付息方式
ft_rdoc_dists.tcaptimefm=事务处理日期从
ft_rdoc_dists.vidflag=制证选项
ft_rdoc_dists.distdatefm=贴现日期从
ft_rdoc_dists.isdircon=是否直联
ft_rdoc_dists.vmarkicode=凭证标记表内码
ft_rdoc_dists.vousheetcode=凭证表单据类型
ft_rdoc_dists.batchvid=凭证号
ft_rdoc_dists.viddesc=凭证号
ft_rdoc_dists.diststatus=贴现状态
ft_rdoc_dists.iscansplit=是否可拆分
ft_rdoc_dists.rdoccode=应收票据认领单号
ft_rdoc_dists.isfundcorp=是否财务公司
ft_rdoc_dists.redflag#5=被红冲
ft_rdoc_dists.redflag#2=红冲
ft_rdoc_dists.duedate=票据到期日
ft_rdoc_dists.fcy=申请贴现金额

# 应收票据转移费率维护 ok
ft_rdoc_trate.rdoctratecode=应收票据转移费率维护编码
ft_rdoc_trate.deptbcode=${bubcode}
ft_rdoc_trate.acpbanktype=承兑银行大行分类
ft_rdoc_trate.yearrate=年利率(不含税)

# 承兑人银行大行分类对应关系维护
ft_rdoc_acbk.rdocacbkcode=承兑人银行大行分类对应关系维护编码
ft_rdoc_acbk.deptbcode=${bubcode}
ft_rdoc_acbk.acpheadbankcode=承兑银行大行
ft_rdoc_acbk.acpbanktype=承兑银行大行分类

# 票据转出单 ok
ft_rdoc_tsout=票据转出单
ft_rdoc_tsout.rdoctsouticode=票据转出单内码
ft_rdoc_tsout.rdoctsoutcode=票据转出单号
ft_rdoc_tsout.rdoctsouticoder=票据转出单红冲核销内码
ft_rdoc_tsout.rdoctsoutcoder=票据转出单红冲核销外码
ft_rdoc_tsout.rdoctsouticodeo=票据转出单原核销内码
ft_rdoc_tsout.tsoutdate=转出日期
ft_rdoc_tsout.tsoutdatefm=转出日期从
ft_rdoc_tsout.redflag=${redflagext}
ft_rdoc_tsout.redflag#5=${bered}
ft_rdoc_tsout.redflag#2=${red}
ft_rdoc_tsout.tsinbwcode=转入部门-业务员
ft_rdoc_tsout.tsinbcode=转入部门
ft_rdoc_tsout.tsinwcode=转入业务员
ft_rdoc_tsout.issametsin=是否同时转入
# 票据转出单凭证视图
ft_rdoc_tsoutvou_view.viddesc=${vid}
ft_rdoc_tsoutvou_view.rdoctsoutcode=票据转出单号
ft_rdoc_tsoutvou_view.tsoutdate=转出日期
ft_rdoc_tsoutvou_view.tsoutfcy=转出金额
ft_rdoc_tsoutvou_view.tsoutfeefcy=财务费用

# 票据转入单凭证视图
ft_rdoc_tsinvou_view.viddesc=${vid}
ft_rdoc_tsinvou_view.rdoctsincode=票据转入单号
ft_rdoc_tsinvou_view.tsindate=转入日期
ft_rdoc_tsinvou_view.tsinfcy=转入金额
ft_rdoc_tsinvou_view.tsinfeefcy=财务费用

# 票据转出单 弹出框 票据转移申请工作台 ok
ft_rdoc_rdoc.duedatefm=票据到期日从
ft_rdoc_rdoc.rdocdatefm=收票日期从
ft_rdoc_rdoc.fcyed2=票据已用金额
ft_rdoc_rdoc.fcying2=票据可用金额

# 票据转出单 转出明细 ok
ft_rdoc_tsoutg.rdoctsoutgicode=票据转出单子表内码
ft_rdoc_tsoutg.rdoctsouticode=票据转出单内码
ft_rdoc_tsoutg.rdocicode=应收票据池内码
ft_rdoc_tsoutg.rdoctsoutgicoder=票据转出单子表红冲核销内码
ft_rdoc_tsoutg.rdoctsouticoder=票据转出单红冲核销内码
ft_rdoc_tsoutg.fcy=转出金额
ft_rdoc_tsoutg.duedate=票据到期日
ft_rdoc_tsoutg.rdoccode=应收票据认领单号
ft_rdoc_tsoutg.yearrate=年利率(不含税)
ft_rdoc_tsoutg.feefcy=财务费用
ft_rdoc_tsoutg.iscansplit=是否可拆分
ft_rdoc_tsoutg.scy=转出金额折本位币金额
ft_rdoc_tsoutg.feescy=财务费用折本位币金额

# 票据转入单 ok
ft_rdoc_tsin=票据转入单
ft_rdoc_tsin.rdoctsinicode=票据转入单内码
ft_rdoc_tsin.rdoctsincode=票据转入单号
ft_rdoc_tsin.rdoctsinicoder=票据转入单红冲核销内码
ft_rdoc_tsin.rdoctsincoder=票据转入单红冲核销外码
ft_rdoc_tsin.rdoctsinicodeo=票据转入单原核销内码
ft_rdoc_tsin.rdoctsouticode=票据转出单内码
ft_rdoc_tsin.tsindate=转入日期
ft_rdoc_tsin.tsindatefm=转入日期从
ft_rdoc_tsin.tsoutbcode=转出部门
ft_rdoc_tsin.issametsin=是否同时转入
ft_rdoc_tsin.redflag=${redflagext}
ft_rdoc_tsin.redflag#5=${bered}
ft_rdoc_tsin.redflag#2=${red}

# 票据转入单 转入明细 ok
ft_rdoc_tsing.rdoctsingicode=票据转入单子表内码
ft_rdoc_tsing.rdoctsinicode=票据转入单内码
ft_rdoc_tsing.rdocicode=应收票据池内码
ft_rdoc_tsing.rdoctsingicoder=票据转入单子表红冲核销内码
ft_rdoc_tsing.rdoctsinicoder=票据转入单红冲核销内码
ft_rdoc_tsing.rdoctsoutgicode=票据转出单子表内码
ft_rdoc_tsing.fcy=转出金额
ft_rdoc_tsing.duedate=票据到期日
ft_rdoc_tsing.rdoccode=应收票据认领单号
ft_rdoc_tsing.yearrate=年利率(不含税)
ft_rdoc_tsing.feefcy=财务费用
ft_rdoc_tsing.isdircon=是否直联
ft_rdoc_tsing.scy=转出金额折本位币金额
ft_rdoc_tsing.feescy=财务费用折本位币金额

# 票据转出单/票据转入单 转入财务费用 ok
ft_rdoc_infee.rdocinfeeicode=转入财务费用子表内码
ft_rdoc_infee.rdocinfeeicoder=转入财务费用子表红冲核销内码
ft_rdoc_infee.rdoctsouticode=票据转出单内码
ft_rdoc_infee.rdoctsouticoder=票据转出单红冲核销内码
ft_rdoc_infee.rdoctsoutgicode=票据转出单子表内码
ft_rdoc_infee.rdoctsoutgicoder=票据转出单子表红冲核销内码
ft_rdoc_infee.rdoctsinicode=票据转入单内码
ft_rdoc_infee.rdoctsinicoder=票据转入单红冲核销内码
ft_rdoc_infee.rdoctsingicode=票据转入单子表内码
ft_rdoc_infee.rdoctsingicoder=票据转入单子表红冲核销内码
ft_rdoc_infee.purordicode=采购合同内码
ft_rdoc_infee.salordicode=销售合同号内码
ft_rdoc_infee.purprjcode=采购业务编号
ft_rdoc_infee.purprjicode=采购业务编号内码
ft_rdoc_infee.salprjcode=销售业务编号
ft_rdoc_infee.salprjicode=销售业务编号内码
ft_rdoc_infee.fcy=转入金额
ft_rdoc_infee.feefcy=财务费用
ft_rdoc_infee.feescy=财务费用折本位币金额

# 应收票据托收 ok
ft_rdoc_coll.rdoccollicode=应收票据托收内码
ft_rdoc_coll.srcsyscode=来源系统单据唯一流水号
ft_rdoc_coll.rdocno=票据号
ft_rdoc_coll.colldate=托收日期
ft_rdoc_coll.colldatefm=托收日期从
ft_rdoc_coll.fcy=托收金额
ft_rdoc_coll.collbankacccode=托收银行账户
ft_rdoc_coll.rdocicode=应收票据池内码
ft_rdoc_coll.collbankcode=托收银行
ft_rdoc_coll.collfcy=托收总金额
ft_rdoc_coll.fcyingflag=只查待执行金额>0
ft_rdoc_rdoc.collablefcy2=计划执行金额
ft_rdoc_rdoc.collfcyed2=已执行金额
ft_rdoc_rdoc.collfcying=待执行金额
ft_rdoc_rdoc.collbankacccode2=托收银行账号

# 应收票据锁定工作台 ok
ft_rdoc_lock.rdoclockicode=应收票据锁定工作台内码
ft_rdoc_lock.rdocicode=应收票据池内码
ft_rdoc_lock.lockpurpose=锁定用途
ft_rdoc_lock.locktype=锁定/取消锁定
ft_rdoc_lock.fcy=票据金额
ft_rdoc_lock.lockfcy=锁定金额
ft_rdoc_lock.lockduedate=锁定到期日
ft_rdoc_lock.rdoccancleicode=取消锁定内码
ft_rdoc_rdoc.docdatefm=出票日期从
ft_rdoc_rdoc.fcyingflagre=只查剩余可锁定金额>0
ft_rdoc_rdoc.usefcy2=可锁定总金额
ft_rdoc_rdoc.lockfcy2=已锁定金额
ft_rdoc_rdoc.fcying3=剩余可锁定金额

# 应收票据托收凭证
ft_rdoc_rdoc.colldatefm=托收到期日从
ft_rdoc_collvou_view.viddesc=${vid}
ft_rdoc_collvou_view.duedatefm=${docduedatefm}
ft_rdoc_collvou_view.duedate=${docduedate}
ft_rdoc_collvou_view.fcy=${docfcy}
ft_rdoc_collvou_view.bcode=部门
ft_rdoc_collvou_view.isrecourse=是否有追索权
ft_rdoc_collvou_view.isfundcorp=是否财务公司
ft_rdoc_collvou_view.vcollfcy=${ft_rdoc_coll.fcy}
ft_rdoc_collvou_view.collbankacccode=托收银行账号

####### 接口相关 ######
#【B15】直联收票
RdocSignSourceReq.waitBillVoList=签收结果
RdocSignSourceReqData.srcsyscode=来源系统单据唯一流水号
RdocSignSourceReqData.draftid=电票唯一标识
RdocSignSourceReqData.rdocno=票据号
RdocSignSourceReqData.docno=票据编号
RdocSignSourceReqData.doctype=票据类型
RdocSignSourceReqData.subdocfm=子票区间起
RdocSignSourceReqData.subdocto=子票区间止
RdocSignSourceReqData.fcode=币种
RdocSignSourceReqData.fcy=票据金额
RdocSignSourceReqData.isdircon=是否直联
RdocSignSourceReqData.isfundcorp=是否财务公司
RdocSignSourceReqData.iscantrans=是否可转让
RdocSignSourceReqData.iscansplit=是否可拆分
RdocSignSourceReqData.rdocbacktype=循环流转票类型
RdocSignSourceReqData.docdate=出票日期
RdocSignSourceReqData.duedate=票据到期日
RdocSignSourceReqData.drawbankcode=出票人银行
RdocSignSourceReqData.drawer=出票人
RdocSignSourceReqData.drawbankaccount=出票人账号
RdocSignSourceReqData.endorccode=最后背书人
RdocSignSourceReqData.accepter=承兑人
RdocSignSourceReqData.acpbankaccount=承兑人账号
RdocSignSourceReqData.acpbankcode=承兑人银行
RdocSignSourceReqData.accepterbwg=承兑人黑白灰名单
RdocSignSourceReqData.acpheadbankcode=承兑银行大行
RdocSignSourceReqData.rdocname=收票人
RdocSignSourceReqData.rdocbankcode=收票人银行
RdocSignSourceReqData.rdocbankacccode=收票人账户
RdocSignSourceReqData.operatetypetac=操作类型

#【B23】接收 生效
RecDocSignStatusReq.signdata=签收/拒收结果
RecDocSignStatusReqData.srcsyscode=来源系统单据唯一流水号
RecDocSignStatusReqData.srcsyscodeo=原来源系统单据唯一流水号
RecDocSignStatusReqData.draftid=电票唯一标识
RecDocSignStatusReqData.signstatus=签收状态

#【XBBRCP01】锁定
RdocSignLockSourceReq.signdata=待签收分发锁票及解锁信息
RdocSignLockSourceReqData.srcsyscode=来源系统单据唯一流水号
RdocSignLockSourceReqData.locksys=锁票系统
RdocSignLockSourceReqData.lockstatus=锁定状态

#【B19】非直联收票
RdocSignNonDirtSourceReq.signdata=非直联数据
RdocSignNonDirtSourceReqData.srcsyscode=来源系统单据唯一流水号
RdocSignNonDirtSourceReqData.draftid=电票唯一标识
RdocSignNonDirtSourceReqData.rdocno=票据号
RdocSignNonDirtSourceReqData.docno=票据编号
RdocSignNonDirtSourceReqData.doctype=票据类型
RdocSignNonDirtSourceReqData.subdocfm=子票区间起
RdocSignNonDirtSourceReqData.subdocto=子票区间止
RdocSignNonDirtSourceReqData.fcode=币种
RdocSignNonDirtSourceReqData.fcy=票据金额
RdocSignNonDirtSourceReqData.isdircon=是否直联
RdocSignNonDirtSourceReqData.isfundcorp=是否财务公司
RdocSignNonDirtSourceReqData.iscantrans=是否可转让
RdocSignNonDirtSourceReqData.iscansplit=是否可拆分
RdocSignNonDirtSourceReqData.docdate=出票日期
RdocSignNonDirtSourceReqData.duedate=票据到期日
RdocSignNonDirtSourceReqData.drawbankcode=出票人银行
RdocSignNonDirtSourceReqData.drawer=出票人
RdocSignNonDirtSourceReqData.drawbankaccount=出票人账号
RdocSignNonDirtSourceReqData.endorccode=最后背书人
RdocSignNonDirtSourceReqData.accepter=承兑人
RdocSignNonDirtSourceReqData.acpbankaccount=承兑人账号
RdocSignNonDirtSourceReqData.acpbankcode=承兑人银行
RdocSignNonDirtSourceReqData.acpheadbankcode=承兑银行大行
RdocSignNonDirtSourceReqData.rdocname=收票人
RdocSignNonDirtSourceReqData.rdocbankcode=收票人银行
RdocSignNonDirtSourceReqData.rdocbankacccode=收票人账户
RdocSignNonDirtSourceReqData.rdocdate=收票日期

#【B29】非直联红冲
RecDocSignRedReq.signdata=非直联冲销数据
RecDocSignRedReqData.srcsyscode=来源系统单据唯一流水号

#【B24】托收
RdocCollSourceReq.colldata=收票到期托收信息推送信息
RdocCollSourceReqData.srcsyscode=来源系统单据唯一流水号
RdocCollSourceReqData.rdocno=票据号
RdocCollSourceReqData.docno=票据编号
RdocCollSourceReqData.subdocfm=子票区间起
RdocCollSourceReqData.subdocto=子票区间止
RdocCollSourceReqData.corpbcode=公司
RdocCollSourceReqData.colldate=托收日期
RdocCollSourceReqData.fcy=托收金额
RdocCollSourceReqData.collbankacccode=托收银行账号
RdocCollSourceReqData.bankno=网银流水号

#【B32】签收明细
RdocDistSourceReq.distdata=票据贴现结果返盘信息
RdocDistSourceReqData.rdocdistcode=贴现申请单号
RdocDistSourceReqData.rdocdisticode=贴现申请单内码
RdocDistSourceReqData.rdocdistgicode=贴现申请单子表内码
RdocDistSourceReqData.isrecourse=是否有追索权
RdocDistSourceReqData.distfcy=实际贴现金额
RdocDistSourceReqData.distpayer=贴现下款方
RdocDistSourceReqData.distbankcode=贴现银行
RdocDistSourceReqData.distbankacccode=贴现银行账户
RdocDistSourceReqData.irtpaytype=利息扣除方式
RdocDistSourceReqData.distrate=贴现利率
RdocDistSourceReqData.distinterest=贴现利息
RdocDistSourceReqData.distdate=贴现日期
RdocDistSourceReqData.payirtmode=付息方式

#【B38】质押
RdocPleSourceReq.pledata=质押信息
RdocPleSourceReqData.srcsyscode=来源系统单据唯一流水号
RdocPleSourceReqData.rdocno=票据号
RdocPleSourceReqData.plestype=操作类型
RdocPleSourceReqData.fcy=质押金额
RdocPleSourceReqData.plebankcode=质押银行
RdocPleSourceReqData.pledate=质押/解除日期











