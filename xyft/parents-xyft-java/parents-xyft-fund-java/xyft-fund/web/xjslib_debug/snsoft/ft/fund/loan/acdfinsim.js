Xjs.loadedXjs.push("snsoft/ft/fund/loan/acdfinsim");
/*snsoft/ft/loan/acdfinsim/app/invoker/AccOrdFinaSimInterestInvoker.java*/
Xjs.namespace("snsoft.ft.loan.acdfinsim.app.invoker");
snsoft.ft.loan.acdfinsim.app.invoker.AccOrdFinaSimInterestInvoker=function(parameter){
    snsoft.ft.loan.acdfinsim.app.invoker.AccOrdFinaSimInterestInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.loan.acdfinsim.app.invoker.AccOrdFinaSimInterestInvoker,snsoft.ext.cmd.com.biz.DialogInvoker,{
  _js$className_:"snsoft.ft.loan.acdfinsim.app.invoker.AccOrdFinaSimInterestInvoker",
    /*snsoft.ft.loan.acdfinsim.app.invoker.AccOrdFinaSimInterestInvoker.onOk*/
    onOk:function(table,event)
    {
        var date = Xjs.util.DateUtils.nowDay(),
            year1 = date.getFullYear(),
            month1 = date.getMonth() + 1,
            year = table.getDataSet().getValue("interestyear"),
            month = table.getDataSet().getValue("interestmonth");
        if(year1 < year || year1 == year && month1 - 1 < month)
        {
            throw Xjs.ResBundle.getResVal("FT-LOAN.00000089");
        }
        var param = {};
        param.interestyear = year;
        param.interestmonth = month;
        param.bcode = this.table.getDataSet().getValue("bcode");
        param.corpbcode = this.table.getDataSet().getValue("corpbcode");
        param.acdfaicode = this.table.getDataSet().getValue("acdfaicode");
        snsoft.ext.cmd.CommandInvokers.remoteInvoke(event.table,event.cfg,"check",param);
        snsoft.ext.cmd.CommandInvokers.remoteInvoke(event.table,event.cfg,"invoker",param);
    },
    /*snsoft.ft.loan.acdfinsim.app.invoker.AccOrdFinaSimInterestInvoker.onShow*/
    onShow:function(tbl,event)
    {
        var rows = this.table.getSelectedRowNumbers(),
            acdfaicode = this.dataSet.getValue("acdfaicode",rows[0]),
            date = Xjs.util.DateUtils.nowDay(),
            month1 = date.getMonth() + 1,
            year = date.getMonth() == 1 ? date.getFullYear() - 1 : date.getFullYear(),
            month = month1 == 1 ? 12 : month1 - 1;
        tbl.getDataSet().setValue("interestyear",year);
        tbl.getDataSet().setValue("interestmonth",month);
        tbl.getDataSet().setValue("acdfaicode",acdfaicode);
    }
});
