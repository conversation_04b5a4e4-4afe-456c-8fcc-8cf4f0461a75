Xjs.loadedXjs.push("snsoft/ft/fund/rpadj/comm");
Xjs.namespace("snsoft.ft.rpadj.comm.invoker");snsoft.ft.rpadj.comm.invoker.CheckTableFcySumInvoker=function(parameter){snsoft.ft.rpadj.comm.invoker.CheckTableFcySumInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.rpadj.comm.invoker.CheckTableFcySumInvoker,snsoft.ext.cmd.CommandInvoker,{checkFcyEquals:function(){var outFcy="curfcy";if(this.isDetail){if(!this.table||this.table.dataSet.dataStore.getData().length==0){throw Xjs.ResBundle.getResVal("FT.00000089",this.getTitle());}outFcy="fcy";}else {var rows=this.table.getSelectedRowNumbers();if(rows==null||rows.length==0){throw Xjs.ResBundle.getResVal("FT.00000048");}}var offInTable=Xjs.util.TableUtils.getTable_$Panel_$String(this.table,"ft_rpadj_offin");for(var i=0;i<offInTable._getRowCount();i++){offInTable.gotoRow(i);offInTable.checkNonBlankForSubmit();}var outFcyScale=this.table.getColumn(outFcy).maxDecimals,sumOutFcy=this.collectTableFcy(this.table,outFcy,!this.isDetail),inFcy=this.collectTableFcy(offInTable,"fcy",false),diff=snsoft.ft.utils.FTUtils.round(snsoft.ft.utils.FTUtils.sub(sumOutFcy,inFcy),outFcyScale);if(this.abs(diff,outFcyScale)>0){throw new Error(Xjs.ResBundle.getResVal("FT-RPADJ.10000007",sumOutFcy,inFcy));}},abs:function(value,scale){if(value<0)return snsoft.ft.utils.FTUtils.round(value*-1,scale);return value;},collectTableFcy:function(collectTable,fcyField,selectedFlag){var collectDataSet=collectTable.getDataSet(),collectRows=this.getRows(selectedFlag,collectTable),collectFcyScale=collectTable.getColumn(fcyField).maxDecimals,collectFcy=0;for(var i=0;i<collectRows.length;i++){var fcy=collectDataSet.getValue(fcyField,collectRows[i]);collectFcy=snsoft.ft.utils.FTUtils.add(fcy,collectFcy);}return snsoft.ft.utils.FTUtils.round(collectFcy,collectFcyScale);},getRows:function(selectedFlag,collectTable){var collectRows,collectDataSet=collectTable.getDataSet();if(selectedFlag)collectRows=collectTable.getSelectedRowNumbers();else {var rowCount=collectDataSet.getRowCount();collectRows=new Array(rowCount);for(var i=0;i<rowCount;i++){collectRows[i]=i;}}return collectRows;}});snsoft.ft.rpadj.comm.invoker.AdjRecPayOffBatchGenInvoker=function(parameter){snsoft.ft.rpadj.comm.invoker.AdjRecPayOffBatchGenInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.rpadj.comm.invoker.AdjRecPayOffBatchGenInvoker,snsoft.ft.rpadj.comm.invoker.CheckTableFcySumInvoker,{beforeCheck:function(event){this.checkFcyEquals();this.checkFcyEqualsInAndSum();return null;},check:function(event){return {type:0,title:this.getTitle(),prompt:Xjs.ResBundle.getResVal("FT-RPADJ.00000002",Xjs.ResBundle.getResVal(this.tipInfo))};},invoke:function(event){var generateMap={},rows=this.table.getSelectedRowNumbers(),main={};main.sheetcode=this.sheetcode;main.ccode=this.table.getDataSet().getValue("ccode",rows[0]);main.bcode=this.table.getDataSet().getValue("bcode",rows[0]);main.wcode=this.table.getDataSet().getValue("wcode",rows[0]);main.corpbcode=this.table.getDataSet().getValue("corpbcode",rows[0]);var rpadjtype=this.table.queryParam.getItemValue("rpadjtype");main.rpadjtype=rpadjtype;generateMap.main=main;{var outValueMap={};for(var i=0;i<rows.length;i++){var outValueInfos=[];outValueInfos.push(this.table.getDataSet().getValue("curfcy",rows[i]));outValueInfos.push(this.table.getDataSet().getValue("idx",rows[i]));outValueMap[this.table.getDataSet().getValue(this.matchCol,rows[i])]=outValueInfos;}generateMap.outInfoMap=outValueMap;}{var offInTable=Xjs.util.TableUtils.getTable_$Panel_$String(this.table,"ft_rpadj_offin"),inInfoRows=[];for(var i=0;i<offInTable.getDataSet().getRowCount();i++){var valueMap={};for(var column of offInTable.getDataSet().columns){var columnValue=offInTable.getDataSet().getValue(column.name,i);valueMap[column.name]=columnValue;}inInfoRows.push(valueMap);}generateMap.inInfoMap=inInfoRows;}{var tcaptime=null,sumTable=Xjs.util.TableUtils.getTable_$Panel_$String(this.table,"bcsumminfo"),sumInfoRows=[];for(var i=0;i<sumTable.getDataSet().getRowCount();i++){var valueMap={};for(var column of sumTable.getDataSet().columns){valueMap[column.name]=sumTable.getDataSet().getValue(column.name,i);}if(!tcaptime)tcaptime=sumTable.getDataSet().getValue("tcaptime",i);sumInfoRows.push(valueMap);}generateMap.sumInfoMap=sumInfoRows;}return generateMap;},afterInvoke:function(event){var beforeCheckRtnVal=event.invokeRtnVal,officode=beforeCheckRtnVal.officode,sheetService=Xjs.RInvoke.newBean("SN-Busi.SheetService"),busiObject=sheetService.getBusiObject(this.sheetcode),data={sheetcode:busiObject.sheetcode};data[busiObject.innerfld]=officode;snsoft.plat.bas.viewdetail.ViewDetailListener.invokeViewDetail(data,busiObject.outerfld);},checkFcyEqualsInAndSum:function(){var offInTable=Xjs.util.TableUtils.getTable_$Panel_$String(this.table,"ft_rpadj_offin"),inFcy=this.collectTableFcy(offInTable,"fcy",false),sumTable=Xjs.util.TableUtils.getTable_$Panel_$String(this.table,"bcsumminfo"),sumFcyScale=sumTable.getColumn("fcy").maxDecimals,sumFcy=this.collectTableFcy(sumTable,"fcy",false),diff=snsoft.ft.utils.FTUtils.round(snsoft.ft.utils.FTUtils.sub(sumFcy,inFcy),sumFcyScale);if(this.abs(diff,sumFcyScale)>0){throw new Error(Xjs.ResBundle.getResVal("FT-RPADJ.10000010"));}}});snsoft.ft.rpadj.comm.invoker.AdjRecPayOffBatchSelLrpInvoker=function(parameter){snsoft.ft.rpadj.comm.invoker.AdjRecPayOffBatchSelLrpInvoker.superclass.constructor.call(this,parameter);this.type=parameter.type;};Xjs.extend(snsoft.ft.rpadj.comm.invoker.AdjRecPayOffBatchSelLrpInvoker,snsoft.ext.cmd.CommandInvoker,{check:function(event){var rows=this.table.getSelectedRowNumbers();if(rows==null||rows.length==0){throw new Error(Xjs.ResBundle.getResVal("FT.00000048"));}var initParams={},refreshParams={},readonlyParams=[];initParams.corpbcode=this.dataSet.getValue("corpbcode",rows[0]);readonlyParams.push("corpbcode");initParams.ccode=this.dataSet.getValue("ccode",rows[0]);readonlyParams.push("ccode");initParams.fcode=this.dataSet.getValue("fcode",rows[0]);readonlyParams.push("fcode");initParams.fcyingflag="Y";readonlyParams.push("fcyingflag");refreshParams.sheetcode=this.dataSet.getValue("sheetcode",rows[0]);refreshParams.limitsheetcode=this.dataSet.getValue("sheetcode",rows[0]);refreshParams.lrpflag=1+4;if(this.type==snsoft.ft.rpadj.comm.invoker.AdjRecPayOffBatchSelLrpInvoker.REC_TYPE)refreshParams.ismatchorc="Y";event.checkData={initParams:initParams,readonlyParams:readonlyParams,refreshParams:refreshParams};return null;},invoke:function(event){var param=event.checkData.param,rowNums=param.selectedRows,cbDataSet=param.cbDataSet,offInTable=Xjs.util.TableUtils.getTable_$Panel_$String(this.mainTable.getRootTable(),"ft_rpadj_offin"),offInset=offInTable.getDataSet(),idx=10;for(var i=0;i<rowNums.length;i++){var lrpicodex=cbDataSet.getValue("lrpicodex",rowNums[i]),checkNum=this.checkDateByLrpicodex(lrpicodex,offInset);if(checkNum==-1){offInset.insertRow(3);for(var column of offInset.columns){if(cbDataSet.columnAt(column.name)!=-1)offInset.setValue(column.name,cbDataSet.getValue(column.name,rowNums[i]));}offInset.setValue("idx",idx);idx+=10;if(this.type==snsoft.ft.rpadj.comm.invoker.AdjRecPayOffBatchSelLrpInvoker.PAY_TYPE)offInset.setValue("fcy",cbDataSet.getValue("fcying",rowNums[i]));else offInset.setValue("fcy",cbDataSet.getValue("curfcy",rowNums[i]));offInset.setValue("cindate",Date.today());}else {offInset.gotoRow(checkNum);if(this.type==snsoft.ft.rpadj.comm.invoker.AdjRecPayOffBatchSelLrpInvoker.PAY_TYPE)offInset.setValue("fcy",snsoft.ft.utils.FTUtils.add(cbDataSet.getValue("fcying",rowNums[i]),offInset.getValue("fcy")));else offInset.setValue("fcy",snsoft.ft.utils.FTUtils.add(cbDataSet.getValue("curfcy",rowNums[i]),offInset.getValue("fcy")));}offInset.saveChanges();}return null;},checkDateByLrpicodex:function(lrpicodex,offset){var offsetCount=offset.getRowCount();for(var k=0;k<offsetCount;k++){var lrpicodexTarget=offset.getValue("lrpicodex",k);if(lrpicodexTarget==lrpicodex)return k;}return -1;}});Xjs.apply(snsoft.ft.rpadj.comm.invoker.AdjRecPayOffBatchSelLrpInvoker,{PAY_TYPE:"AdjPayOffBatch",REC_TYPE:"AdjRecOffBatch"});snsoft.ft.rpadj.comm.invoker.AdjRecPayOffDetailAutoMatchInvoker=function(parameter){snsoft.ft.rpadj.comm.invoker.AdjRecPayOffDetailAutoMatchInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.rpadj.comm.invoker.AdjRecPayOffDetailAutoMatchInvoker,snsoft.ft.rpadj.comm.invoker.CheckTableFcySumInvoker,{beforeCheck:function(event){this.checkFcyEquals();return null;},afterInvoke:function(event){var sumTable=Xjs.util.TableUtils.getTable_$Panel_$String(event.table,"ft_rpadj_offgs");if(sumTable)sumTable.refreshTable();}});snsoft.ft.rpadj.comm.invoker.AdjRecPayOffEditCcodeInvoker=function(parameter){snsoft.ft.rpadj.comm.invoker.AdjRecPayOffEditCcodeInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.rpadj.comm.invoker.AdjRecPayOffEditCcodeInvoker,snsoft.ext.cmd.com.biz.DialogInvokerModifyTableMain,{check:function(event){var gTable=Xjs.util.TableUtils.getTable_$Panel_$String(this.mainTable,"ft_rpadj_offg");if(gTable&&gTable.dataSet.dataStore.getData().length>0)return {type:0,title:this.getTitle(),prompt:Xjs.ResBundle.getResVal("FT-RPADJ.10000009",event.table.getColumn("ccode").title)};return null;},onOk:function(tbl,event){if(this.isTableChanged(tbl,event.table)){var editCcodeParams=this.buildParams(tbl,event);this.fireOnOk(editCcodeParams);this.doSave(tbl,event);}},fireOnOk:function(params){var getValueStr=(ds,cols)=>{var vals=cols.split(",").map((col)=>{return ds.getValue(col);});return vals.join("|");},cols="bcode,wcode,corpbcode,ccode,rpadjtype",judgeFieldValue=getValueStr(params.tblDataSet,cols),bJudgeFieldValue=getValueStr(params.eventDataSet,cols);if(!(judgeFieldValue==bJudgeFieldValue)){if(params.callOutDataSet.getRowCount()!=0){params.callInDataSet.deleteAllRows();params.callOutDataSet.deleteAllRows();this.mainTable.saveChanges();this.mainTable.refreshTable();}}},doSave:function(tbl,event){var sp=this.buildSaveMainData(tbl);snsoft.ft.rpadj.comm.invoker.AdjRecPayOffEditCcodeInvoker.superclass.onOkSaveParam.call(this,sp,event);},buildParams:function(tbl,event){var params={};params.tbl=tbl;params.event=event;params.tblDataSet=tbl.getDataSet();params.eventDataSet=event.table.getDataSet();params.callOutDataSet=Xjs.util.TableUtils.getTable_$Panel_$String(this.mainTable,"ft_rpadj_offg").dataSet;params.callInDataSet=Xjs.util.TableUtils.getTable_$Panel_$String(this.mainTable,"ft_rpadj_offgs").dataSet;return params;}});snsoft.ft.rpadj.comm.invoker.AdjRecPayOffNewSheet=function(parameter){snsoft.ft.rpadj.comm.invoker.AdjRecPayOffNewSheet.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.rpadj.comm.invoker.AdjRecPayOffNewSheet,snsoft.plat.bas.sheet.cmd.sheet.CommandNewSheet,{invoke:function(event){var busiObject;if(this.sheetcode){var serv=Xjs.RInvoke.newBean("SN-Busi.SheetService");busiObject=serv.getBusiObject(this.sheetcode);}else busiObject=this.getBusiObject(this.mainDataSet);this.newSheet(event,this.table,busiObject);return null;}});snsoft.ft.rpadj.comm.invoker.AdjRecPayOffWorkBenchAutoMatchInvoker=function(parameter){snsoft.ft.rpadj.comm.invoker.AdjRecPayOffWorkBenchAutoMatchInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.rpadj.comm.invoker.AdjRecPayOffWorkBenchAutoMatchInvoker,snsoft.ft.rpadj.comm.invoker.CheckTableFcySumInvoker,{bcsumminfoTableName:"bcsumminfo",refSheetCodeColumn:"sheetcode",beforeCheck:function(event){this.checkFcyEquals();return null;},check:function(event){return {type:0,title:this.getTitle(),prompt:Xjs.ResBundle.getResVal("C001",event.cfg.title)};},invoke:function(event){var bcsumminfo=Xjs.util.TableUtils.getTable_$Panel_$String(this.table.getRootTable(),this.bcsumminfoTableName),bcsumminfoDataSet=bcsumminfo.getDataSet();bcsumminfoDataSet.refresh();var outFcy=this.isDetail?"fcy":"curfcy";if(bcsumminfo.getDataSet()!=null||bcsumminfo.getDataSet().getRowCount()>0)bcsumminfoDataSet.deleteAllRows();var residueOutFcy=0,residueInFcy=0,offOutRowIndex=0,offInTable=Xjs.util.TableUtils.getTable_$Panel_$String(this.table.getRootTable(),"ft_rpadj_offin"),offInRowCount=offInTable.getDataSet().getRowCount(),offInDataSet=offInTable.getDataSet(),rows=this.getRows(!this.isDetail,this.table),inFcyScale=offInTable.getColumn("fcy").maxDecimals,outFcyScale=this.table.getColumn(outFcy).maxDecimals;for(var row=0;row<offInRowCount;row++){residueInFcy=offInDataSet.getValue("fcy",row);while(residueInFcy>0){var offgicode=this.isDetail?this.dataSet.getValue("offgicode",rows[offOutRowIndex]):"";if(residueOutFcy<=0)residueOutFcy=this.dataSet.getValue(outFcy,rows[offOutRowIndex]);if(residueInFcy>=residueOutFcy){this.copy(offInDataSet,bcsumminfoDataSet,row);this.insertBcSumminRow(bcsumminfoDataSet,rows[offOutRowIndex],residueOutFcy,offgicode);residueInFcy=snsoft.ft.utils.FTUtils.round(snsoft.ft.utils.FTUtils.sub(residueInFcy,residueOutFcy),inFcyScale);residueOutFcy=0;offOutRowIndex++;}else if(residueInFcy<residueOutFcy){this.copy(offInDataSet,bcsumminfoDataSet,row);this.insertBcSumminRow(bcsumminfoDataSet,rows[offOutRowIndex],residueInFcy,offgicode);residueOutFcy=snsoft.ft.utils.FTUtils.round(snsoft.ft.utils.FTUtils.sub(residueOutFcy,residueInFcy),outFcyScale);residueInFcy=0;}}}this.table.saveChanges();if(this.rptypeCtrlTblName){var invokeParam=this.buildInvokeParam(null);invokeParam.outInvokeParams=this.getOutInvokeParams();invokeParam.rptypeReqParams=this.buildRptypeReqParams(bcsumminfoDataSet);invokeParam.sumTableInfos=this.getSumTableInfos();return invokeParam;}return null;},getOutInvokeParams:function(){var outInvokeParams=[];if(this.outInvokeCols){var rows=this.table.getSelectedRowNumbers();for(var i=0;i<rows.length;i++){var cols=this.outInvokeCols.split(","),p={};for(var j=0;j<cols.length;j++){var col=cols[j],fcol=col,tcol=col,idx=col.indexOf("=");if(idx>0){tcol=col.substring(0,idx);fcol=col.substring(idx+1);}var v=this.table.dataSet.getValue(fcol,rows[i]);p[tcol]=v;}outInvokeParams.push(p);}}return outInvokeParams;},getSumTableInfos:function(){var bcsumminfo=Xjs.util.TableUtils.getTable_$Panel_$String(this.table.getRootTable(),this.bcsumminfoTableName),bcsumminfoDataSet=bcsumminfo.getDataSet(),datas=[],rowCount=bcsumminfoDataSet.getRowCount();for(var row=0;row<rowCount;row++){var data={},columns=bcsumminfo.getColumns();for(var i=0;i<columns.length;i++){var value=bcsumminfoDataSet.getValue(columns[i].name,row);if(value)data[columns[i].name]=value;}datas.push(data);}return datas;},buildRptypeReqParams:function(dataSet){var params=[],refSheetCodeTble=Xjs.util.TableUtils.getTable_$Panel_$String(dataSet.getTables()[0],this.refSheetCodeTblName),rowCount=dataSet.getRowCount();for(var i=0;i<rowCount;i++){var p={};p.sheetcode=refSheetCodeTble.dataSet.getValue(this.refSheetCodeColumn);p.rptype=dataSet.getValue("rptype",i);p.cfmode=dataSet.getValue("cfmode",i);params.push(p);}return params;},copy:function(fm,to,row){to.insertRow(3);var columns=to.columns;for(var i=0;i<columns.length;i++){var dc=columns[i],idx=fm.columnAt(dc.name);if(idx<0)continue;var value=fm.getValue(dc.name,row);to.setValue(dc.name,value);}},insertBcSumminRow:function(bcsumminfoDataSet,outRow,fcy,offgicode){bcsumminfoDataSet.setValue("outidx",this.dataSet.getValue("idx",outRow));bcsumminfoDataSet.setValue("tcaptime",Xjs.util.DateUtils.nowDay());if(this.extCopyOutCols){var cols=this.extCopyOutCols.split(",");for(var i=0;i<cols.length;i++){bcsumminfoDataSet.setValue(cols[i],this.dataSet.getValue(cols[i],outRow));}}bcsumminfoDataSet.setValue("fcy",fcy);if(this.isDetail)bcsumminfoDataSet.setValue("offgicode",offgicode);bcsumminfoDataSet.getTables()[0].render(1+2048);},afterInvoke:function(event){if(event.invokeRtnVal){var disHiddenCols=event.invokeRtnVal.disHiddenCols;if(disHiddenCols){var rptypeCtrlTable=Xjs.util.TableUtils.getTable_$Panel_$String(event.table,this.rptypeCtrlTblName);if(rptypeCtrlTable){var listener=rptypeCtrlTable.getListener(snsoft.ft.lcfg.comm.LrpRptypeDlgCtrlJSListener);if(listener)listener.setColOptions(event.table.dataSet,disHiddenCols);}}var subGDetailDatas=event.invokeRtnVal.subGDetailDatas;if(subGDetailDatas){var bcsumminfo=Xjs.util.TableUtils.getTable_$Panel_$String(this.table.getRootTable(),this.bcsumminfoTableName),bcsumminfoDataSet=bcsumminfo.getDataSet(),rowCount=bcsumminfoDataSet.getRowCount();for(var row=0;row<rowCount;row++){bcsumminfoDataSet.gotoRow(row);var subDataMap=subGDetailDatas[row];for(var name in subDataMap){if(bcsumminfoDataSet.columnAt(name)>-1)bcsumminfoDataSet.setValue(name,subDataMap[name]);}}}}}});snsoft.ft.rpadj.comm.invoker.AdjvSelLrpCorInvoker$DialogTableRefreshListener=function(dlogTable,params){this.dlogTable=dlogTable;this.refreshParams=params;};Xjs.extend(snsoft.ft.rpadj.comm.invoker.AdjvSelLrpCorInvoker$DialogTableRefreshListener,Xjs.table.DefaultListener,{dataSetRefreshing:function(dataSet,e){if(!e.refreshParam)e.refreshParam={};Xjs.apply(e.refreshParam,this.refreshParams);}});snsoft.ft.rpadj.comm.invoker.AdjvSelLrpCorInvoker=function(parameter){snsoft.ft.rpadj.comm.invoker.AdjvSelLrpCorInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.rpadj.comm.invoker.AdjvSelLrpCorInvoker,snsoft.ext.cmd.CommandInvoker,{virtualTable:false,check:function(event){this.initParams=event.checkData.initParams;this.readonlyParams=event.checkData.readonlyParams;this.refreshParams=event.checkData.refreshParams;return this.newDialogWithFunc(event,(e)=>{var dlgcfg={_UI_InitParam:this.initParams},dialogPane=Xjs.ui.UIUtil.loadDialog(this.muiid,0,null,null,Xjs.apply({},dlgcfg));dialogPane.setWidth("auto");dialogPane.setHeight("80%");dialogPane.title=event.cfg.title;var dlgtbl=Xjs.util.TableUtils.getAllTablesFromComponent(dialogPane,false)[0];dialogPane.beforeShowing=this.resetBeforeShowing.createDelegate(this,[dialogPane,dlgtbl],true);dialogPane.addListener("onShowing",new Xjs.FuncCall(this.resetOnShowing,this,[dlgtbl]));if(!dlgtbl.getListener(snsoft.ft.rpadj.comm.invoker.AdjvSelLrpCorInvoker$DialogTableRefreshListener)){var reflistener=new snsoft.ft.rpadj.comm.invoker.AdjvSelLrpCorInvoker$DialogTableRefreshListener(dlgtbl,this.refreshParams);dlgtbl.addListener(reflistener);}var queryPane=dlgtbl.queryParam;for(var name in this.initParams){queryPane.getItemByName(name).setValue(this.initParams[name]);}for(var i=0;i<this.readonlyParams.length;i++){queryPane.getItemByName(this.readonlyParams[i]).setReadonly(true);}dialogPane.showModal();return dialogPane;},(e,d)=>{var srcTable=snsoft.ft.utils.FTUtils.getTableFromPanel(d),dialgRows=srcTable.getSelectedRowNumbers(),cbDataSet=srcTable.getDataSet();cbDataSet.postRow();var param={},lrpicodexFcyMap={},selectedRows=[];for(var i=0;i<dialgRows.length;i++){var lrpicodex=String.obj2str(cbDataSet.getValue("lrpicodex",dialgRows[i]),null);if(!lrpicodex)continue;selectedRows.push(dialgRows[i]);if(cbDataSet.columnAt(this.fcyCol)>=0)lrpicodexFcyMap[lrpicodex]=cbDataSet.getValue(this.fcyCol,dialgRows[i]);}if(this.virtualTable){param.cbDataSet=cbDataSet;param.selectedRows=selectedRows;}else {param.sheetcode=this.mainDataSet.getValue("sheetcode");param.modifydate=this.mainDataSet.getValue("modifydate");param.lrpicodexFcyMap=lrpicodexFcyMap;param.offgicode=Xjs.util.TableUtils.getTable_$Panel_$String(this.mainTable,"ft_rpadj_offg").getValue("offgicode");param.officode=this.mainTable.getValue("officode");param.isBatchAdj=this.isBatchAdj;}event.checkData.param=param;});},resetBeforeShowing:function(dlg,dlgTable){snsoft.ft.utils.FTUtils.hiddenPaneCollapseBtns(dlgTable.queryParam);snsoft.ft.utils.FTUtils.hiddenSaveLoadValueBtns(dlgTable.queryParam);snsoft.ft.utils.FTUtils.setDialogToolVisiable(dlg,this.obj2bool(this.isToolHidden,true));snsoft.ft.utils.FTUtils.setBtnVisible(dlgTable,this.toolHidCommands,false);},obj2bool:function(value,dft){if(dft===undefined)dft=false;return value==null||value===undefined?dft:Xjs.Data.parseFromSqlType(value,-7);},invoke:function(event){if(this.virtualTable)return null;return event.checkData.param;},afterInvoke:function(event){this.dataSet.refresh();if(this.refreshSumTbl){var collectTable=Xjs.util.TableUtils.getTable_$Panel_$String(this.mainTable,"ft_rpadj_offgs_collect");collectTable.dataSet.refresh();}},resetOnShowing:function(dlg,dlgtbl){snsoft.ft.utils.FTUtils.resetBackupVal(dlg,dlgtbl);if(this.sheetCode)snsoft.ft.utils.FTUtils.addAidsSheetfilter(dlgtbl,this.sheetCode,this.limitcols);dlgtbl.refreshTableIfOK();if(dlg&&dlg.dom&&Xjs.DOM.findById("PaneCollapseBtn2",dlg.dom))snsoft.ft.utils.FTUtils.setWidthfmOffsetWidth(dlg);}});snsoft.ft.rpadj.comm.invoker.SheetDataCopyCustomRdonlyInvoker=function(params){snsoft.ft.rpadj.comm.invoker.SheetDataCopyCustomRdonlyInvoker.superclass.constructor.call(this,params);};Xjs.extend(snsoft.ft.rpadj.comm.invoker.SheetDataCopyCustomRdonlyInvoker,snsoft.ft.comm.cmdreg.SheetDataCopyInvoker,{setInitParasVales:function(table,dialog,sheetparams){;var params=this.getInitParasVales(table,dialog,sheetparams.cfgParams),rdcols=sheetparams.extRdonlyCols?sheetparams.extRdonlyCols.split(","):[];if(rdcols.length>0||!snsoft.ft.comm.cmdreg.SheetDataCopyInvoker.isEmpty(params)){var _findfunc=new Xjs.FuncCall(this.findInitValInput,this),inputs=dialog.findComps(_findfunc,false);if(inputs.length==0)return;for(var i=0,len=inputs.length;i<len;i++){if(!(inputs[i] instanceof Xjs.ui.Component))continue;var item=inputs[i];if(item.name in params)this.setDlgItemAttrVal(item,params[item.name],true,this.rdonlyQueryParams===undefined?false:String.likeOneOf(item.name,this.rdonlyQueryParams));if(rdcols.indexOf(item.name)>-1)this.setDlgItemAttrVal(item,null,false,true);}}},findInitValInput:function(comp){return comp instanceof Xjs.ui.InputField||comp instanceof Xjs.ui.CheckboxGroup;}});Xjs.namespace("snsoft.ft.rpadj.comm.lis");snsoft.ft.rpadj.comm.lis.AdjRecPayOffGDetailJSListener=function(params){snsoft.ft.rpadj.comm.lis.AdjRecPayOffGDetailJSListener.superclass.constructor.call(this,params);};Xjs.extend(snsoft.ft.rpadj.comm.lis.AdjRecPayOffGDetailJSListener,snsoft.plat.bas.busi.SystemFunctionListener,{callInTableName:"ft_rpadj_offgs",initComponent:function(table,values){snsoft.ft.rpadj.comm.lis.AdjRecPayOffGDetailJSListener.superclass.initComponent.call(this,table,values);this.callOutTable=this.getTable(table.getRootTable(),"ft_rpadj_offg");this.callInTable=this.getTable(table.getRootTable(),this.callInTableName);if(this.sumTableName)this.sumTable=this.getTable(table.getRootTable(),this.sumTableName);},itemAidInputing:function(table,e){snsoft.ft.rpadj.comm.lis.AdjRecPayOffGDetailJSListener.superclass.itemAidInputing.call(this,table,e);if(this.callInTable==table&&e.forTblColumn.name=="rptype"){var rptypeCodeData=e.forTblColumn.selectOptions;rptypeCodeData.loadParameter.sheetcode=this.callOutTable.getDataSet().getValue("srcsheetcode");}},dataSetSaved:function(dataSet,e){snsoft.ft.rpadj.comm.lis.AdjRecPayOffGDetailJSListener.superclass.dataSetSaved.call(this,dataSet,e);if(this.sumTable)this.sumTable.refreshTable();}});snsoft.ft.rpadj.comm.lis.AdjRecPayOffWorkBatchJSListener=function(params){snsoft.ft.rpadj.comm.lis.AdjRecPayOffWorkBatchJSListener.superclass.constructor.call(this,params);};Xjs.extend(snsoft.ft.rpadj.comm.lis.AdjRecPayOffWorkBatchJSListener,snsoft.plat.bas.busi.SystemFunctionListener,{initComponent:function(table,values){if(this.table==null)this.table=table;},dataSetFieldPosting:function(dataSet,event){if(event.columnName==this.colName)this.checkInputFcy(dataSet,event);if(this.isCheckDate){if(event.columnName=="cindate")this.checkCInDateEffective(event);}},dataSetFieldPosted:function(dataSet,event){if(this.isSetValue&&event.columnName==this.colName){var value=dataSet.getValue(this.colName);if(value==null)dataSet.setRowSelected(dataSet.getRow(),this.table._getRowSelid(),false);else if(!this.lock)dataSet.setRowSelected(dataSet.getRow(),this.table._getRowSelid(),true);}},dataSetRowSelected:function(dataSet,e){if(this.isSetValue){dataSet.gotoRow(e.row);if(e.selected)this.autoSetLeftValue(dataSet,e.row);else dataSet.setValue(this.colName,null);dataSet.postRow();this.table.setSelectedColumn(0,true);}},checkCInDateEffective:function(event){var callOutTable=this.getTable(this.table,"ft_pay_fpsg_cb_view"),cindate=event.value,rows=callOutTable.getSelectedRowNumbers(1);for(var i=0;i<rows.length;i++){var paydate=callOutTable.getDataSet().getValue("paydate",i);paydate=new Date(paydate.getFullYear(),paydate.getMonth(),paydate.getDate());cindate=new Date(cindate.getFullYear(),cindate.getMonth(),cindate.getDate());var diff=(cindate.getTime()-paydate.getTime())/(24*60*60*1000);if(diff<0){throw new Error(Xjs.ResBundle.getResVal("FT-RPADJ.10000008"));}}},checkInputFcy:function(dataSet,event){var title=this.table.getColumn(this.colName).title,scale=this.table.getColumn(this.refColName).maxDecimals,refValue=this.round(this.nullAsZero(dataSet.getValue(this.refColName)),scale),value=this.round(this.nullAsZero(event.value),scale);if(refValue==0)return;if(event.value!=null){if(refValue<0&&value>=0){var propmt=Xjs.ResBundle.getRes("UI").get("range.neg");throw Xjs.table.Table.newInputErr(this.table.getColumn(this.colName),value,propmt);}if(refValue>0&&value<=0){var propmt=Xjs.ResBundle.getRes("UI").get("range.pos");throw Xjs.table.Table.newInputErr(this.table.getColumn(this.colName),value,propmt);}}if(this.abs(value,scale)>this.abs(refValue,scale)){var refTitle=this.table.getColumn(this.refColName).title,errMsg=Xjs.ResBundle.getResVal("FT.00000263",title,refTitle);throw errMsg;}},nullAsZero:function(value){return value==null?0:value;},abs:function(value,scale){if(value<0)return this.round(value*-1,scale);return value;},autoSetLeftValue:function(dataSet,row){var tgtValue=dataSet.getValue(this.colName);if(tgtValue==null)try{this.lock=true;var leftValue=this.getLeftValue(dataSet);dataSet.setValue(this.colName,leftValue);}finally{this.lock=false;}},getLeftValue:function(dataSet){var newValue=this.nullAsZero(dataSet.getValue(this.refColName));return newValue;},dataLoaded:function(dataSet,e){snsoft.ft.rpadj.comm.lis.AdjRecPayOffWorkBatchJSListener.superclass.dataLoaded.call(this,dataSet,e);this.refreshOrClearTable();},refreshOrClearTable:function(){if(this.clearDsTblNames){for(var tblName of this.clearDsTblNames.split(",")){var clearDsTbl=Xjs.util.TableUtils.getTable_$Panel_$String(this.table,tblName);if(clearDsTbl)clearDsTbl.dataSet.refresh();}}}});snsoft.ft.rpadj.comm.lis.AdjRecPayOffWorkBenchCallInJSListener=function(params){snsoft.ft.rpadj.comm.lis.AdjRecPayOffWorkBenchCallInJSListener.superclass.constructor.call(this,params);};Xjs.extend(snsoft.ft.rpadj.comm.lis.AdjRecPayOffWorkBenchCallInJSListener,snsoft.plat.bas.busi.SystemFunctionListener,{initComponent:function(table,values){snsoft.ft.rpadj.comm.lis.AdjRecPayOffWorkBenchCallInJSListener.superclass.initComponent.call(this,table,values);this.callInTable=table;this.callInDataSet=table.getDataSet();this.callOutTable=Xjs.util.TableUtils.getTable_$Panel_$String(table,this.callOutTableName);this.callOutDataSet=this.callOutTable.getDataSet();},dataSetFieldPosted:function(dataSet,event){snsoft.ft.rpadj.comm.lis.AdjRecPayOffWorkBenchCallInJSListener.superclass.dataSetFieldPosted.call(this,dataSet,event);if(event.columnName=="rptype"||event.columnName=="cfmode"){dataSet.setValue("fcode",this.callOutDataSet.getValue("fcode"));dataSet.setValue("ccode",this.callOutDataSet.getValue("ccode"));dataSet.setValue("corpbcode",this.callOutDataSet.getValue("corpbcode"));}}});snsoft.ft.rpadj.comm.lis.RecPayColSheetcodeListener=function(params){snsoft.ft.rpadj.comm.lis.RecPayColSheetcodeListener.superclass.constructor.call(this,params);};Xjs.extend(snsoft.ft.rpadj.comm.lis.RecPayColSheetcodeListener,snsoft.plat.bas.busi.SystemFunctionListener,{callOutTableName:"ft_rpadj_offg",callInTableName:"ft_rpadj_offgs",callOutSheetcodeColumn:"srcsheetcode",initComponent:function(table,values){snsoft.ft.rpadj.comm.lis.RecPayColSheetcodeListener.superclass.initComponent.call(this,table,values);this.callOutTable=this.getTable(table.getRootTable(),this.callOutTableName);this.callOutDataSet=this.callOutTable.getDataSet();this.callInTable=this.getTable(table.getRootTable(),this.callInTableName);this.callInDataSet=this.callInTable.getDataSet();},dataSetRefreshing:function(dataSet,e){snsoft.ft.rpadj.comm.lis.RecPayColSheetcodeListener.superclass.dataSetRefreshing.call(this,dataSet,e);e.refreshParam.srcsheetcode=this.callOutDataSet.getValue(this.callOutSheetcodeColumn);},itemAidInputing:function(table,e){snsoft.ft.rpadj.comm.lis.RecPayColSheetcodeListener.superclass.itemAidInputing.call(this,table,e);if("rptype"==e.forTblColumn.name){var tc=table.getColumn("rptype"),codeData=tc.selectOptions;codeData.setLoadParameter("sheetcode",this.callOutDataSet.getValue(this.callOutSheetcodeColumn));codeData.clearBuffered();}}});snsoft.ft.rpadj.comm.lis.RecPayOffRptypeCtrlJSListener=function(params){snsoft.ft.rpadj.comm.lis.RecPayOffRptypeCtrlJSListener.superclass.constructor.call(this,params);this.replaceTableName=params.replaceTableName;this.removeFldName=params.removeFldName;};Xjs.extend(snsoft.ft.rpadj.comm.lis.RecPayOffRptypeCtrlJSListener,snsoft.ft.lcfg.comm.LrpRptypeCtrlJSListener,{replaceTableName:"",removeFldName:"",aidinputParams:function(desc){var params=snsoft.ft.rpadj.comm.lis.RecPayOffRptypeCtrlJSListener.superclass.aidinputParams.call(this,desc);this.dealOffgsAidinputParams(params);return params;},dealOffgsAidinputParams:function(params){if(this.replaceTableName){var dlgParam=params.dlgParam;if(dlgParam){for(var key in dlgParam){var fldParam=dlgParam[key];if(fldParam.indexOf(".")>-1){var fldname=fldParam.split(".");dlgParam[key]=this.replaceTableName+"."+fldname[1];}}}}if(this.removeFldName){var removeCopyMap=params.removeCopyMap;if(removeCopyMap){for(var i=0;i<removeCopyMap.length;i++){if(String.isStrIn(this.removeFldName,removeCopyMap[i],","))delete removeCopyMap[i];}}}}});