Xjs.loadedXjs.push("snsoft/ft/fund/loan/acdfin");
Xjs.namespace("snsoft.ft.loan.acdfin.app.invoker");snsoft.ft.loan.acdfin.app.invoker.AccOrdFinaAppLIRedVoucherInvoker=function(parameter){snsoft.ft.loan.acdfin.app.invoker.AccOrdFinaAppLIRedVoucherInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.loan.acdfin.app.invoker.AccOrdFinaAppLIRedVoucherInvoker,snsoft.ext.cmd.CommandInvoker,{invoke:function(event){var rows=this.mainTable.getSelectedRowNumbers();if(rows==null||rows.length==0){throw Xjs.ResBundle.getResVal("FT.00000048");}for(var i=0;i<rows.length;i++){var redflag=this.dataSet.getValue("redflag",rows[i]);if(redflag!=0){throw Xjs.ResBundle.getResVal("SNA-ACC.00000087");}var vidflag=this.dataSet.getValue("vidflag",rows[i]);if(vidflag!=1){throw Xjs.ResBundle.getResVal("SNA-ACC.00000096");}}var vmarkicodes=Xjs.util.DataSetUtils.getColumnStrValues_DataSet_int(this.mainDataSet,"vmarkicode",rows),innercodes=Xjs.util.DataSetUtils.getColumnStrValues_DataSet_int(this.mainDataSet,"acdfaliicode",rows);return {innercodes:innercodes};},afterInvoke:function(event){this.table.refreshTable();}});snsoft.ft.loan.acdfin.app.invoker.AccOrdFinaAppRIRedVoucherInvoker=function(parameter){snsoft.ft.loan.acdfin.app.invoker.AccOrdFinaAppRIRedVoucherInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.loan.acdfin.app.invoker.AccOrdFinaAppRIRedVoucherInvoker,snsoft.ext.cmd.CommandInvoker,{invoke:function(event){var rows=this.mainTable.getSelectedRowNumbers();if(rows==null||rows.length==0){throw Xjs.ResBundle.getResVal("FT.00000048");}for(var i=0;i<rows.length;i++){var vidflag=this.dataSet.getValue("vidflag",rows[i]);if(vidflag!=1){throw Xjs.ResBundle.getResVal("FT-LOAN.00000050");}}var innercodes=Xjs.util.DataSetUtils.getColumnStrValues_DataSet_int(this.mainDataSet,"acdfariicode",rows);return {innercodes:innercodes};},afterInvoke:function(event){this.table.refreshTable();}});snsoft.ft.loan.acdfin.app.invoker.AccOrdFinaAppSendBackInvoker=function(parameter){snsoft.ft.loan.acdfin.app.invoker.AccOrdFinaAppSendBackInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.loan.acdfin.app.invoker.AccOrdFinaAppSendBackInvoker,snsoft.ext.cmd.CommandInvoker,{beforeInvoke:function(event){var complstatus=this.mainDataSet.getValue("complstatus");if(complstatus=="30"){var lendInfoTable=Xjs.util.TableUtils.getTable_$Panel_$String(this.mainTable.getRootTable(),"ft_loan_acdfali"),lendInfoDataSet=lendInfoTable.getDataSet();if(lendInfoDataSet.getRowCount()!=0){throw new Error(Xjs.ResBundle.getResVal("FT-LOAN.00000004"));}}return {innercode:this.mainDataSet.get("acdfaicode"),sheetcode:this.mainDataSet.get("sheetcode")};},invoke:function(event){var returnCode=event.beforeInvokeRtnVal.returnCode;if(returnCode=="E"){throw {dummy:true};}return null;}});snsoft.ft.loan.acdfin.app.invoker.AccOrdFinaExtendAppSendBackInvoker=function(parameter){snsoft.ft.loan.acdfin.app.invoker.AccOrdFinaExtendAppSendBackInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.loan.acdfin.app.invoker.AccOrdFinaExtendAppSendBackInvoker,snsoft.ext.cmd.CommandInvoker,{beforeCheck:function(event){;return {innercode:this.mainDataSet.get("acdfaeicode"),sheetcode:this.mainDataSet.get("sheetcode"),acdfaicode:this.mainDataSet.get("acdfaicode")};},check:function(event){this.table.refreshTable();return null;}});Xjs.namespace("snsoft.ft.loan.acdfin.app.lis");snsoft.ft.loan.acdfin.app.lis.AccOrdFinaAppJSListener=function(params){snsoft.ft.loan.acdfin.app.lis.AccOrdFinaAppJSListener.superclass.constructor.call(this,params);};Xjs.extend(snsoft.ft.loan.acdfin.app.lis.AccOrdFinaAppJSListener,snsoft.plat.bas.busi.SystemFunctionListener,{});Xjs.namespace("snsoft.ft.loan.acdfin.back.invoker");snsoft.ft.loan.acdfin.back.invoker.AccOrdFinaBackChangeIsreferableInvoker=function(parameter){snsoft.ft.loan.acdfin.back.invoker.AccOrdFinaBackChangeIsreferableInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.loan.acdfin.back.invoker.AccOrdFinaBackChangeIsreferableInvoker,snsoft.ext.cmd.CommandInvoker,{invoke:function(event){var isreferable=this.dataSet.getValue("isreferable");if("N"==isreferable)this.mainTable.checkNonBlankForSubmit();return this.buildInvokeParam(null);},afterInvoke:function(event){this.mainTable.refreshTableIfOK();}});snsoft.ft.loan.acdfin.back.invoker.AccOrdFinaBackFundSrcDeleteInvoker=function(parameter){snsoft.ft.loan.acdfin.back.invoker.AccOrdFinaBackFundSrcDeleteInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.loan.acdfin.back.invoker.AccOrdFinaBackFundSrcDeleteInvoker,snsoft.ext.cmd.CommandInvoker,{beforeCheck:function(event){if(this.dataSet.getValue("srcismanual")=="N"&&this.dataSet.getValue("srccode")){throw Xjs.ResBundle.getResVal("FT-LOAN.00000040");}return null;}});snsoft.ft.loan.acdfin.back.invoker.AccOrdFinaBackSendSerCancelInvoker=function(parameter){snsoft.ft.loan.acdfin.back.invoker.AccOrdFinaBackSendSerCancelInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.loan.acdfin.back.invoker.AccOrdFinaBackSendSerCancelInvoker,snsoft.plat.bas.sheet.cmd.CommandSheet,{beforeInvoke:function(event){var innercode=this.getInnercode();if(innercode==null)return null;var parameter=this.buildSheetRecordParameter();return parameter;},invoke:function(event){var returnCode=event.beforeInvokeRtnVal.returnCode;if(returnCode=="E"){throw {dummy:true};}return null;}});snsoft.ft.loan.acdfin.back.invoker.DeleteAccOrdFinaBackAppInvoker=function(parameter){snsoft.ft.loan.acdfin.back.invoker.DeleteAccOrdFinaBackAppInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.loan.acdfin.back.invoker.DeleteAccOrdFinaBackAppInvoker,snsoft.ext.cmd.com.biz.CommandDelete,{beforeInvoke:function(event){var ds=this.mainDataSet;return ds.getKeyValues();}});Xjs.namespace("snsoft.ft.loan.acdfin.back.lis");snsoft.ft.loan.acdfin.back.lis.AccOrdFinaBackControlJSListener=function(params){snsoft.ft.loan.acdfin.back.lis.AccOrdFinaBackControlJSListener.superclass.constructor.call(this,params);};Xjs.extend(snsoft.ft.loan.acdfin.back.lis.AccOrdFinaBackControlJSListener,snsoft.plat.bas.busi.SystemFunctionListener,{initComponent:function(table,values){snsoft.ft.loan.acdfin.back.lis.AccOrdFinaBackControlJSListener.superclass.initComponent.call(this,table,values);this.mainTable=this.getTable(table,"ft_loan_acdfar");this.mainDataSet=this.mainTable.getDataSet();this.acdfarsrcTable=this.getTable(table,"ft_fund_fsrc");this.acdfarsrcDataSet=this.acdfarsrcTable.getDataSet();},dataSetFieldPosted:function(dataSet,event){this.controlSrc(event);},controlSrc:function(event){if(String.isStrIn("fcode,sfcode",event.columnName)){var fcode=this.mainDataSet.getValue("fcode"),sfcode=this.mainDataSet.getValue("sfcode");if(fcode==sfcode)this.acdfarsrcDataSet.deleteAllRows();}}});Xjs.namespace("snsoft.ft.loan.acdfin.extend.lis");snsoft.ft.loan.acdfin.extend.lis.AccOrdFinaExtendAppJSListener=function(params){snsoft.ft.loan.acdfin.extend.lis.AccOrdFinaExtendAppJSListener.superclass.constructor.call(this,params);};Xjs.extend(snsoft.ft.loan.acdfin.extend.lis.AccOrdFinaExtendAppJSListener,snsoft.plat.bas.busi.SystemFunctionListener,{initComponent:function(table,values){snsoft.ft.loan.acdfin.extend.lis.AccOrdFinaExtendAppJSListener.superclass.initComponent.call(this,table,values);this.mainTable=this.getTable(table,"ft_loan_acdfae_bas");this.mainDataSet=this.mainTable.getDataSet();},dataSetFieldPosted:function(dataSet,event){;snsoft.ft.loan.acdfin.extend.lis.AccOrdFinaExtendAppJSListener.superclass.dataSetFieldPosted.call(this,dataSet,event);if(event.columnName=="exdays"){var duedate=this.mainDataSet.getValue("duedate"),exdays=this.mainDataSet.getValue("exdays"),exdate=Xjs.util.DateUtils.incDate(duedate,exdays);this.mainDataSet.setValue("exduedate",exdate);}}});