# 语法一：使用 while 循环（更清晰易读）
git cherry origin/phase2 origin/master | grep '^+' | awk '{print $2}' | while read sha; do
  # 检查当前 commit 的作者是否是 "池启苹"
  if git show -s --format='%an' $sha | grep -q "池启苹"; then
    # 如果是，则按你的格式要求输出 log
    git log -1 --date=format:'%Y-%m-%d %H:%M:%S' --pretty=format:"%h - %an, %ad : %s" $sha
  fi
done

# 语法二：使用 xargs（更紧凑的单行命令）
git cherry origin/phase2 origin/master | grep '^+' | awk '{print $2}' | xargs -I {} git show -s --format="%an {}" {} | grep "^池启苹" | awk '{print $2}' | xargs -I {} git log -1 --date=format:'%Y-%m-%d %H:%M:%S' --pretty=format:"%h - %an, %ad : %s" {}