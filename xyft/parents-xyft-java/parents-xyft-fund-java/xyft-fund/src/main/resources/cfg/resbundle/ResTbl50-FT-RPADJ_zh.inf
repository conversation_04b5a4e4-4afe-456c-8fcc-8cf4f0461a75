advOffFcy=冲销金额
adjOutFcy=调出金额
adjInFcy=调入金额

# 调整/冲销单主表
ft_rpadj_off=调整/冲销单主表
ft_rpadj_off.officode=${code}
ft_rpadj_off.offcode=冲销单号
adjPayOff.offcode=付款调整单号
ft_rpadj_off.rec.offcode=收款调整单号
ft_rpadj_off.status=${status}
ft_rpadj_off.bcode=${b_wcode}
ft_rpadj_off.wcode=${wcode}
ft_rpadj_off.corpbcode=${corpbcode}
ft_rpadj_off.ccode=${recccode}
ft_rpadj_off.wfuid=审批节点
ft_rpadj_off.purprjcode=采购业务编号
ft_rpadj_off.purordcode=采购合同号
ft_rpadj_off.purprjcodelist=采购业务编号
ft_rpadj_off.purordcodelist=采购合同号
ft_rpadj_off.purprjicodelist=采购业务编号内码
ft_rpadj_off.purordicodelist=采购合同号内码
ft_rpadj_off.pacodelist=付款申请号
ft_rpadj_off.paicodelist=付款申请号内码
ft_rpadj_off.sheetcode=单据类型
ft_rpadj_off.predatefm=${predatefm}
ft_rpadj_off.predateto=${dateto}
ft_rpadj_off.pacode=${pacode}
ft_rpadj_off.vprepare=${vprepare}
ft_rpadj_off.redflag=${redflagext}
ft_rpadj_off.reprpflag=代收代付
ft_rpadj_off.rpadjtype=付款调整类型
ft_rpadj_off.rec.rpadjtype=收款调整类型
ft_rpadj_off.officoder=调整/冲销单红冲内码
ft_rpadj_off.offcoder=调整/冲销单红冲号
ft_rpadj_off.officodeo=调整/冲销单原红内冲码
ft_rpadj_off.salprjcode=销售业务编号
ft_rpadj_off.salordcode=销售合同号
ft_rpadj_off.salshipcoder=发货单号
ft_rpadj_off.salprjicodelist=销售业务内码
ft_rpadj_off.salprjcodelist=销售业务编号
ft_rpadj_off.salordicodelist=销售合同号内码
ft_rpadj_off.salordcodelist=销售合同号
ft_rpadj_off.salshipicoderlist=发货单内码
ft_rpadj_off.salshipcoderlist=发货单号
ft_rpadj_off.rclmicodelist=收款认领单内码
ft_rpadj_off.rclmcodelist=收款认领单号
ft_rpadj_off.ccode1=客户

# 调整/冲销单子表-预付款
ft_rpadj_offg=调整/冲销单子表
ft_rpadj_offg.offgicode=调整/冲销单子表内码
ft_rpadj_offg.officode=调整/冲销单内码
ft_rpadj_offg.offgicoder=调整/冲销单子表红冲内码
ft_rpadj_offg.officoder=调整/冲销单红冲内码
ft_rpadj_offg.offcoder=调整/冲销单红冲号
ft_rpadj_offg.fpsgicoder=财务实付分摊核销码
ft_rpadj_offg.fpgicode=实付子表内码
ft_rpadj_offg.paicode=付款申请内码
ft_rpadj_offg.pacode=付款申请单号
ft_rpadj_offg.pagicode=付款单子表核销码
ft_rpadj_offg.rclmicoder=收款认领红冲核销内码
ft_rpadj_offg.purprjcode=采购业务编号
ft_rpadj_offg.purprjicode=采购业务编号内码
ft_rpadj_offg.purordcode=采购合同号
ft_rpadj_offg.purordicode=采购合同内码
ft_rpadj_offg.srccode=付款申请单号
ft_rpadj_offg.invrptype=费用类型
ft_rpadj_offg.rptype=收付项目
ft_rpadj_offg.fcode=${fcode}
ft_rpadj_offg.fcying=${unexecfcy}
ft_rpadj_offg.ccodetrust=委托方
# 核算项(8)发票登记号、采购合同号、采购业务编号、到货单号、销售合同号、销售业务编号、报关单号。
ft_rpadj_offg.invcoder=发票登记号
ft_rpadj_offg.purordcode=采购合同号
ft_rpadj_offg.purprjcode=采购业务编号
ft_rpadj_offg.purshipcoder=到货单号
ft_rpadj_offg.salordcode=销售合同号
ft_rpadj_offg.salprjcode=销售业务编号
ft_rpadj_offg.eclcode=报关单号
ft_rpadj_offg.fcy=${advOffFcy}
adjPayOff.fcy=${adjOutFcy}
ft_rpadj_offg.fcyed=${executefcy}
ft_rpadj_offg.rpfcy=支付金额
ft_rpadj_offg.scy=折本位币金额
ft_rpadj_offg.zcny=折人民币金额
ft_rpadj_offg.zusd=折美元金额
ft_rpadj_offg.fserate=${fserate}
ft_rpadj_offg.mfserate=申请支付币种折本位币汇率
ft_rpadj_offg.scerate=${fcerate}
ft_rpadj_offg.suerate=${fuerate}
ft_rpadj_offg.fzerate=折支付币种比率
ft_rpadj_offg.srcfcy=${fcy}
ft_rpadj_offg.srcicode=${srcicode}
ft_rpadj_offg.srccode=${srccode}
ft_rpadj_offg.srcsheetcode=${srcsheetcode}
ft_rpadj_offg.tcaptime=${tcaptime}
ft_rpadj_offg.year=${year}
ft_rpadj_offg.month=${month}
ft_rpadj_offg.sdate=支付日期
ft_rpadj_offg.salprjicode=销售业务编号内码
ft_rpadj_offg.salordicode=销售合同号内码
ft_rpadj_offg.purshipicoder=到货单号内码
ft_rpadj_offg.salshipicoder=销售发货单号内码
ft_rpadj_offg.logitoicode=运输委托单内码
ft_rpadj_offg.tsosicoder=出仓单内码
ft_rpadj_offg.invicoder=发票登记内码
ft_rpadj_offg.invsheetcode=发票单据号
ft_rpadj_offg.pticode=协议内码
ft_rpadj_offg.adjfcy=可使用金额
ft_rpadj_offg.adjsumfcy=可使用总金额
ft_rpadj_offg.paydate=支付日期
ft_rpadj_offg.paysumfcy=累计付款金额
ft_rpadj_offg.qtc=合同数量
ft_rpadj_offg.mfcy=合同金额
ft_rpadj_offg.settqtc=已匹票数量
ft_rpadj_offg.settfcy=已匹票金额
ft_rpadj_offg.vqtc=已到货数量
ft_rpadj_offg.vqty=已到货金额
ft_rpadj_offg.invfcyed=发票已核销金额

#调整后汇总信息
ft_bcsumm.outIdx=调出序号
ft_bcsumm.inIdx=调入序号
ft_bcsumm.rptype=收付项目
ft_bcsumm.invrptype=费用类型
ft_bcsumm.fcode=币种
ft_bcsumm.fcy=${adjInFcy}
ft_bcsumm.rec.fcy=实际调入金额
ft_bcsumm.fcyed=已执行金额
ft_bcsumm.sumfcyed=调整后支付金额
ft_bcsumm.rec.sumfcyed=调整后认领金额
ft_bcsumm.cindate=调入日期
ft_bcsumm.purprjcode=采购业务编号
ft_bcsumm.purordcode=采购合同号
ft_bcsumm.salprjcode=销售业务编号
ft_bcsumm.salordcode=销售合同号
ft_bcsumm.cfmode=认定方式
ft_bcsumm.purshipcoder=到货单号
ft_bcsumm.tcaptime=${tcaptime}
ft_bcsumm.ccdoe=收款方

# 调整/冲销单孙表-冲销数据
ft_rpadj_offgs=调整/冲销单孙表
ft_rpadj_offgs.offgsicode=调整/冲销单孙表内码
ft_rpadj_offgs.offgicode=调整/冲销单子表内码
ft_rpadj_offgs.officode=调整/冲销单内码
ft_rpadj_offgs.offgsicoder=调整/冲销单孙表红冲内码
ft_rpadj_offgs.offgicoder=调整/冲销单子表红冲内码
ft_rpadj_offgs.officoder=调整/冲销单红冲内码
ft_rpadj_offgs.offcoder=调整/冲销单红冲号
ft_rpadj_offgs.lrpicodex=往来应收付核销码
ft_rpadj_offgs.purprjcode=采购业务编号
ft_rpadj_offgs.purordcode=${purordcode}
ft_rpadj_offgs.invrptype=费用类型
ft_rpadj_offgs.fdc=金额方向
ft_rpadj_offgs.fcy=${ft_bcsumm.fcy}
ft_rpadj_offgs.adv.fcy=${fcy}
ft_rpadj_offg.adv.rpfcy=折预付金额
ft_rpadj_offgs.rec.fcy=${ft_bcsumm.rec.fcy}
ft_rpadj_offgs.fcyed=${executefcy}
ft_rpadj_offgs.payfcyed=已付款金额
ft_rpadj_offgs.scy=折本位币金额
ft_rpadj_offgs.zcny=折人民币金额
ft_rpadj_offgs.zusd=折美元金额
ft_rpadj_offgs.fserate=${fserate}
ft_rpadj_offgs.scerate=${fcerate}
ft_rpadj_offgs.suerate=${fuerate}
ft_rpadj_offgs.srcfcy=${planexecutefcy}
ft_rpadj_offgs.sumfcyed=${ft_bcsumm.sumfcyed}
ft_rpadj_offgs.sumpayfcyed=${ft_bcsumm.sumfcyed}
ft_rpadj_offgs.rec.sumfcyed=${ft_bcsumm.rec.sumfcyed}
ft_rpadj_offgs.cindate=调入日期
ft_rpadj_offgs.rptype=收付项目
ft_rpadj_offgs.fcode=${fcode}
ft_rpadj_offgs.fcying=${unexecfcy}
ft_rpadj_offgs.payfcy=折预付金额
ft_rpadj_offgs.fzerate=折预付比率
ft_rpadj_offgs.tcaptime=事务处理日期
ft_rpadj_offgs.paydate=支付日期
ft_rpadj_offgs.recfcy=折预收金额
ft_rpadj_offgs.recfzerate=折预收比率
ft_rpadj_offgs.rpfcy=折预收/付金额
ft_rpadj_offgs.ccodetrust=委托方
ft_rpadj_offgs.sdate=支付日期
ft_rpadj_offgs.rec.rclmfpayfcyed=已认领金额
ft_rpadj_offgs.pay.rclmfpayfcyed=已付款金额
# 核算项
ft_rpadj_offgs.invcoder=发票登记号
ft_rpadj_offgs.purordicode=采购合同内码
ft_rpadj_offgs.purordcode=采购合同号
ft_rpadj_offgs.purprjicode=采购业务编号内码
ft_rpadj_offgs.purprjcode=采购业务编号
ft_rpadj_offgs.purshipcoder=到货单号
ft_rpadj_offgs.salordcode=销售合同号
ft_rpadj_offgs.salprjcode=销售业务编号
ft_rpadj_offgs.salprjicode=销售业务编号内码
ft_rpadj_offgs.eclcode=报关单号
ft_rpadj_offgs.salordicode=销售合同号内码
ft_rpadj_offgs.purshipicoder=到货单号内码
ft_rpadj_offgs.salshipicoder=销售发货单号内码
ft_rpadj_offgs.logitoicode=运输委托单内码
ft_rpadj_offgs.tsosicoder=出仓单内码
ft_rpadj_offgs.invicoder=发票登记内码
ft_rpadj_offgs.invsheetcode=发票单据号
ft_rpadj_offgs.pticode=协议内码

# 追加预付款工作台
ft_pay_fpsg_cb_view.salordcode=销售合同号
ft_pay_fpsg_cb_view.invcoder=发票登记号
ft_pay_fpsg_cb_view.paydatefm=支付日期从
ft_pay_fpsg_cb_view.paydateto=到
ft_pay_fpsg_cb_view.rptype=收付项目
ft_pay_fpsg_cb_view.invrptype=费用类型
ft_pay_fpsg_cb_view.curfcy=本次使用金额
ft_pay_fpsg_cb_view.fcying=${unexecfcy}
ft_pay_fpsg_cb_view.paydate=支付日期
ft_pay_fpsg_cb_view.purordcode=采购合同号
ft_pay_fpsg_cb_view.purprjcode=采购业务编号
ft_pay_fpsg_cb_view.purshipcoder=到货单号
ft_pay_fpsg_cb_view.padateto=到货单号
ft_pay_fpsg_cb_view.salordcode=销售合同号
ft_pay_fpsg_cb_view.salprjcode=销售业务编号
ft_pay_fpsg_cb_view.eclcode=报关单号
ft_pay_fpsg_cb_view.outfcy=${adjOutFcy}
ft_pay_fpsg_cb_view.fcy=原币金额
ft_pay_fpsg_cb_view.qtc=合同数量
ft_pay_fpsg_cb_view.mfcy=合同金额
ft_pay_fpsg_cb_view.settqtc=已匹票数量
ft_pay_fpsg_cb_view.settfcy=已匹票金额
ft_pay_fpsg_cb_view.vqtc=已到货数量
ft_pay_fpsg_cb_view.vqty=已到货金额
ft_pay_fpsg_cb_view.invfcyed=发票已核销金额
ft_pay_fpsg_cb_view.rpadjtype=付款调整类型
ft_pay_fpsg_cb_view.ccode=收款方
ft_pay_fpsg_cb_view.fpsgicode=实付分摊明细内码
ft_pay_fpsg_cb_view.fpsgicoder=财务实付分摊核销码
ft_pay_fpsg_cb_view.paicode=付款申请单号内码
ft_pay_fpsg_cb_view.purordicode=采购合同内码
ft_pay_fpsg_cb_view.purordcode=采购合同号
ft_pay_fpsg_cb_view.purprjicode=采购业务编号内码
ft_pay_fpsg_cb_view.purprjcode=采购业务编号
ft_pay_fpsg_cb_view.logitoicode=运输委托单内码
ft_pay_fpsg_cb_view.logitocode=运输委托单号
ft_pay_fpsg_cb_view.intricode=内部交易内码
ft_pay_fpsg_cb_view.intrcode=内部交易号
ft_pay_fpsg_cb_view.adjfcy=可使用金额
ft_pay_fpsg_cb_view.adjsumfcy=可使用总金额
ft_pay_fpsg_cb_view.paysumfcy=累计付款金额

#付款调整单(批量)调入信息表
ft_rpadj_offin.idx=${idx}
ft_rpadj_offin.officode=调整单内码
ft_rpadj_offin.officoder=付款调整单红冲内码
ft_rpadj_offin.offcoder=付款调整单红冲号
ft_rpadj_offin.offinicode=调入信息内码
ft_rpadj_offin.offinicoder=调入信息红冲内码
ft_rpadj_offin.lrpicodex=往来应收付核销码
ft_rpadj_offin.pagicode=付款申请明细内码
ft_rpadj_offin.rptype=收付项目
ft_rpadj_offin.invrptype=费用类型
ft_rpadj_offin.fcode=${fcode}
ft_rpadj_offin.fcy=${ft_bcsumm.fcy}
ft_rpadj_offin.fdc=金额方向
ft_rpadj_offin.fcying=${unexecfcy}
ft_rpadj_offin.fcyed=已执行金额
ft_rpadj_offin.srcfcy=计划执行金额
ft_rpadj_offin.cindate=调入日期
ft_rpadj_offin.ccode=收款方
ft_rpadj_offin.purprjcode=采购业务编号
ft_rpadj_offin.purprjicode=采购业务编号内码
ft_rpadj_offin.purordcode=采购合同号
ft_rpadj_offin.purordicode=采购合同内码
ft_rpadj_offin.salprjcode=销售业务编号
ft_rpadj_offin.salordcode=销售合同号
ft_rpadj_offin.tradetype=贸易类型
ft_rpadj_offin.cfmode=认定方式
ft_rpadj_offin.purshipcoder=到货单号
ft_rpadj_offin.invcoder=发票登记号
ft_rpadj_offin.srccode=${srccode}
ft_rpadj_offin.srcsheetcode=${srcsheetcode}
ft_rpadj_offin.rclmfcyed=已认领金额

# 往来冲抵单
ft_rpadj_lrpoff=往来冲抵单
ft_rpadj_lrpoff.lrpofficode=冲抵单内码
ft_rpadj_lrpoff.lrpoffcode=冲抵单号
ft_rpadj_lrpoff.lrpofficoder=往来冲抵单红冲核销内码
ft_rpadj_lrpoff.lrpoffcoder=往来冲抵单红冲核销外码
ft_rpadj_lrpoff.lrpofficodeo=往来冲抵单原核销内码
ft_rpadj_lrpoff.fcode=冲抵币种
ft_rpadj_lrpoff.advrecfcy=预收金额
ft_rpadj_lrpoff.advpayfcy=预付金额
ft_rpadj_lrpoff.recfcy=应收金额
ft_rpadj_lrpoff.payfcy=应付金额
ft_rpadj_lrpoff.redflag#2=${red}
ft_rpadj_lrpoff.redflag#5=${bered}
ft_rpadj_lrpoff.vidflag=制证选项
ft_rpadj_lrpoff.viddesc=凭证号

ft_pay_fpsg_view.lrpofficode=往来冲抵单内码

# 往来冲抵单 冲抵明细表
ft_rpadj_lrpoffg.lrpoffgicode=往来冲抵单明细表内码
ft_rpadj_lrpoffg.lrpofficode=冲抵单内码
ft_rpadj_lrpoffg.lrpoffgicoder=往来冲抵单明细表红冲核销内码
ft_rpadj_lrpoffg.lrpofficoder=往来冲抵单红冲核销内码
ft_rpadj_lrpoffg.lrpicodex=往来核销码
ft_rpadj_lrpoffg.fpsgicoder=实付分摊核销内码
ft_rpadj_lrpoffg.rclmicoder=收款认领红冲核销内码
ft_rpadj_lrpoffg.dc=收付
ft_rpadj_lrpoffg.fdc=金额方向
ft_rpadj_lrpoffg.tdc=是否退款
ft_rpadj_lrpoffg.fcy=原币金额
ft_rpadj_lrpoffg.offfcy=折冲抵币种金额
ft_rpadj_lrpoffg.fzerate=折冲抵币种比率
ft_rpadj_lrpoffg.rptype=收付项目
ft_rpadj_lrpoffg.salprjcode=销售业务编号
ft_rpadj_lrpoffg.purprjcode=采购业务编号
ft_rpadj_lrpoffg.salshipcoder=发货单号
ft_rpadj_lrpoffg.invcoder=发票登记号
ft_rpadj_lrpoffg.tsoscoder=出仓单号
ft_rpadj_lrpoffg.purshipcoder=到货单号
ft_rpadj_lrpoffg.ccode=供应商
ft_rpadj_lrpoffg.invcoder=发票号
ft_rpadj_lrpoffg.scy=折本位币金额
ft_rpadj_lrpoffg.tradetype=贸易类型
ft_rpadj_lrpoffg.isincost=是否进成本
ft_rpadj_lrpoffg.prjcode=业务编号
ft_rpadj_lrpoffg.fserate=汇率

# 往来处理单
ft_rpadj_lrpdeal=往来处理单
ft_rpadj_lrpdeal.lrpdicode=往来处理单内码
ft_rpadj_lrpdeal.lrpdcode=往来处理单号
ft_rpadj_lrpdeal.lrpdicoder=往来处理单红冲核销内码
ft_rpadj_lrpdeal.lrpdcoder=往来处理单红冲核销外码
ft_rpadj_lrpdeal.lrpdicodeo=往来处理单原核销内码
ft_rpadj_lrpdeal.dealtype=处理类型
ft_rpadj_lrpdeal.fcy=金额
ft_rpadj_lrpdeal.redflag#2=红冲
ft_rpadj_lrpdeal.redflag#5=被红冲

# 往来处理单 往来处理信息明细
ft_rpadj_lrpdealg.lrpdgicode=往来处理单明细表内码
ft_rpadj_lrpdealg.lrpdicode=往来处理单内码
ft_rpadj_lrpdealg.lrpdgicoder=往来处理单明细表红冲核销内码
ft_rpadj_lrpdealg.lrpdicoder=往来处理单红冲核销内码
ft_rpadj_lrpdealg.lrpicodex=往来核销码
ft_rpadj_lrpdealg.fpsgicoder=实付分摊核销内码
ft_rpadj_lrpdealg.rclmicoder=收款认领红冲核销内码
ft_rpadj_lrpdealg.dc=收付
ft_rpadj_lrpdealg.fdc=金额方向
ft_rpadj_lrpdealg.tdc=退款方向
ft_rpadj_lrpdealg.fcy=金额
ft_rpadj_lrpdealg.rptype=收付项目
ft_rpadj_lrpdealg.salprjcode=销售业务编号
ft_rpadj_lrpdealg.purprjcode=采购业务编号
ft_rpadj_lrpdealg.purshipcoder=到货单号
ft_rpadj_lrpdealg.invcoder=发票登记号
ft_rpadj_lrpdealg.ccodetrust=委托方
ft_rpadj_lrpdealg.tsoscoder=出仓回单号
ft_rpadj_lrpdealg.invsheetcode=发票单据号

# 内存表
advancebalance=预付款余额
payablebalance=应付款余额
adjustmoney=可调整金额

# 往来处理单凭证
ft_rpadj_lrpdealvmark_view.lrpdcode=往来处理单号
ft_rpadj_lrpdealvmark_view.lrpdicode=往来处理单内码
ft_rpadj_lrpdealvmark_view.dealtype=处理类型
ft_rpadj_lrpdealvmark_view.viddesc=凭证号
ft_rpadj_lrpdealvmark_view.redflag=红冲
ft_rpadj_lrpdealvmark_view.redflag#1=${bered}
ft_rpadj_lrpdealvmark_view.redflag#2=${red}
ft_rpadj_lrpdealvmark_view.batchvid=凭证大号
ft_rpadj_lrpdealvmark_view.vmarkicode=制凭证标记表内码
ft_rpadj_lrpdealvmark_view.lrpicode=往来内码

# 往来冲销工作台
advofftype=冲销分类
isprjadvoff=是否按业务编号冲销
isordadvoff=是否按合同号冲销
islogitoadvoff=是否按运输委托单冲销
isadvoffemd=是否冲销保证金
isssordvoff=是否按发货单号冲销
offaccountdate=冲销入账日期