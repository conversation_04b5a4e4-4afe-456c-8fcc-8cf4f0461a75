classes:
 snsoft.ft.comm.busi.FundAidAddTgbControlJSListener:snsoft.plat.bas.busi.SystemFunctionListener
 snsoft.ft.comm.busi.JudgeNullFldsCopy:Xjs.ui.util.JudgeFldsCopy
 snsoft.ft.comm.busi.JudgeSwiftCodeFldsCopy:Xjs.ui.util.JudgeFldsCopy
 snsoft.ft.comm.invoker.AddFundsrcInvoker:snsoft.ext.cmd.com.biz.ClickPullDownInvoker
 snsoft.ft.comm.invoker.CheckTblNotEmptyInvoker:snsoft.ext.cmd.CommandInvoker
 snsoft.ft.comm.invoker.DataShowChangeInvoker:snsoft.ext.cmd.CommandInvoker
 snsoft.ft.comm.invoker.DeleteFundSrcOprateInvoker:snsoft.ext.cmd.CommandInvoker
 snsoft.ft.comm.invoker.ForceRefreshTableInvoker:snsoft.ext.cmd.CommandInvoker
 snsoft.ft.comm.invoker.FundCreditInfoRefreshInvoker:snsoft.ft.comm.cmdreg.CmdCommInvoker
 snsoft.ft.comm.invoker.FundCredittypeEditInvoker:snsoft.ft.comm.cmdreg.CellsModifyInvoker
 snsoft.ft.comm.invoker.PushFundBatchInvoker:snsoft.ext.cmd.CommandInvoker
 snsoft.ft.comm.invoker.PushFundBatchJSInvoker:snsoft.ext.cmd.CommandInvoker
 snsoft.ft.comm.invoker.QueryResultBtnInvoker:snsoft.ext.cmd.CommandInvoker
 snsoft.ft.comm.invoker.SendATSInvoker:snsoft.ext.cmd.CommandInvoker
 snsoft.ft.comm.invoker.SendSerCancelInvoker:snsoft.plat.bas.sheet.cmd.CommandSheet
 snsoft.ft.comm.invoker.SupFundsrcInvoker:snsoft.ext.cmd.CommandInvoker
 snsoft.ft.comm.lis.FundChkFldCodeDataJSListener:snsoft.plat.bas.busi.SystemFunctionListener
 snsoft.ft.comm.lis.FundCreditFinancialInfoJSListener:snsoft.plat.bas.busi.SystemFunctionListener
 snsoft.ft.comm.lis.FundCreditInfoCtrlJSListener:snsoft.plat.bas.busi.SystemFunctionListener
 snsoft.ft.comm.lis.FundCreditInfoJSListener:snsoft.plat.bas.busi.SystemFunctionListener
 snsoft.ft.comm.lis.FundCreditInfoRefreshJSListener:snsoft.plat.bas.busi.SystemFunctionListener
 snsoft.ft.comm.lis.FundCreditMainInfoJSListener:snsoft.plat.bas.busi.SystemFunctionListener
 snsoft.ft.comm.lis.FundSrcJSListener:snsoft.plat.bas.busi.SystemFunctionListener
 snsoft.ft.comm.match.CreditmodeMatchValue:Xjs.table.sample.TableOptsCtrlListener$MatchValue
 snsoft.ft.comm.match.RpModeMatchValue:Xjs.table.sample.TableOptsCtrlListener$MatchValue
 snsoft.ft.comm.service.FundCreditUIService
