Xjs.loadedXjs.push("snsoft/ft/fund/rec/rclm");
Xjs.namespace("snsoft.ft.rec.rclm.invoker");snsoft.ft.rec.rclm.invoker.RecClaimDelInvoker=function(parameter){snsoft.ft.rec.rclm.invoker.RecClaimDelInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.rec.rclm.invoker.RecClaimDelInvoker,snsoft.ext.cmd.com.biz.OperateDelete,{invoke:function(event){if(snsoft.ext.cmd.com.biz.SaveInvoker.isNewRow(event.dataSet,this.dataSet.getRow())){this.dataSet.deleteRow();return null;}var invokeParam=this.buildDetailInvokeParam(null);invokeParam.sheetcode=this.sheetcode;return invokeParam;},buildDetailInvokeParam:function(func){var param={},checkModified=(this.cfg.flags&0x4)!=0,dataLimit=(this.cfg.flags&0x8)!=0;if(checkModified||dataLimit){var tbl=this.table,ds=this.dataSet;if((this.cfg.flags&0x1)!=0){var rows=tbl.getSelectedRowNumbers(1);if(rows.length==0)throw new Error("没有数据！");param.mains=[];for(var r=0;r<rows.length;r++){var keyVals=ds.getKeyValues(rows[r]);if(checkModified)keyVals.modifydate=ds.getValue("modifydate",rows[r]);if(func)func.call(this,keyVals,rows[r]);param.mains.push(keyVals);}}else {if(ds.getRowCount()==0)throw new Error("没有数据！");var keyVals=ds.getKeyValues();if(checkModified)keyVals.modifydate=ds.getValue("modifydate");if(func)func.call(this,keyVals,ds.getRow());param.mains=[keyVals];}}return param;},afterInvoke:function(event){if(event.invokeRtnVal)snsoft.ft.utils.FTUtils.replaceValueAndAutoRefresh(event.mainTable,event.invokeRtnVal.tableRowValues);}});Xjs.namespace("snsoft.ft.rec.rclm.lis");snsoft.ft.rec.rclm.lis.RecClaimGvcodeCtrlJSListener=function(params){snsoft.ft.rec.rclm.lis.RecClaimGvcodeCtrlJSListener.superclass.constructor.call(this,params);};Xjs.extend(snsoft.ft.rec.rclm.lis.RecClaimGvcodeCtrlJSListener,snsoft.plat.bas.busi.SystemFunctionListener,{initComponent:function(table,values){snsoft.ft.rec.rclm.lis.RecClaimGvcodeCtrlJSListener.superclass.initComponent.call(this,table,values);this.table=table;this.dataSet=table.getDataSet();this.pendPayRptypes=values.pendPayRptypes||[];this.recClaimGvcodeDefs=values.recClaimGvcodeDefs||[];},dataSetFieldPosted:function(dataSet,event){snsoft.ft.rec.rclm.lis.RecClaimGvcodeCtrlJSListener.superclass.dataSetFieldPosted.call(this,dataSet,event);var columnName=event.columnName;if("rptype"==columnName||"bcode"==columnName)this.setGvcode();},setGvcode:function(){var bcode=this.dataSet.getValue("bcode"),rptype=this.dataSet.getValue("rptype");if(this.pendPayRptypes.indexOf(rptype)>=0){this.dataSet.setValue("gvcode",null);var results=this.recClaimGvcodeDefs.filter((def)=>{return def.bcode==bcode&&def.isdft=="Y";});if(results.length>0){var jso=results[0];this.dataSet.setValue("gvcode",jso.gvcode);}}},itemAidInputing:function(table,e){snsoft.ft.rec.rclm.lis.RecClaimGvcodeCtrlJSListener.superclass.itemAidInputing.call(this,table,e);if(e.forTblColumn.name=="gvcode")this.setGvcodeFilter();},setGvcodeFilter:function(){var rptype=this.dataSet.getValue("rptype");if(this.pendPayRptypes.indexOf(rptype)>=0){var bcode=this.dataSet.getValue("bcode");if(bcode==null){var bcodeTitle=this.table.getColumn("bcode").title,message=Xjs.ResBundle.getResVal("FT.00000037",bcodeTitle);throw new Error(message);}}}});snsoft.ft.rec.rclm.lis.RecClaimJSListener=function(params){snsoft.ft.rec.rclm.lis.RecClaimJSListener.superclass.constructor.call(this,params);};Xjs.extend(snsoft.ft.rec.rclm.lis.RecClaimJSListener,snsoft.plat.bas.busi.SystemFunctionListener,{initComponent:function(table,values){snsoft.ft.rec.rclm.lis.RecClaimJSListener.superclass.initComponent.call(this,table,values);this.rclmTable=table;this.rclmDataSet=table.getDataSet();this.bknoTable=Xjs.util.TableUtils.getTable_$Panel_$String(table,"ft_rec_bno");this.bknoDataSet=this.bknoTable.getDataSet();},dataSetFieldPosted:function(dataSet,event){snsoft.ft.rec.rclm.lis.RecClaimJSListener.superclass.dataSetFieldPosted.call(this,dataSet,event);var columnName=event.columnName;if("fcode"==columnName||"fcy"==columnName)this.setRecfcy(event);},itemAidInputing:function(table,e){snsoft.ft.rec.rclm.lis.RecClaimJSListener.superclass.itemAidInputing.call(this,table,e);this.checkIsValid();if(e.forTblColumn.name=="gvcode")this.checkIsValid();},checkIsValid:function(){var bkno_isvalid=this.bknoDataSet.getValue("isvalid");if("Y"!==bkno_isvalid){throw new Error(Xjs.ResBundle.getResVal("FT-REC.00000041"));}},setRecfcy:function(event){if("fcode"==event.columnName){var fcode_new=this.rclmDataSet.getValue("fcode");if(!(fcode_new==event.value))this.rclmDataSet.setValue("recfcy",null);}var bkno_fcode=this.bknoDataSet.getValue("fcode"),rclm_fcode=this.rclmDataSet.getValue("fcode");if(bkno_fcode==rclm_fcode){var fcy=this.rclmDataSet.getValue("fcy");this.rclmDataSet.setValue("recfcy",fcy);}},dataSetSaved:function(dataSet,e){snsoft.ft.rec.rclm.lis.RecClaimJSListener.superclass.dataSetSaved.call(this,dataSet,e);this.bknoReplaceValue(e);},bknoReplaceValue:function(e){if(e.saveInfo){var values=e.saveInfo["UI."+this.bknoTable.name];if(values)this.bknoDataSet.dataStore._replaceValues(values.ReplaceValues);}},onChkingRowNonBlank:function(table,e,va){if(va){var $listener=this;va.forEach((v)=>{return delete v.fmasterRow;});}}});