Xjs.loadedXjs.push("snsoft/ft/fund/forex/dely");
Xjs.namespace("snsoft.ft.forex.dely.lis");snsoft.ft.forex.dely.lis.ForexDelyAppCtrlJSListener=function(params){snsoft.ft.forex.dely.lis.ForexDelyAppCtrlJSListener.superclass.constructor.call(this,params);};Xjs.extend(snsoft.ft.forex.dely.lis.ForexDelyAppCtrlJSListener,snsoft.plat.bas.busi.SystemFunctionListener,{dataSetFieldPosted:function(dataSet,event){snsoft.ft.forex.dely.lis.ForexDelyAppCtrlJSListener.superclass.dataSetFieldPosted.call(this,dataSet,event);if("aecdate"==event.columnName){var aecdate=dataSet.getValue("aecdate"),exduedatefm=dataSet.getValue("exduedatefm"),exduedateto=dataSet.getValue("exduedateto");if(exduedateto&&aecdate>exduedateto){throw new Error(Xjs.ResBundle.getResVal("FT-FOREX.00000028"));}if(exduedatefm&&exduedatefm>aecdate)dataSet.setValue("duratype","1");else dataSet.setValue("duratype","10");}}});