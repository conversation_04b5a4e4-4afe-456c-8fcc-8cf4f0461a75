Xjs.loadedXjs.push("snsoft/ft/fund/rpadj/pay");
Xjs.namespace("snsoft.ft.rpadj.pay.invoker");snsoft.ft.rpadj.pay.invoker.AddDueCheckByAdjPayOffInvoker=function(parameter){snsoft.ft.rpadj.pay.invoker.AddDueCheckByAdjPayOffInvoker.superclass.constructor.call(this,parameter);this.otherRptypes=window.EnvParameter.S1002+","+window.EnvParameter.S1003+","+window.EnvParameter.S1001+","+window.EnvParameter.S1005+","+window.EnvParameter.S1008;};Xjs.extend(snsoft.ft.rpadj.pay.invoker.AddDueCheckByAdjPayOffInvoker,snsoft.ext.cmd.CommandInvoker,{check:function(event){var initParams={},refreshParams={},readonlyParams=[],offgDataset=Xjs.util.TableUtils.getTable_$Panel_$String(this.mainTable,"ft_rpadj_offg").dataSet;initParams.corpbcode=offgDataset.getValue("corpbcode");readonlyParams.push("corpbcode");initParams.bcode=offgDataset.getValue("bcode");if(String.isStrIn(this.otherRptypes,offgDataset.getValue("rptype"))){initParams.ccode=offgDataset.getValue("ccode");if(!this.mainDataSet.getValue("reprpflag"))readonlyParams.push("ccode");}if(!this.mainDataSet.getValue("reprpflag"))readonlyParams.push("bcode");initParams.fcyingflag="Y";if(this.fcyingflagRdonly)readonlyParams.push("fcyingflag");initParams.fcode=offgDataset.getValue("fcode");readonlyParams.push("fcode");refreshParams.sheetcode=offgDataset.getValue("srcsheetcode");refreshParams.limitsheetcode=this.sheetcode;refreshParams.lrpflag=1+4;event.checkData={initParams:initParams,readonlyParams:readonlyParams,refreshParams:refreshParams};return null;}});snsoft.ft.rpadj.pay.invoker.AddDueCheckByAdvPayOffInvoker=function(parameter){snsoft.ft.rpadj.pay.invoker.AddDueCheckByAdvPayOffInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.rpadj.pay.invoker.AddDueCheckByAdvPayOffInvoker,snsoft.ext.cmd.CommandInvoker,{beforeCheck:function(event){return {paymentsRtypes:""};},check:function(event){var initParams={},refreshParams={},readonlyParams=[],offgDataset=Xjs.util.TableUtils.getTable_$Panel_$String(this.mainTable,"ft_rpadj_offg").dataSet;initParams.bcode=offgDataset.getValue("bcode");readonlyParams.push("bcode");initParams.corpbcode=offgDataset.getValue("corpbcode");readonlyParams.push("corpbcode");initParams.ccode=this.mainDataSet.getValue("ccode");var paymentsRtypes=event.beforeCheckRtnVal.paymentsRtypes;if(!this.mainDataSet.getValue("reprpflag")||paymentsRtypes.indexOf(offgDataset.getValue("rptype"))!=-1)readonlyParams.push("ccode");initParams.fcyingflag="Y";readonlyParams.push("fcyingflag");refreshParams.sheetcode=offgDataset.getValue("srcsheetcode");refreshParams.limitsheetcode=this.sheetcode;refreshParams.lrpflag=2+4;if(!this.mainDataSet.getValue("reprpflag"))refreshParams.offgicode=offgDataset.getValue("offgicode");event.checkData={initParams:initParams,readonlyParams:readonlyParams,refreshParams:refreshParams};return null;},afterInvoke:function(event){var offgDataset=Xjs.util.TableUtils.getTable_$Panel_$String(this.mainTable,"ft_rpadj_offg").dataSet,gfcode=offgDataset.getValue("fcode"),gsfcode=this.dataSet.getValue("fcode");if(gfcode==gsfcode)this.dataSet.setValue("rpfcy",this.dataSet.getValue("fcy"));}});snsoft.ft.rpadj.pay.invoker.AdjPayOffAddPayDetailCheckInvoker=function(parameter){snsoft.ft.rpadj.pay.invoker.AdjPayOffAddPayDetailCheckInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.rpadj.pay.invoker.AdjPayOffAddPayDetailCheckInvoker,snsoft.ext.cmd.CommandInvoker,{check:function(event){this.mainTable.checkNonBlankForSubmit(0x12);return null;}});snsoft.ft.rpadj.pay.invoker.AdjPayOffEditCcodeCheckInvoker=function(parameter){snsoft.ft.rpadj.pay.invoker.AdjPayOffEditCcodeCheckInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.rpadj.pay.invoker.AdjPayOffEditCcodeCheckInvoker,snsoft.ft.rpadj.comm.invoker.AdjRecPayOffEditCcodeInvoker,{fireOnOk:function(params){snsoft.ft.rpadj.pay.invoker.AdjPayOffEditCcodeCheckInvoker.superclass.fireOnOk.call(this,params);var judgeReprpflag=params.tblDataSet.getValue("reprpflag"),bJudgeReprpflag=params.eventDataSet.getValue("reprpflag");if(!(judgeReprpflag==bJudgeReprpflag)&&judgeReprpflag=="1"){if(!(params.callOutDataSet.getValue("ccode")==params.tblDataSet.getValue("ccode"))){if(params.callOutDataSet.getRowCount()!=0){params.callInDataSet.deleteAllRows();this.mainTable.saveChanges();this.mainTable.refreshTable();}}}}});snsoft.ft.rpadj.pay.invoker.AdjPayOffSingleDataCopyInvoker=function(parameter){snsoft.ft.rpadj.pay.invoker.AdjPayOffSingleDataCopyInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.rpadj.pay.invoker.AdjPayOffSingleDataCopyInvoker,snsoft.ft.comm.cmdreg.SheetDataCopyInvoker,{resetOnShowing:function(dlg,dlgtbl,sheetparams){snsoft.ft.rpadj.pay.invoker.AdjPayOffSingleDataCopyInvoker.superclass.resetOnShowing.call(this,dlg,dlgtbl,sheetparams);var listener=dlgtbl.getListener(snsoft.ft.comm.busi.FieldExceedJSListener);if(listener){listener.maxIncZero=true;listener.minIncZero=true;}}});snsoft.ft.rpadj.pay.invoker.AdjPayOffWorkBenchExportInvoker=function(parameter){snsoft.ft.rpadj.pay.invoker.AdjPayOffWorkBenchExportInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.rpadj.pay.invoker.AdjPayOffWorkBenchExportInvoker,snsoft.ext.cmd.CommandInvoker,{beforeCheck:function(event){if(!this.dataSet.isOpen()){throw new Error(Xjs.ResBundle.getResVal("FT-PAY.00000072",event.table.title));}if(event.table.dataSet.getRowCount()==0){throw new Error(Xjs.ResBundle.getResVal("FT-PAY.00000072",event.table.title));}return null;},check:function(event){return {type:0,title:this.getTitle(),prompt:this.getResVal("FT-PAY.00000073")};},invoke:function(event){this.table.dataSet.postRow();var invokeParam=this.buildInvokeParam(null),adjPayOffExportParams={};adjPayOffExportParams.sheetcode=this.sheetcode;adjPayOffExportParams.xlsdefcode=this.xlsdefcode;adjPayOffExportParams.muiid=this.table.getRootComponentMID();adjPayOffExportParams.sub=this.table.name;adjPayOffExportParams.refreshParam=this.table.dataSet._refreshParameter;adjPayOffExportParams.matchCol=this.matchCol;invokeParam.adjPayOffExportParams=adjPayOffExportParams;return invokeParam;},afterInvoke:function(event){if(event.invokeRtnVal&&"url" in event.invokeRtnVal)window.open(Xjs.ROOTPATH+event.invokeRtnVal.url,"_top");}});snsoft.ft.rpadj.pay.invoker.EditCcodeConfirmInvoker=function(parameter){snsoft.ft.rpadj.pay.invoker.EditCcodeConfirmInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.rpadj.pay.invoker.EditCcodeConfirmInvoker,snsoft.ext.cmd.CommandInvoker,{check:function(event){return this.newDialogWithFunc(event,(e)=>{this.mainTable.openPopupEdit("editccodeDlg");var editccodeDlg=this.mainTable.getPopupEditDialog("editccodeDlg");return editccodeDlg;},(e,d)=>{var param={};param.officode=this.mainDataSet.get("officode");param.bcode=d.getItemByName("bcode").getValue();param.ccode=d.getItemByName("ccode").getValue();param.corpbcode=d.getItemByName("corpbcode").getValue();param.reprpflag=d.getItemByName("reprpflag").getValue();event.checkData.param=param;});},invoke:function(event){return event.checkData.param;},afterInvoke:function(event){this.mainTable.refreshTable();}});snsoft.ft.rpadj.pay.invoker.EditCcodeInvoker=function(parameter){snsoft.ft.rpadj.pay.invoker.EditCcodeInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.rpadj.pay.invoker.EditCcodeInvoker,snsoft.ext.cmd.CommandInvoker,{check:function(event){var gTable=Xjs.util.TableUtils.getTable_$Panel_$String(this.mainTable,"ft_rpadj_offg"),gsTable=Xjs.util.TableUtils.getTable_$Panel_$String(this.mainTable,"ft_rpadj_offgs"),gTableSize=gTable.getDataSet().getRows().length,gsTableSize=gsTable.getDataSet().getRows().length;if(gTableSize!=0||gsTableSize!=0)return {type:0,title:this.getTitle(),prompt:this.getResVal("FT-RPADJ.10000001")};return null;}});Xjs.namespace("snsoft.ft.rpadj.pay.lis");snsoft.ft.rpadj.pay.lis.AdjPayOffGDetailJSListener=function(parameter){snsoft.ft.rpadj.pay.lis.AdjPayOffGDetailJSListener.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.rpadj.pay.lis.AdjPayOffGDetailJSListener,snsoft.ft.rpadj.comm.lis.AdjRecPayOffGDetailJSListener,{dataSetFieldPosting:function(dataSet,event){snsoft.ft.rpadj.pay.lis.AdjPayOffGDetailJSListener.superclass.dataSetFieldPosting.call(this,dataSet,event);if(this.callInTable.getDataSet()==dataSet&&event.columnName=="cindate"){var callOutDate=this.callOutTable.getDataSet().getValue("sdate"),callInDate=event.value;if(callInDate<callOutDate){throw new Error(Xjs.ResBundle.getResVal("FT-RPADJ.10000008"));}}},dataSetRowNavigated:function(dataSet,e){snsoft.ft.rpadj.pay.lis.AdjPayOffGDetailJSListener.superclass.dataSetRowNavigated.call(this,dataSet,e);var mainDataset=Xjs.util.TableUtils.getTable_$Panel_$String(dataSet.getTables()[0],"ft_rpadj_off").dataSet,rdonlyFlag=String.isStrIn(window.EnvParameter.S1006,dataSet.getValue("rptype"))&&mainDataset.getValue("reprpflag");dataSet.getTables()[0].getColumn("ccode").setReadonly(!rdonlyFlag);}});snsoft.ft.rpadj.pay.lis.AdjPayOffRptypeCtrlJSlistener=function(params){snsoft.ft.rpadj.pay.lis.AdjPayOffRptypeCtrlJSlistener.superclass.constructor.call(this,params);this.bwcodeNoRdonly=params.bwcodeNoRdonly;};Xjs.extend(snsoft.ft.rpadj.pay.lis.AdjPayOffRptypeCtrlJSlistener,snsoft.ft.rpadj.comm.lis.RecPayOffRptypeCtrlJSListener,{bwcodeNoRdonly:false,itemAidInputing:function(table,e){var mainTable=this.getTable(table,"ft_rpadj_off"),reprpflag=null,rptype=table.getDataSet().getValue("rptype");if(mainTable)reprpflag=mainTable.getDataSet().getValue("reprpflag");var rdonlyFlag=String.isStrIn(window.EnvParameter.S1006,rptype)&&reprpflag;if(rdonlyFlag)this.removeFldName=this.removeFldName?this.removeFldName+",ccode":"ccode";else this.removeFldName=this.removeFldName?this.removeFldName:"";snsoft.ft.rpadj.pay.lis.AdjPayOffRptypeCtrlJSlistener.superclass.itemAidInputing.call(this,table,e);if(e.item.name=="ptcode"){if(reprpflag)snsoft.ft.rpadj.pay.lis.AdjPayOffRptypeCtrlJSlistener.setParamItemsCtrl(table,e,"bcode,ccode");}if(e.item.name=="purordcode"){if(this.bwcodeNoRdonly)snsoft.ft.rpadj.pay.lis.AdjPayOffRptypeCtrlJSlistener.setParamItemsCtrl(table,e,"bcode");if(reprpflag){if(String.isStrIn(window.EnvParameter.S1006,rptype))snsoft.ft.rpadj.pay.lis.AdjPayOffRptypeCtrlJSlistener.setParamItemsCtrl(table,e,"bcode");else if(String.isStrIn(window.EnvParameter.S1002+","+window.EnvParameter.S1005,rptype))snsoft.ft.rpadj.pay.lis.AdjPayOffRptypeCtrlJSlistener.setParamItemsCtrl(table,e,"bcode,purccode");}}if(e.item.name=="salordcode"){if(reprpflag){if(String.isStrIn(window.EnvParameter.S1006,rptype))snsoft.ft.rpadj.pay.lis.AdjPayOffRptypeCtrlJSlistener.setParamItemsCtrl(table,e,"bcode");else if(String.isStrIn(window.EnvParameter.S1003,rptype))snsoft.ft.rpadj.pay.lis.AdjPayOffRptypeCtrlJSlistener.setParamItemsCtrl(table,e,"bcode,salccode");}}if(e.item.name=="salshipcoder"){if(reprpflag)snsoft.ft.rpadj.pay.lis.AdjPayOffRptypeCtrlJSlistener.setParamItemsCtrl(table,e,"bcode,salccode");}if(e.item.name=="logitocode"){if(reprpflag)snsoft.ft.rpadj.pay.lis.AdjPayOffRptypeCtrlJSlistener.setParamItemsCtrl(table,e,"bcode,carrier");}}});Xjs.apply(snsoft.ft.rpadj.pay.lis.AdjPayOffRptypeCtrlJSlistener,{setParamItemsCtrl:function(table,e,fldNames){var ptcode=table.getColumn(e.item.name),ptcodeDialog=ptcode.aidInputer,paramItemsCtrl=ptcodeDialog.paramItemsCtrl;for(var fldName of fldNames.split(",")){delete paramItemsCtrl[fldName];}}});snsoft.ft.rpadj.pay.lis.AdvPayOffgsJSListener=function(params){snsoft.ft.rpadj.pay.lis.AdvPayOffgsJSListener.superclass.constructor.call(this,params);};Xjs.extend(snsoft.ft.rpadj.pay.lis.AdvPayOffgsJSListener,snsoft.plat.bas.busi.SystemFunctionListener,{initComponent:function(table,values){snsoft.ft.rpadj.pay.lis.AdvPayOffgsJSListener.superclass.initComponent.call(this,table,values);if(this.gstable==null){this.gstable=table;this.mainTable=table.getRootTable();this.gtable=Xjs.util.TableUtils.getTable_$Panel_$String(this.mainTable,"ft_rpadj_offg");}},dataSetFieldPosted:function(dataSet,event){snsoft.ft.rpadj.pay.lis.AdvPayOffgsJSListener.superclass.dataSetFieldPosted.call(this,dataSet,event);if(event.columnName!="rpfcy"&&event.columnName!="fcy")return;var fzeratescale=this.gstable.getColumn("fzerate").maxDecimals,rpfcy=dataSet.getValue("rpfcy"),fcy=dataSet.getValue("fcy");dataSet.setValue("fzerate",snsoft.ft.utils.FTUtils.round(snsoft.ft.utils.FTUtils.div(rpfcy,fcy),fzeratescale));},dataSetRowNavigated:function(dataSet,e){snsoft.ft.rpadj.pay.lis.AdvPayOffgsJSListener.superclass.dataSetRowNavigated.call(this,dataSet,e);var gfcode=this.gtable.getDataSet().getValue("fcode"),gsfcode=dataSet.getValue("fcode"),isReadonly=gfcode==gsfcode;this.gstable.getColumn("rpfcy").editdisable=isReadonly;this.gstable.render(2);}});