Xjs.loadedXjs.push("snsoft/ft/fund/cps/upa");
Xjs.namespace("snsoft.ft.cps.upa.invoker");snsoft.ft.cps.upa.invoker.CpsUsePayAppGenInvoker=function(parameter){snsoft.ft.cps.upa.invoker.CpsUsePayAppGenInvoker.superclass.constructor.call(this,parameter);this.tgtsheetcode=parameter.tgtsheetcode;this.copycode=parameter.copycode;};Xjs.extend(snsoft.ft.cps.upa.invoker.CpsUsePayAppGenInvoker,snsoft.ft.comm.cmdreg.CmdCommInvoker,{beforeCheck:function(event){var repayful=this.mainDataSet.getValue("repayful");if("Y"==repayful){throw new Error(Xjs.ResBundle.getResVal("FT-CPS.00000007"));}return null;},invoke:function(event){var invokeParam=this.buildInvokeParam(null);invokeParam.mains=[];var keyVals=Xjs.util.DataSetUtils.getValuesTo(this.dataSet,0,null);invokeParam.mains.push(keyVals);invokeParam.tgtsheetcode=this.tgtsheetcode;invokeParam.copycode=this.copycode;return invokeParam;},check:function(event){return {type:0,title:this.getTitle(),prompt:this.getResVal("FT-CPS.00000006")};},afterInvoke:function(event){snsoft.plat.bas.viewdetail.ViewDetailListener.invokeViewDetail(event.invokeRtnVal,"upacode");}});snsoft.ft.cps.upa.invoker.CpsUsePayAppNewSheetInvoker=function(parameter){snsoft.ft.cps.upa.invoker.CpsUsePayAppNewSheetInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.cps.upa.invoker.CpsUsePayAppNewSheetInvoker,snsoft.ft.comm.cmdreg.CmdCommInvoker,{beforeInvoke:function(event){var urldefvalues=event.checkData.urldefvalues,fcode=urldefvalues.fcode,sfcode=urldefvalues.sfcode;if(fcode==sfcode){throw new Error(Xjs.ResBundle.getResVal("FT-CPS.00000003"));}return null;}});snsoft.ft.cps.upa.invoker.CpsUsePayAppOpenInvoker=function(parameter){snsoft.ft.cps.upa.invoker.CpsUsePayAppOpenInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.cps.upa.invoker.CpsUsePayAppOpenInvoker,snsoft.ft.comm.cmdreg.CmdCommInvoker,{invoke:function(event){var param={};param.upaicode=this.dataSet.getValue("upaicode");return param;},afterInvoke:function(event){var upaicodes=event.invokeRtnVal.upaicodes;if(!upaicodes){throw new Error(Xjs.ResBundle.getResVal("FT-CPS.00000009"));}else {var sheetService=Xjs.RInvoke.newBean("SN-Busi.SheetService"),busiObject=sheetService.getBusiObject("FT-CPS.CpsUsePayApp"),data={sheetcode:busiObject.sheetcode};data[busiObject.innerfld]=upaicodes;snsoft.plat.bas.viewdetail.ViewDetailListener.invokeViewDetail(data,busiObject.outerfld);}}});snsoft.ft.cps.upa.invoker.CpsUsePayAppReTfloatAdjInvoker=function(parameter){snsoft.ft.cps.upa.invoker.CpsUsePayAppReTfloatAdjInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.cps.upa.invoker.CpsUsePayAppReTfloatAdjInvoker,snsoft.ext.cmd.com.biz.SaveInvoker,{check:function(event){return this.newDialogWithFunc(event,(e)=>{var dlg=Xjs.ui.UIUtil.loadDialog("FT-CPS.CpsUsePayAppTfloatAdjDlg");dlg.title=this.cfg.title;dlg.showModal();return dlg;},(e,d)=>{var t=d.getItemByName("ft_cps_upa");t.postPending();t.checkCurRowDataNonBlank(t.dataSet);event.checkData.showdlg=d;});},invoke:function(event){var showdlg=event.checkData.showdlg;if(!showdlg)return null;var dlgTable=showdlg.getItemByName("ft_cps_upa"),upaicode=this.mainDataSet.getValue("upaicode"),tfloatadj=dlgTable.dataSet.getValue("tfloatadj"),p={};p.upaicode=upaicode;p.tfloatadj=tfloatadj;return p;},afterInvoke:function(event){this.table.refreshTable();}});snsoft.ft.cps.upa.invoker.CpsUserPayAppAddUpagInvoker$DialogTableRefreshListener=function(dlogTable,params){this.dlogTable=dlogTable;this.refreshParams=params;};Xjs.extend(snsoft.ft.cps.upa.invoker.CpsUserPayAppAddUpagInvoker$DialogTableRefreshListener,Xjs.table.DefaultListener,{dataSetRefreshing:function(dataSet,e){if(!e.refreshParam)e.refreshParam={};Xjs.apply(e.refreshParam,this.refreshParams);}});snsoft.ft.cps.upa.invoker.CpsUserPayAppAddUpagInvoker=function(parameter){snsoft.ft.cps.upa.invoker.CpsUserPayAppAddUpagInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.cps.upa.invoker.CpsUserPayAppAddUpagInvoker,snsoft.ext.cmd.CommandInvoker,{beforeCheck:Xjs.nullFn,check:function(event){var initParams={},readonlyParams=[];initParams.bcode=this.mainDataSet.getValue("bcode");readonlyParams.push("bcode");initParams.corpbcode=this.mainDataSet.getValue("corpbcode");readonlyParams.push("corpbcode");initParams.fcode=this.mainDataSet.getValue("fcode");readonlyParams.push("fcode");this.readonlyParams=readonlyParams;this.initParams=initParams;return this.newDialogWithFunc(event,(e)=>{var dialogPane=Xjs.ui.UIUtil.loadDialog("FT-CPS.CpsUsePayFlowRstWorkBenchDlg",0,null,null,null);dialogPane.setWidth("auto");dialogPane.setHeight("80%");dialogPane.title=this.cfg.title;var dlgtbl=Xjs.util.TableUtils.getAllTablesFromComponent(dialogPane,false)[0];dialogPane.addListener("onShowing",new Xjs.FuncCall(this.resetOnShowing,this,[dlgtbl]));if(!dlgtbl.getListener(snsoft.ft.cps.upa.invoker.CpsUserPayAppAddUpagInvoker$DialogTableRefreshListener)){var reflistener=new snsoft.ft.cps.upa.invoker.CpsUserPayAppAddUpagInvoker$DialogTableRefreshListener(dlgtbl,this.refreshParams);dlgtbl.addListener(reflistener);}var queryPane=dialogPane.getItemByName("query");for(var name in initParams){queryPane.getItemByName(name).setValue(initParams[name]);}for(var i=0;i<readonlyParams.length;i++){queryPane.getItemByName(readonlyParams[i]).setReadonly(true);}dialogPane.showModal();return dialogPane;},(e,d)=>{var fiodlgTable=d.getItemByName("ft_cps_fiodlg"),dialgRows=fiodlgTable.getSelectedRowNumbers();if(dialgRows.length==0){throw new Error(Xjs.ResBundle.getResVal("FT.00000048"));}var fiodlgDataSet=fiodlgTable.getDataSet();fiodlgDataSet.postRow();var param={},fioicodefcymap={};for(var i=0;i<dialgRows.length;i++){var thismfcy=fiodlgDataSet.getValue("thismfcy",dialgRows[i]);if(thismfcy==null||thismfcy=="")continue;fioicodefcymap[fiodlgDataSet.getValue("fioicode",dialgRows[i]).toString()]=fiodlgDataSet.getValue("thismfcy",dialgRows[i]);}param.upaicode=this.mainDataSet.getValue("upaicode");param.fioicodefcymap=fioicodefcymap;event.checkData.param=param;});},invoke:function(event){var parameter={};parameter.param=event.checkData.param;return parameter;},afterInvoke:function(event){this.mainTable.refreshTableIfOK();},resetOnShowing:function(dlg,dlgtbl){dlgtbl.refreshTableIfOK();}});Xjs.namespace("snsoft.ft.cps.upa.lis");snsoft.ft.cps.upa.lis.CpsUpagCalcScyByFcyListener=function(params){snsoft.ft.cps.upa.lis.CpsUpagCalcScyByFcyListener.superclass.constructor.call(this,params);};Xjs.extend(snsoft.ft.cps.upa.lis.CpsUpagCalcScyByFcyListener,snsoft.plat.bas.busi.SystemFunctionListener,{dataSetFieldPosted:function(dataSet,event){snsoft.ft.cps.upa.lis.CpsUpagCalcScyByFcyListener.superclass.dataSetFieldPosted.call(this,dataSet,event);if("fcy"==event.columnName)this.setScyValue(dataSet);},setScyValue:function(dataSet){var fcy=dataSet.getValue("fcy"),fserate=dataSet.master.getValue("fserate");if(fcy!=null&&fserate!=null){var table=dataSet.getListener(Xjs.table.Table),scale=table.getColumn("scy").maxDecimals;dataSet.setValue("scy",this.round(fcy*fserate,scale));}else dataSet.setValue("scy",null);}});