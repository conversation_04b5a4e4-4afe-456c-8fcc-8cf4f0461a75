# FT-PAY系统异常资源文件
FT-PAY.00000000=〖%0〗为空，请检查！
FT-PAY.00000001=是否确定已提交？
FT-PAY.00000002=勾选数据都已提交，禁止操作！
FT-PAY.00000003=勾选行数据不得存在“%0”小于等于0的数据！
FT-PAY.00000004=%0为空，请检查！
FT-PAY.00000005=存在非预付款数据，禁止操作！
FT-PAY.00000006=%0必须为‘%1’且%2必须为‘%3’
FT-PAY.00000007=申请支付金额与资金来源汇总金额不相等，请检查！
FT-PAY.00000008=资金来源为其他部门现汇且未生成来源单据时，才能生成资金池用款申请！
FT-PAY.00000009=资金来源为锁汇交割且未生成来源单据时，才能生成外汇交割申请！
FT-PAY.00000010=资金来源行必录项不能为空！
FT-PAY.00000011=确认新建资金池用款申请单吗？
FT-PAY.00000012=确认新建外汇交割申请单吗？
FT-PAY.00000013=当前资金来源行已生成来源单据%0，不可删除！
FT-PAY.00000014=存在状态非60：待送接口数据，禁止操作！
FT-PAY.00000015=勾选数据存在无手动推送资金的记录，禁止操作！
FT-PAY.00000016=%0字段值不相同，禁止操作！
FT-PAY.00000017=直联和非直联票据不能合并付款，请检查！
FT-PAY.00000018=付款银行不可为空！
FT-PAY.00000019=当前收款方账号/银行非有效状态，请重新选择收款方账号/银行！
FT-PAY.00000020=客商%0已纳入黑名单，请注意审核是否可以继续操作！
FT-PAY.00000021=”是否自动推资金”为‘否’，请手动推送付款指令！
FT-PAY.00000022=收款方账号/银行为虚拟银行，若非第三方支付平台，请联系资金人员确认银行信息无误后再提交！
FT-PAY.00000023=“%0”合计值同“%1”必须相等！
FT-PAY.00000024=〖%0〗“%1”合计值同“%2”必须相等！
FT-PAY.00000025=“%0”字段中存在资金付款特殊字符校验‘%1’，请检查！
FT-PAY.00000026=非应付ABS业务，禁止操作！
FT-PAY.00000027=仅支持勾选一条数据，禁止操作！
FT-PAY.00000028=单据创建方式非‘入口手工新建’不允许手工红冲，请检查！
FT-PAY.00000029=勾选行存在非最新单据数据！
FT-PAY.00000030=单据存在下游单据不允许红冲，请检查！
FT-PAY.00000031=%0，%1，项下存在相同金额的付款申请单%2，是否重复付款！
FT-PAY.00000032=付款申请单已被实付，禁止操作！
FT-PAY.00000033=跨境支付需上传监管材料，请检查！
FT-PAY.00000034=合同号%0，累计付款金额%1，大于合同原币金额%2，禁止操作！
FT-PAY.00000035=单据存在下游单据不允许删除，请检查！
FT-PAY.00000036=非预付款，禁止操作！
FT-PAY.00000037=已存在赋予冲销单%0,确认新建预付冲销单？
FT-PAY.00000038=勾选记录中存在可冲销金额为0的记录，禁止操作！
FT-PAY.00000039=勾选记录中存在支付金额为0的记录，禁止操作！
FT-PAY.00000040=已存在付款调整单%0，确认新建付款调整单？
FT-PAY.00000041=锁汇银行与跨境付款银行不一致，请确认银行信息无误后再提交！
FT-PAY.00000042=付款关联采购合同%0为非有效数据，请检查！
FT-PAY.00000043=收款方为空，请检查！
FT-PAY.00000044=序号：%0,为不可调整数据，禁止操作！
FT-PAY.00000045=序号：%0，项下无余额可以调整，禁止操作！
FT-PAY.00000046=存在未提交的【%0】%1,确定新建%0？
FT-PAY.00000047=序号：%0,为不可冲销数据，禁止操作！
FT-PAY.00000048=序号：%0，项下无余额可以冲销，禁止操作！
FT-PAY.00000049=本次使用金额不得大于待付款金额，请检查！
FT-PAY.00000050=存在基于当前单据生成的资金来源单据，请先删除！
FT-PAY.00000051=【%0】不支持批量生成！
FT-PAY.00000052=退款需退回客户原付款账号，请检查！
FT-PAY.00000053=1、“是否模拟”为‘否’，且“未生效时可引用”为‘是’，且单据为可修改状态才可生成【外汇交割申请单】；\n2、“是否模拟”为‘是’，且“是否资金来源发送ATS”为‘否’，且单据为生效状态才可生成【外汇交割申请单】
FT-PAY.00000054=申请支付币种非‘CNY’，禁止操作！
FT-PAY.00000055=申请支付方式必须为‘银行承兑汇票背书’或‘商业承兑汇票背书’或‘类票据背书’，禁止操作！
FT-PAY.00000056=非内部交易业务，禁止操作！
FT-PAY.00000057=%0票据不可拆分，需全额申请背书！
FT-PAY.00000058=非应收票据，禁止操作！
FT-PAY.00000059=%0票据不可拆分，需删除此票据编号的所有记录!
FT-PAY.00000060=非跨境支付业务，禁止操作！
FT-PAY.00000061=非开具应付票据付款业务，禁止操作！
FT-PAY.00000062=当前数据列表中已存在采购合同号%0，请先删除其他行记录，再操作当前按钮！
FT-PAY.00000063=非合同预付款，禁止操作！
FT-PAY.00000064=本部门现汇金额需大于等于【付款申请单】〖资金来源〗为“本部门现汇”行的金额！
FT-PAY.00000065=已存在支付记录【Srcsyscode：%0，Srcsys：%1】，禁止重复！
FT-PAY.00000066=付款申请记录【paicode：%0，nstctranno：%1】不存在，禁止操作！
FT-PAY.00000067=当前记录已被下游单据引用，禁止操作！
FT-PAY.00000068=接口返回错误！
FT-PAY.00000069=1、“是否模拟”为‘否’，且“未生效时可引用”为‘是’，且单据为可修改状态才可生成【资金池用款申请单】；\n2、“是否模拟”为‘是’，且“是否资金来源发送ATS”为‘否’，且单据为生效状态才可生成【资金池用款申请单】
FT-PAY.00000070=勾选行存在未制作过凭证的单据！
FT-PAY.00000071=未查询到%0对应的支付记录信息！
FT-PAY.00000072=〖%0〗为空，请先查询数据！
FT-PAY.00000073=是否确定导出？
FT-PAY.00000074=收付项目非预付货款，不支持导入！
FT-PAY.00000075=采购合同号%0、采购业务编号%1无，请核对！
FT-PAY.00000076=调出金额%0, 调入金额%1不相等，禁止操作！
FT-PAY.00000077=〖调出信息〗币种%0,〖调入信息〗采购合同号%1+币种%2,不相同，请核对！
FT-PAY.00000078=跨境支付场景〖跨境支付信息〗中“收款方英文户名”/“开户行贸易国别(地区)”不能为空！
FT-PAY.00000079=货款、费用不能合并一个付款申请单进行支付，请检查！
FT-PAY.00000080=支付记录【Srcsyscode：%0，Srcsys：%1】不存在，请检查！
FT-PAY.00000081=〖%0〗第%1行数据:\n%2
FT-PAY.00000082=该事业部本周计划收支净额%0，实际收支净额%1，当前执行差额%2，超计划金额%3
FT-PAY.00000083=该事业部本周计划收支净额%0，实际收支净额%1，当前执行差额%2，未超计划
FT-PAY.00000084=预付不得同应付合并生成一个付款调整单！
FT-PAY.00000085=付款申请单【paicode：%0，nstctranno：%1】非生效状态，禁止推送！
FT-PAY.00000086=根据应收票据认领单内码rdocicode[%0]获取【票据登记】数据有误！
FT-PAY.00000087=根据票据号doccode[%0]获取【票据登记】数据有误！
FT-PAY.00000088=存在状态不可推送资金的数据，禁止操作！
FT-PAY.00000089=当前单据状态不允许推送资金，禁止操作！
FT-PAY.00000090=当前单据禁止推送资金！
FT-PAY.00000091=预付款禁止修改是否自动推资金，请检查！
FT-PAY.00000092=存在是否有追索权不同值集的记录，禁止操作！
FT-PAY.00000093=票据金额合计值：%0，同申请支付金额：%1必须相等！
FT-PAY.00000094=票据登记行必录项不能为空！
FT-PAY.00000095=〖调出信息〗业务员部门：%0+公司：%1+收款方：%2,同〖调入信息〗采购合同号：%3+采购业务编号：%4关联的业务员部门、公司、收款方,不相同，请核对！
FT-PAY.00000096=%0发起的%1：%2，客户名称：%3，申请金额：%4 %5；已经审批通过。其相关付款申请【%6】需手动推送资金，请知悉!
FT-PAY.00000097=发送Elink消息出错！
FT-PAY.00000098=R08内部贴现查询接口调用报错，禁止结清！
FT-PAY.00000099=存在预付款或多笔发票，禁止操作！
FT-PAY.00000100=【%0】不存在，请重新选择！
FT-PAY.00000101=定时任务号：%0，其他参数未配置：%1
FT-PAY.00000102=您好，付款申请单号%0未提交保税企业监管材料！

