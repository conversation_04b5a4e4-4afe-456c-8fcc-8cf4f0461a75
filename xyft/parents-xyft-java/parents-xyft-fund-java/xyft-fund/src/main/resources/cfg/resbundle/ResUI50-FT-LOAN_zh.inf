## 单据号
title_FT-LOAN.LoanInnerIrt=内部借款利息计提单
title_FT-LOAN.AccOrdFinaApp=赎单融资申请单
title_FT-LOAN.AccOrdFinaBack=赎单融资还款申请单
title_FT-FT-LOAN.AccOrdFinaBack=赎单融资还款申请单
title_FT-LOAN.AccOrdFinaEx=赎单融资展期申请单
title_FT-LOAN.AccOrdFinaAppLIVMark=赎单融资放款凭证标记表
title_FT-LOAN.AccOrdFinaAppRIVMark=赎单融资还款凭证标记表
title_FT-LOAN.AccOrdFinaAppLI=赎单融资放款
title_FT-LOAN.AccOrdFinaAppRI=赎单融资还款
title_FT-LOAN.DocuDeliveryFinaApp=交单融资申请单
title_FT-FOREX.ForexBvalVMark=银行估值表凭证
title_FT-LOAN.DocuDeliveryFinaLIVMark=交单融资放款凭证标记表
title_FT-LOAN.DocuDeliveryFinaRIVMark=交单融资还款凭证标记表
title_FT-LOAN.DocuDeliveryFinaLI=交单融资放款
title_FT-LOAN.DocuDeliveryFinaRI=交单融资还款
title_FT-LOAN.FactFinaApp=保理融资申请
title_FT-LOAN.FactFinaLoanInfo=保理融资放款信息
title_FT-LOAN.FactFinaRepayInfo=保理融资还款信息
title_FT-LOAN.ABSFinaBatchReg=ABS批次号登记
## 入口
title_FT-LOAN.LoanInnerIrtEntry=内部借款利息计提单-入口
title_FT-LOAN.AccOrdFinaExEntry=赎单融资展期申请单-入口
title_FT-LOAN.DocuDeliveryFinaAppEntry=交单融资申请单-入口
title_FT-LOAN.FactFinaAppEntry=保理融资申请单-入口
title_FT-LOAN.FactFinaHandleWorkbench=保理融资处理工作台

## 工作台
FT-LOAN.AccOrdFinaExWorkBench=赎单融资展期申请工作台
FT-LOAN.AccOrdFinaHandleWorkBench=赎单融资处理工作台

title_FT-LOAN.AccOrdFinaSimWorkBench=模拟利息计提工作台
title_FT-LOAN.AccOrdFinaAppEntry=赎单融资申请单-入口
title_FT-LOAN.AccOrdFinaBackEntry=赎单融资还款申请单-入口
title_FT-LOAN.AccOrdFinaBackWorkBench=赎单融资还款展期工作台
title_FT-LOAN.AccOrdFinaAppWorkBench=赎单融资工作台
title_FT-LOAN.AccOrdFinaHandleWorkBench=赎单融资处理工作台
title_FT-LOAN.DocuDeliveryFinaAppWorkBench=交单融资工作台
title_FT-LOAN.DocuDeliveryFinaHandleWorkBench=交单融资处理工作台
title_FT-LOAN.FactFinaHandleWorkBench=保理融资处理工作台

## tab标题
FT.LOAN.title.tab.bas=${FT.title.grp.bas1}
FT.LOAN.title.tab.financingRepayment=融资还款信息
FT.LOAN.title.tab.financingRepaymentBas=${FT.title.grp.bas1}
FT.LOAN.title.tab.financingRepaymentSys=${FT.title.grp.sys}
FT.LOAN.title.tab.financingRepaymentFpsrc=资金来源

FT.LOAN.title.tab.deptdetail=部门融资金额明细
FT.LOAN.title.tab.sharedetail=融资明细
FT.LOAN.title.tab.handleinfo=办理信息
FT.LOAN.title.tab.lendinfo=放款信息
FT.LOAN.title.tab.lendinfoshare=放款分摊明细
FT.LOAN.title.tab.repinfo=还款信息
FT.LOAN.title.tab.repinfoshare=还款分摊明细
FT.LOAN.title.tab.exinfo=展期信息
FT.LOAN.title.tab.bfee=费用信息
FT.LOAN.title.tab.bfeedetail=费用明细
FT.LOAN.title.tab.rclmg=认领明细

## 详情
title_FT-LOAN.LoanInnerIrtDetail=内部借款利息计提单
title_FT-LOAN.AccOrdFinaExDetail=赎单融资展期申请单
title_FT-LOAN.DocuDeliveryFinaAppDetail=交单融资申请单
title_FT-LOAN.FactFinaAppDetail=保理融资申请单

title_FT-LOAN.AccOrdFinaAppDetail=赎单融资申请单
title_FT-LOAN.AccOrdFinaBackDetail=赎单融资还款申请单
## 按钮名称
FT.cmd_pushInvoicePlat=推送待开票平台
FT.cmd_sendPipCancel=取消推送
FT.cmd_dataswitching=数据显示切换
FT.cmd_generateaccordfina=生成融资还款申请单
FT.cmd_sendSerCancel=已送接口撤回
FT.cmd_createAccOrdFinaBack=生成赎单融资还款申请单
FT.cmd_addFinancingRepayment=追加赎单融资申请数据
FT.cmd_createAccOrdFinaExtend=生成赎单融资展期申请单
FT.cmd_createAccOrdFina=生成赎单融资申请单
FT.cmd_createDocuDeliveryFina=生成交单融资申请单
FT.cmd_generateVoucher=生成凭证
FT.cmd_redVoucher=红冲
FT.cmd_generateRedVoucher=生成红冲凭证
FT.cmd_openDocuDeliveryFina=打开交单融资申请单
FT.cmd_generatePayApp=生成付款申请单
FT.cmd_openPayApp=打开付款申请单
FT.cmd_genPrePay=生成预付款
FT.cmd_genInv=生成发票


## tab
FT.title.tab.handleInfo=办理信息

## 工作台
title_FT-LOAN.LoanInnerIrtWorkBench=内部借款利息申开工作台
title_FT-LOAN.AccOrdFinaExWorkBench=内部借款利息申开工作台


## group 分组
FT.LOAN.title.grp.basinfo=${FT.title.grp.bas1}
FT.LOAN.title.grp.loaninfo=借款信息
FT.LOAN.title.grp.sys=${FT.title.grp.sys}
FT.LOAN.title.grp.finextendinfo=融资展期信息
FT.LOAN.title.grp.factfinalrpinfo=往来信息
FT.LOAN.title.grp.factfinafeeinfo=费用信息
FT.LOAN.title.grp.loaninfo=放款信息
FT.LOAN.title.grp.repayinfo=还款信息

FT.LOAN.title.grp.lendinfo=融资信息
FT.LOAN.title.grp.acdfininfo=赎单信息
FT.LOAN.title.grp.appdeptdetail=部门融资金额明细
FT.LOAN.title.grp.dlydinfo=议付交单信息
FT.LOAN.title.grp.dlydfainfo=融资申请信息
FT.LOAN.title.grp.dlyddetail=交单明细
