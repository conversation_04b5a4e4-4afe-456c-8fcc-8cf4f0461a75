Xjs.loadedXjs.push("snsoft/ft/fund/lc/open");
/*snsoft/ft/lc/open/app/invoker/ClosedBookApplicationInvoker.java*/
Xjs.namespace("snsoft.ft.lc.open.app.invoker");
snsoft.ft.lc.open.app.invoker.ClosedBookApplicationInvoker=function(parameter){
    snsoft.ft.lc.open.app.invoker.ClosedBookApplicationInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.lc.open.app.invoker.ClosedBookApplicationInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.lc.open.app.invoker.ClosedBookApplicationInvoker",
    /*snsoft.ft.lc.open.app.invoker.ClosedBookApplicationInvoker.invoke*/
    invoke:function(event)
    {
        var invokeParam = this.buildInvokeParam(null);
        return invokeParam;
    },
    /*snsoft.ft.lc.open.app.invoker.ClosedBookApplicationInvoker.check*/
    check:function(event)
    {
        var rows = this.table.getSelectedRowNumbers();
        if(rows == null || rows.length == 0)
        {
            throw new Error(Xjs.ResBundle.getResVal("FT.********"));
        }
        for(var i=0;i < rows.length;i++)
        {
            var r = rows[i],
                status = this.dataSet.getValue("status",r);
            if(!(status == "70"))
            {
                throw new Error(Xjs.ResBundle.getResVal("FT-LC.********"));
            }
        }
        return null;
    },
    /*snsoft.ft.lc.open.app.invoker.ClosedBookApplicationInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        this.dataSet.refresh();
        this.mainTable.refreshTableIfOK();
    }
});
/*snsoft/ft/lc/open/app/invoker/ClosedBookRevokeInvoker.java*/
snsoft.ft.lc.open.app.invoker.ClosedBookRevokeInvoker=function(parameter){
    snsoft.ft.lc.open.app.invoker.ClosedBookRevokeInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.lc.open.app.invoker.ClosedBookRevokeInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.lc.open.app.invoker.ClosedBookRevokeInvoker",
    /*snsoft.ft.lc.open.app.invoker.ClosedBookRevokeInvoker.invoke*/
    invoke:function(event)
    {
        var invokeParam = this.buildInvokeParam(null);
        return invokeParam;
    },
    /*snsoft.ft.lc.open.app.invoker.ClosedBookRevokeInvoker.check*/
    check:function(event)
    {
        var rows = this.table.getSelectedRowNumbers();
        if(rows == null || rows.length == 0)
        {
            throw new Error(Xjs.ResBundle.getResVal("FT.********"));
        }
        for(var i=0;i < rows.length;i++)
        {
            var r = rows[i],
                status = this.dataSet.getValue("status",r);
            if(!(status == "91") && !(status == "75"))
            {
                throw new Error(Xjs.ResBundle.getResVal("FT-LC.00000019"));
            }
        }
        return null;
    },
    /*snsoft.ft.lc.open.app.invoker.ClosedBookRevokeInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        this.dataSet.refresh();
        this.mainTable.refreshTableIfOK();
    }
});
/*snsoft/ft/lc/open/app/invoker/LcAppSyncContractInvoker.java*/
snsoft.ft.lc.open.app.invoker.LcAppSyncContractInvoker=function(parameter){
    snsoft.ft.lc.open.app.invoker.LcAppSyncContractInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.lc.open.app.invoker.LcAppSyncContractInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.lc.open.app.invoker.LcAppSyncContractInvoker",
    /*snsoft.ft.lc.open.app.invoker.LcAppSyncContractInvoker.invoke*/
    invoke:function(event)
    {
        var purordicodeList = [];
        for(var i=0;i < this.dataSet.getRows().length;i++)
        {
            purordicodeList[i] = this.dataSet.getValue("lcagicode",i) + "," + this.dataSet.getValue("purordicode",i);
        }
        return {purordicodeList:purordicodeList,lcaicode:this.mainDataSet.getValue("lcaicode"),modifydate:this.mainDataSet.get("modifydate")};
    },
    /*snsoft.ft.lc.open.app.invoker.LcAppSyncContractInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        this.dataSet.refresh();
        this.mainTable.refreshTableIfOK();
    }
});
/*snsoft/ft/lc/open/app/lis/CleanValueLcAppJSListener.java*/
Xjs.namespace("snsoft.ft.lc.open.app.lis");
snsoft.ft.lc.open.app.lis.CleanValueLcAppJSListener=function(params){
    snsoft.ft.lc.open.app.lis.CleanValueLcAppJSListener.superclass.constructor.call(this,params);
};
Xjs.extend(snsoft.ft.lc.open.app.lis.CleanValueLcAppJSListener,snsoft.plat.bas.busi.SystemFunctionListener,{
  _js$className_:"snsoft.ft.lc.open.app.lis.CleanValueLcAppJSListener",
    /*snsoft.ft.lc.open.app.lis.CleanValueLcAppJSListener.initComponent*/
    initComponent:function(table,values)
    {
        snsoft.ft.lc.open.app.lis.CleanValueLcAppJSListener.superclass.initComponent.call(this,table,values);
        if(this.postInfos)
        {
            for(var i=0;i < this.postInfos.length;i++)
            {
                var t = table.getRootComponent().getMainComponentByName(this.postInfos[i].uiname);
                if(t)
                {
                    t.dataSet.addListener(this);
                }
            }
        }
    },
    /*snsoft.ft.lc.open.app.lis.CleanValueLcAppJSListener.dataSetFieldPosted*/
    dataSetFieldPosted:function(dataSet,event)
    {
        if(this.postInfos && !dataSet.get("vsntype"))
        {
            for(var i=0;i < this.postInfos.length;i++)
            {
                if(dataSet.name == this.postInfos[i].uiname && this.postInfos[i].colnames && this.postInfos[i].colnames.indexOf(event.columnName) > -1 && (!this.cleanIfNull || dataSet.getValue(event.columnName) == null))
                {
                    this.cleanValue(dataSet,this.postInfos[i]);
                }
            }
        }
    },
    /*snsoft.ft.lc.open.app.lis.CleanValueLcAppJSListener.cleanValue*/
    cleanValue:function(dataSet,info)
    {
        if(info.infos)
        {
            for(var i=0;i < info.infos.length;i++)
            {
                var pinfo = info.infos[i],
                    table = Xjs.table.Table.getTablesOfDataSet(dataSet)[0].getRootComponent().getMainComponentByName(pinfo.uiname);
                if(pinfo.colnames)
                {
                    if(!pinfo.uiname)
                    {
                        this.cleanColumnValues(dataSet,pinfo.colnames);
                    } else 
                    {
                        this.cleanDataSetValues(table.dataSet,pinfo);
                    }
                } else 
                {
                    table.dataSet.deleteAllRows();
                }
            }
        }
    },
    /*snsoft.ft.lc.open.app.lis.CleanValueLcAppJSListener.cleanColumnValues*/
    cleanColumnValues:function(dataSet,colnames)
    {
        for(var i=0;i < colnames.length;i++)
        {
            dataSet.setValue(colnames[i],null);
        }
    },
    /*snsoft.ft.lc.open.app.lis.CleanValueLcAppJSListener.cleanDataSetValues*/
    cleanDataSetValues:function(dataSet,pinfo)
    {
        for(var r=0;r < dataSet.getRowCount();r++)
        {
            dataSet.gotoRow(r);
            this.cleanColumnValues(dataSet,pinfo.colnames);
            this.cleanValue(null,pinfo);
        }
    }
});
/*snsoft/ft/lc/open/app/lis/LcAppJSListener.java*/
snsoft.ft.lc.open.app.lis.LcAppJSListener=function(params){
    snsoft.ft.lc.open.app.lis.LcAppJSListener.superclass.constructor.call(this,params);
};
Xjs.extend(snsoft.ft.lc.open.app.lis.LcAppJSListener,snsoft.plat.bas.busi.SystemFunctionListener,{
  _js$className_:"snsoft.ft.lc.open.app.lis.LcAppJSListener",
    /*snsoft.ft.lc.open.app.lis.LcAppJSListener.initComponent*/
    initComponent:function(table,values)
    {
        snsoft.ft.lc.open.app.lis.LcAppJSListener.superclass.initComponent.call(this,table,values);
        this.mainTable = table;
        if(!this.isfundList)
        {
            this.isfundList = window.EnvParameter._ISFUND;
        }
        if(!this.ftlcappTable)
        {
            this.ftlcappTable = this.getTable(table,"ftlcappbas2");
        }
        if(!this.ftlcappgTable)
        {
            this.ftlcappgTable = this.getTable(table,"ft_lc_appg");
        }
        if(!this.vbhistory)
        {
            this.vbhistory = this.getTable(table,"sysinfo_vsn");
        }
    },
    /*snsoft.ft.lc.open.app.lis.LcAppJSListener.dataLoaded*/
    dataLoaded:function(dataSet,event)
    {
        snsoft.ft.lc.open.app.lis.LcAppJSListener.superclass.dataLoaded.call(this,dataSet,event);
        this.mainTable.render(2);
        this.ftlcappTable.render(2);
        this.ftlcappgTable.render(2);
    },
    /*snsoft.ft.lc.open.app.lis.LcAppJSListener.setTableRdonly*/
    setTableRdonly:function()
    {
        var isinit = this.mainTable.getDataSet().getValue("isinit");
        if(isinit == "Y")
        {
            this.ftlcappTable.getColumn("pcacode").nonBlankOnSubmit = false;
            this.ftlcappTable.getColumn("bankcode").nonBlankOnSubmit = false;
            this.ftlcappTable.getColumn("alctype").nonBlankOnSubmit = false;
        } else 
        {
            this.ftlcappTable.getColumn("pcacode").nonBlankOnSubmit = true;
            this.ftlcappTable.getColumn("bankcode").nonBlankOnSubmit = true;
            this.ftlcappTable.getColumn("alctype").nonBlankOnSubmit = true;
        }
        var vsntype = this.vbhistory.getDataSet().getValue("vsntype"),
            status = this.mainTable.getDataSet().getValue("status");
        if(status > 20 || vsntype)
        {
            for(var i=0;i < this.ftlcappTable.getColumns().length;i++)
            {
                this.ftlcappTable.getColumns()[i].setReadonly(true);
            }
            for(var i=0;i < this.ftlcappgTable.getColumns().length;i++)
            {
                this.ftlcappgTable.getColumns()[i].setReadonly(true);
            }
        } else 
        {
            for(var i=0;i < this.ftlcappTable.getColumns().length;i++)
            {
                this.ftlcappTable.getColumns()[i].setReadonly(false);
            }
            var lcappReadonlyClom = ["priceterm","fcy","days","fcode","purccode","bankcode","alctype","isfundcf"];
            for(var i=0;i < lcappReadonlyClom.length;i++)
            {
                var column = this.ftlcappTable.getColumn(lcappReadonlyClom[i]);
                column.setReadonly(true);
            }
            var lcappgReadonlyClom = ["loadingportlist","dischargeportlist","fcy"];
            for(var i=0;i < lcappgReadonlyClom.length;i++)
            {
                var column = this.ftlcappgTable.getColumn(lcappgReadonlyClom[i]);
                column.setReadonly(false);
            }
            var lctype = this.vbhistory.getDataSet().getValue("lctype");
            if("0060" == lctype || "0070" == lctype)
            {
                var column = this.ftlcappTable.getColumn("days");
                column.nonBlankOnSubmit = false;
            } else if("0065" == lctype || "0075" == lctype)
            {
                var column = this.ftlcappTable.getColumn("days");
                column.nonBlankOnSubmit = true;
            }
        }
    },
    /*snsoft.ft.lc.open.app.lis.LcAppJSListener.initOpectrl*/
    initOpectrl:function(dataSet)
    {
        var lccorpbcode = this.ftlcappTable.getColumn("lccorpbcode");
        if(dataSet.get("isreplc") == "Y")
        {
            lccorpbcode.setReadonly(false);
        } else if(dataSet.get("isreplc") == "N")
        {
            dataSet.setValue("lccorpbcode",dataSet.getValue("corpbcode"));
            lccorpbcode.setReadonly(true);
        }
        var overshiprate = this.ftlcappTable.getColumn("overshiprate");
        if(dataSet.get("isosrate") == "Y")
        {
            overshiprate.nonBlankOnSubmit = true;
            overshiprate.setReadonly(false);
        } else if(dataSet.get("isosrate") == "N")
        {
            dataSet.setValue("overshiprate",null);
            overshiprate.nonBlankOnSubmit = false;
            overshiprate.setReadonly(true);
        } else if(!dataSet.get("isosrate") || dataSet.get("isosrate")===undefined)
        {
            dataSet.setValue("overshiprate",null);
            overshiprate.nonBlankOnSubmit = false;
            overshiprate.setReadonly(true);
        }
    },
    /*snsoft.ft.lc.open.app.lis.LcAppJSListener.vsnOpectrl*/
    vsnOpectrl:function()
    {
        var data = window.EnvParameter._VBTYPES_,
            vsntype = this.vbhistory.getDataSet().getValue("vsntype"),
            status = this.mainTable.getDataSet().getValue("status");
        if("S01"==vsntype && status < 20)
        {
            for(var i=0;i < this.isfundList.length;i++)
            {
                var jsObject = this.isfundList[i],
                    readonly = jsObject.var02 == "Y" ? false : true,
                    tableName = jsObject.var04,
                    colName = jsObject.var05;
                if(tableName == "ft_lc_app")
                {
                    var column = this.ftlcappTable.getColumn(colName);
                    column.setReadonly(readonly);
                } else if(tableName == "ft_lc_appg")
                {
                    var column = this.ftlcappgTable.getColumn(colName);
                    column.setReadonly(readonly);
                }
            }
        }
        if(data && vsntype && vsntype==data.S05 && status < 20)
        {
            var lcappReadonlyClom = ["lctype","isosrate","vddate","beneccode","linkmanmobile","lsdate","linkman"];
            for(var i=0;i < lcappReadonlyClom.length;i++)
            {
                var column = this.ftlcappTable.getColumn(lcappReadonlyClom[i]);
                column.setReadonly(false);
            }
            var lcappgReadonlyClom = ["loadingportlist","dischargeportlist","fcy"];
            for(var i=0;i < lcappgReadonlyClom.length;i++)
            {
                var column = this.ftlcappgTable.getColumn(lcappgReadonlyClom[i]);
                column.setReadonly(false);
            }
        }
    },
    /*snsoft.ft.lc.open.app.lis.LcAppJSListener.dataSetFieldPosting*/
    dataSetFieldPosting:function(dataSet,event)
    {
        snsoft.ft.lc.open.app.lis.LcAppJSListener.superclass.dataSetFieldPosting.call(this,dataSet,event);
        if(event.columnName == "overshiprate" && event.value)
        {
            var overshiprateVal = event.value;
            if(overshiprateVal < 0 || overshiprateVal > 1)
            {
                throw new Error(Xjs.ResBundle.getResVal("FT-LC.00000045"));
            }
        }
    },
    /*snsoft.ft.lc.open.app.lis.LcAppJSListener.dataSetRowNavigated*/
    dataSetRowNavigated:function(dataSet,e)
    {
        this.setTableRdonly();
        this.vsnOpectrl();
        var data = window.EnvParameter._VBTYPES_,
            vsntype = this.vbhistory.getDataSet().getValue("vsntype");
        if(!(data && vsntype && vsntype==data.S06))
        {
            this.initOpectrl(dataSet);
        }
    },
    /*snsoft.ft.lc.open.app.lis.LcAppJSListener.dataSetFieldPosted*/
    dataSetFieldPosted:function(dataSet,event)
    {
        snsoft.ft.lc.open.app.lis.LcAppJSListener.superclass.dataSetFieldPosted.call(this,dataSet,event);
        if(event.columnName == "vsntype" || event.columnName == "status")
        {
            this.setTableRdonly();
            this.vsnOpectrl();
        }
        var lccorpbcode = this.ftlcappTable.getColumn("lccorpbcode");
        if(event.columnName == "isreplc" && dataSet.get("isreplc") == "Y")
        {
            dataSet.setValue("lccorpbcode",null);
            lccorpbcode.setReadonly(false);
        } else if(event.columnName == "isreplc" && dataSet.get("isreplc") == "N")
        {
            dataSet.setValue("lccorpbcode",dataSet.getValue("corpbcode"));
            lccorpbcode.setReadonly(true);
        }
        if(event.columnName == "corpbcode" && dataSet.get("isreplc") == "N")
        {
            dataSet.setValue("lccorpbcode",dataSet.getValue("corpbcode"));
        }
        if(event.columnName == "pcacode")
        {
            if(dataSet.get("pcacode"))
            {
                var tableColumn = this.ftlcappTable.getColumn("bktcode"),
                    dialog = tableColumn.aidInputer;
                dialog.doAidInput(this.ftlcappTable.getItemByName("bktcode"),null,null);
                dialog.hideAidInputer(this.ftlcappTable.getItemByName("bktcode"));
                var datas = dialog.getTable().getDataSet().getRows();
                if(datas != null && datas.length == 1)
                {
                    this.ftlcappTable.dataSet.setValue("bktcode",datas[0][0]);
                    this.ftlcappTable.dataSet.setValue("bkticode",datas[0][3]);
                } else 
                {
                    this.ftlcappTable.dataSet.setValue("bktcode",null);
                    this.ftlcappTable.dataSet.setValue("bkticode",null);
                }
            } else 
            {
                this.ftlcappTable.dataSet.setValue("bankcode",null);
                this.ftlcappTable.dataSet.setValue("bktcode",null);
                this.ftlcappTable.dataSet.setValue("bkticode",null);
                this.ftlcappTable.dataSet.setValue("pcaicode",null);
                this.ftlcappTable.dataSet.setValue("alctype",null);
            }
        }
        var overshiprate = this.ftlcappTable.getColumn("overshiprate");
        if(event.columnName == "isosrate" && dataSet.get("isosrate") == "Y")
        {
            overshiprate.nonBlankOnSubmit = true;
            overshiprate.setReadonly(false);
        } else if(event.columnName == "isosrate" && dataSet.get("isosrate") == "N")
        {
            dataSet.setValue("overshiprate",null);
            overshiprate.nonBlankOnSubmit = false;
            overshiprate.setReadonly(true);
        } else if(event.columnName == "isosrate" && (!dataSet.get("isosrate") || dataSet.get("isosrate")===undefined))
        {
            dataSet.setValue("overshiprate",null);
            overshiprate.nonBlankOnSubmit = false;
            overshiprate.setReadonly(true);
        }
        if(event.columnName == "pcacode" && !dataSet.get("pcacode"))
        {
            dataSet.setValue("pcaicode",null);
            dataSet.setValue("bankcode",null);
            dataSet.setValue("bktcode",null);
            dataSet.setValue("bkticode",null);
            dataSet.setValue("alctype",null);
            dataSet.setValue("days",null);
            dataSet.setValue("plcdate",null);
        }
        this.ftlcappTable.render(2);
    },
    /*snsoft.ft.lc.open.app.lis.LcAppJSListener.itemAidInputing*/
    itemAidInputing:function(table,e)
    {
        snsoft.ft.lc.open.app.lis.LcAppJSListener.superclass.itemAidInputing.call(this,table,e);
        if(e.forTblColumn.name == "pcacode")
        {
            this.setPcacodeDataLoadParams();
        }
    },
    /*snsoft.ft.lc.open.app.lis.LcAppJSListener.setPcacodeDataLoadParams*/
    setPcacodeDataLoadParams:function()
    {
        this.mainTable.saveChanges();
        var pcacodeColumn = this.mainTable.getColumn("pcacode");
        if(pcacodeColumn)
        {
            var aidInputer = pcacodeColumn.aidInputer;
            if(aidInputer instanceof Xjs.ui.SelectTableDataDialog)
            {
                var selectTableDataDialog = aidInputer;
                selectTableDataDialog.addRefreshParam("lcaicode",this.mainTable.dataSet.getValue("lcaicode"));
            }
        }
    }
});
/*snsoft/ft/lc/open/pca/invoker/LcPreCreditAppRelPcFcyInvoker.java*/
Xjs.namespace("snsoft.ft.lc.open.pca.invoker");
snsoft.ft.lc.open.pca.invoker.LcPreCreditAppRelPcFcyInvoker=function(parameter){
    snsoft.ft.lc.open.pca.invoker.LcPreCreditAppRelPcFcyInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.lc.open.pca.invoker.LcPreCreditAppRelPcFcyInvoker,snsoft.plat.bas.sheet.cmd.CommandSheet,{
  _js$className_:"snsoft.ft.lc.open.pca.invoker.LcPreCreditAppRelPcFcyInvoker",
    /*snsoft.ft.lc.open.pca.invoker.LcPreCreditAppRelPcFcyInvoker.check*/
    check:function(event)
    {
        var rows = this.table.getSelectedRowNumbers();
        if(rows == null || rows.length == 0)
        {
            throw new Error(Xjs.ResBundle.getResVal("FT.********"));
        }
        for(var i=0;i < rows.length;i++)
        {
            var r = rows[i],
                status = this.dataSet.getValue("status",r);
            if(status != "70")
            {
                throw new Error(Xjs.ResBundle.getResVal("FT-LC.********"));
            }
        }
        return null;
    }
});
/*snsoft/ft/lc/open/tpl/invoker/LcBankTplStatusInvoker.java*/
Xjs.namespace("snsoft.ft.lc.open.tpl.invoker");
snsoft.ft.lc.open.tpl.invoker.LcBankTplStatusInvoker=function(parameter){
    snsoft.ft.lc.open.tpl.invoker.LcBankTplStatusInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.lc.open.tpl.invoker.LcBankTplStatusInvoker,snsoft.ext.cmd.com.biz.SaveInvoker,{
  _js$className_:"snsoft.ft.lc.open.tpl.invoker.LcBankTplStatusInvoker",
    /*snsoft.ft.lc.open.tpl.invoker.LcBankTplStatusInvoker.beforeCheck*/
    beforeCheck:function(event)
    {
        var rows = this.table.getSelectedRowNumbers();
        if(rows == null || rows.length == 0)
        {
            throw new Error(Xjs.ResBundle.getResVal("FT.********"));
        }
        var btn_name = event.cfg.cmd,
            status_code = btn_name == "start" ? "10" : "70",
            cityBank = {};
        for(var i=0;i < rows.length;i++)
        {
            var r = rows[i],
                status = this.dataSet.getValue("status",r),
                sncitycode = this.dataSet.getValue("sncitycode",r),
                targetStr = this.dataSet.getValue("bankcode",r).toString() + (sncitycode ? sncitycode.toString() : "");
            if(status != status_code)
            {
                throw new Error(Xjs.ResBundle.getResVal("FT-LC.********",status_code == "10" ? "草拟" : "启用"));
            }
            if(targetStr in cityBank)
            {
                throw new Error(Xjs.ResBundle.getResVal("FT-LC.********"));
            }
            cityBank[targetStr] = targetStr;
        }
        return null;
    },
    /*snsoft.ft.lc.open.tpl.invoker.LcBankTplStatusInvoker.invoke*/
    invoke:function(event)
    {
        window.console.log("===============");
        var usercode = window.EnvParameter.USERCODE,
            dataStore = event.mainDataSet.dataStore,
            forceSaveColumns = dataStore ? dataStore.forceSaveColumns : [],
            statusSaveDatas = event.checkData ? event.checkData.statusSaveDatas : {},
            SaveDatas = this.buildMainSaveDatas((nv,row)=>{
            nv.status = this.status;
            nv.modifier = usercode;
            for(var i=0,len=forceSaveColumns.length;i < len;i++)
            {
                var col = forceSaveColumns[i];
                if(!String.isStrIn("modifier,status,modifydate",col))
                {
                    nv[col] = event.mainDataSet.getValue(col);
                }
            }
            if(statusSaveDatas)
            {
                if(Xjs.isArray(statusSaveDatas))
                {
                    var lstatus = statusSaveDatas,
                        keyValues = event.mainDataSet.getKeyValues(row);
                    for(var i=0,len=lstatus.length;i < len;i++)
                    {
                        var v = lstatus[i];
                        if(snsoft.ft.lc.open.tpl.invoker.LcBankTplStatusInvoker.keyEquals(keyValues,v))
                        {
                            Xjs.apply(nv,v);
                            break;
                        }
                    }
                } else 
                    Xjs.apply(nv,statusSaveDatas);
            }
        }),
            sp = {SaveDatas:[],ClientParams:{}};
        sp.SaveDatas = SaveDatas;
        if(this.replaceMode===undefined || this.replaceMode == 0)
            this.replaceMode = 7;
        this.onOkSaveParam(sp,event);
        return null;
    },
    /*snsoft.ft.lc.open.tpl.invoker.LcBankTplStatusInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        if(event.invokeRtnVal)
        {
            snsoft.ft.utils.FTUtils.replaceValueAndGotoDetail(event.table,event.invokeRtnVal.tableRowValues);
        }
    }
});
Xjs.apply(snsoft.ft.lc.open.tpl.invoker.LcBankTplStatusInvoker,{
    /*snsoft.ft.lc.open.tpl.invoker.LcBankTplStatusInvoker.keyEquals*/
    keyEquals:function(keys,v2)
    {
        if(keys == v2)
            return true;
        if(keys == null || v2 == null)
            return false;
        for(var key in keys)
        {
            if(keys[key] != v2[key])
                return false;
        }
        return true;
    }
});
