Xjs.loadedXjs.push("snsoft/ft/fund/fee/irticm");
/*snsoft/ft/fee/irticm/invoker/CreateIrtIncomeRedVfldInvoker.java*/
Xjs.namespace("snsoft.ft.fee.irticm.invoker");
snsoft.ft.fee.irticm.invoker.CreateIrtIncomeRedVfldInvoker=function(parameter){
    snsoft.ft.fee.irticm.invoker.CreateIrtIncomeRedVfldInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.fee.irticm.invoker.CreateIrtIncomeRedVfldInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.fee.irticm.invoker.CreateIrtIncomeRedVfldInvoker",
    /*snsoft.ft.fee.irticm.invoker.CreateIrtIncomeRedVfldInvoker.check*/
    check:function(event)
    {
        var rows = this.table.getSelectedRowNumbers();
        if(!rows || rows.length == 0)
        {
            throw new Error(Xjs.ResBundle.getResVal("FT.00000048"));
        }
        var vmarkicodes = [];
        for(var i=0;i < rows.length;i++)
        {
            var redflag = this.dataSet.getValue("redflag",rows[i]);
            if(redflag == 1 || redflag == 2)
            {
                throw new Error(Xjs.ResBundle.getResVal("FT-PAY.00000029"));
            }
            var vidflag = this.dataSet.getValue("vidflag",rows[i]);
            if(vidflag != 1)
            {
                throw new Error(Xjs.ResBundle.getResVal("FT-PAY.00000070"));
            }
            vmarkicodes.push(this.dataSet.getValue("vmarkicode",rows[i]));
        }
        return this.newDialogWithFunc(event,(e)=>{
            var dialogPane = Xjs.ui.UIUtil.loadDialog("FT-FEE.IrtIncomeRedDlg",0,null,{width:325,height:120},null,null);
            dialogPane.title = this.cfg.title;
            dialogPane.showModal();
            return dialogPane;
        },(e,d)=>{
            var tables = Xjs.util.TableUtils.getAllTablesFromComponent(d,false),
                param = {};
            if(tables && tables.length > 0)
            {
                for(var i=0,len=tables.length;i < len;i++)
                {
                    var t = tables[i];
                    t.postPending();
                    if(!t.dataSet.isChanged(false))
                        t.dataSet.setRowChanged(true);
                    t.checkNonBlankForSubmit();
                    if("irtIncomeRedDlg" == t.name)
                    {
                        param.tcaptime = t.getDataSet().getValue("tcaptime");
                        param.vmarkicodes = vmarkicodes;
                    }
                }
                event.checkData.param = param;
            }
        });
    },
    /*snsoft.ft.fee.irticm.invoker.CreateIrtIncomeRedVfldInvoker.invoke*/
    invoke:function(event)
    {
        return event.checkData.param;
    }
});
