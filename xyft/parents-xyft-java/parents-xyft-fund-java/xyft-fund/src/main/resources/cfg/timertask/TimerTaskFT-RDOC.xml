<?xml version="1.0" encoding="UTF-8"?>
<timertask-list xmlns="http://www.snsoft.com.cn/schema/TimerTask"
                xsi:schemaLocation="http://www.snsoft.com.cn/schema/TimerTask http://www.snsoft.com.cn/schema/TimerTask.xsd"
                xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <timertask id="FT-RDOC.001" name="【票据贴现到期凭证】定时任务" work-time="CRON:0 05 0 * * ?" interval="10" servhost="timer*" auto-start="true" disabled="false" thread="true" task-type="bean">
        <task>FT-RDOC.DealRecClaimVouTimer</task>
        <remark><![CDATA[
            定时任务触发自动凭证生成：每天0点5分执行
        ]]></remark>
    </timertask>

    <timertask id="FT-RDOC.002" name="【应收票据锁定工作台】定时任务" work-time="CRON:0 05 0 * * ?" interval="10" auto-start="true" disabled="false" thread="true" task-type="bean">
        <task>FT-RDOC.RdocLockTimer</task>
        <remark><![CDATA[
            定时任务取消锁定：每天0点5分执行
        ]]></remark>
    </timertask>

    <timertask id="FT-RDOC.003" name="应收票据背书/贴现到期（初始化）定时任务" work-time="CRON:0 0 0 * * ?" interval="0" auto-start="true" disabled="false" thread="true" task-type="bean">
        <task>FT-RDOC.RdocEndoDistInitTimer</task>
        <remark><![CDATA[
            定时任务触发自动凭证生成：每天0时执行
        ]]></remark>
    </timertask>

</timertask-list>
