<?xml version="1.0" encoding="UTF-8"?>
<db id="FT-DOCU_View" title="单证视图" xmlns="http://www.snsoft.com.cn/schema/CreateView" xsi:schemaLocation="http://www.snsoft.com.cn/schema/CreateView http://www.snsoft.com.cn/schema/CreateView.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

    <view name="ft_docu_actvou_view" title="承兑赎单实付凭证视图" datasrcid="FT-FUND" viewexpr="承兑赎单实付凭证视图">
        <![CDATA[
		select
            v.batchvid,
            v.vidflag,
            m.acdicode,
            m.acdcode,
            g.fpgcode,
            g.paytype,
            g.paydate,
            m.beneccode,
            g.fcode,
            g.bankcode,
            g.bankacccode,
            g.bankno,
            g.redflag,
            m.isreplc,
            v.corpbcode,
            m.bcode,
            m.wcode,
            v.keptbcode,
            v.tcaptime,
            v.vprepare,
            v.predate,
            v.year,
            v.month,
            v.vmarkicode,
            m.modifier,
            m.modifydate,
            v.srcicode,
            g.pgicode,
            v.sheetcode srcsheetcode,
            v.srcsheetcode sheetcode
        from ft_fund_vmark v inner join ft_docu_acdpg g on v.srcicode =g.pgicode inner join ft_docu_acd m on g.acdicode = m.acdicode
		]]>
    </view>

</db>