Xjs.loadedXjs.push("snsoft/ft/fund/forex/moa");
Xjs.namespace("snsoft.ft.forex.moa.invoker");snsoft.ft.forex.moa.invoker.DeleteForexMoveAppInvoker=function(parameter){snsoft.ft.forex.moa.invoker.DeleteForexMoveAppInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.forex.moa.invoker.DeleteForexMoveAppInvoker,snsoft.ext.cmd.com.biz.CommandDelete,{beforeInvoke:function(event){var ds=this.mainDataSet;return ds.getKeyValues();}});snsoft.ft.forex.moa.invoker.ForexMoveAppPushFundInvoker=function(parameter){snsoft.ft.forex.moa.invoker.ForexMoveAppPushFundInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.forex.moa.invoker.ForexMoveAppPushFundInvoker,snsoft.ext.cmd.CommandInvoker,{invoke:function(event){var invokeParam=this.buildInvokeParam(null);return invokeParam;},afterInvoke:function(event){this.mainTable.refreshTableIfOK();}});