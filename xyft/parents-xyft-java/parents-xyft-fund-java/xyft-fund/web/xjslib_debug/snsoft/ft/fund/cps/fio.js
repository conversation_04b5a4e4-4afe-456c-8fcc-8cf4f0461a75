Xjs.loadedXjs.push("snsoft/ft/fund/cps/fio");
/*snsoft/ft/cps/fio/invoker/CpsFlowInOutCancelMatchingInvoker.java*/
Xjs.namespace("snsoft.ft.cps.fio.invoker");
snsoft.ft.cps.fio.invoker.CpsFlowInOutCancelMatchingInvoker=function(parameter){
    snsoft.ft.cps.fio.invoker.CpsFlowInOutCancelMatchingInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.cps.fio.invoker.CpsFlowInOutCancelMatchingInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.cps.fio.invoker.CpsFlowInOutCancelMatchingInvoker",
    /*snsoft.ft.cps.fio.invoker.CpsFlowInOutCancelMatchingInvoker.check*/
    check:function(event)
    {
        var rows = this.table.getSelectedRowNumbers();
        if(rows == null || rows.length == 0)
        {
            throw new Error(Xjs.ResBundle.getResVal("FT.00000048"));
        }
        for(var i=0;i < rows.length;i++)
        {
            if(this.dataSet.getValue("redflag",rows[i]) != null && this.dataSet.getValue("redflag",rows[i]) != 0)
            {
                throw new Error(Xjs.ResBundle.getResVal("FT-CPS.00000010"));
            }
        }
        return this.newDialogWithFunc(event,(e)=>{
            var dialogPane = Xjs.ui.UIUtil.loadDialog("FT-CPS.CpsUsePayFlowRstCancelMatchDlg",0,null,null,null);
            dialogPane.setWidth("auto");
            dialogPane.setHeight("auto");
            dialogPane.title = event.cfg.title;
            dialogPane.showModal();
            return dialogPane;
        },(e,d)=>{
            var srcTable = d.getItemByName("ft_cps_upfr"),
                cbDataSet = srcTable.getDataSet();
            srcTable.postPending();
            var param = {},
                upfricodelist = [],
                mainDataSet = Xjs.util.TableUtils.getTable_$Panel_$String(Xjs.table.Table.getWUIRootTable(),"ft_cps_upfr").dataSet,
                mainTable = mainDataSet.getTables()[0],
                selectedRowNumbers = mainTable.getSelectedRowNumbers();
            for(var i=0;i < selectedRowNumbers.length;i++)
            {
                upfricodelist.push(mainTable.getValue("upfricode",selectedRowNumbers[i]));
            }
            srcTable.checkNonBlankForSubmit();
            param.upfricode = upfricodelist;
            param.canceldate = cbDataSet.getValue("canceldate");
            event.checkData.param = param;
        });
    },
    /*snsoft.ft.cps.fio.invoker.CpsFlowInOutCancelMatchingInvoker.invoke*/
    invoke:function(event)
    {
        return event.checkData.param;
    },
    /*snsoft.ft.cps.fio.invoker.CpsFlowInOutCancelMatchingInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        this.mainTable.refreshTableIfOK();
    }
});
/*snsoft/ft/cps/fio/invoker/CpsFlowInOutManualMatchInvoker.java*/
snsoft.ft.cps.fio.invoker.CpsFlowInOutManualMatchInvoker$DialogTableRefreshListener=function(dlogTable,params){
    this.dlogTable = dlogTable;
    this.refreshParams = params;
};
Xjs.extend(snsoft.ft.cps.fio.invoker.CpsFlowInOutManualMatchInvoker$DialogTableRefreshListener,Xjs.table.DefaultListener,{
  _js$className_:"snsoft.ft.cps.fio.invoker.CpsFlowInOutManualMatchInvoker$DialogTableRefreshListener",
    /*snsoft.ft.cps.fio.invoker.CpsFlowInOutManualMatchInvoker$DialogTableRefreshListener.dataSetRefreshing*/
    dataSetRefreshing:function(dataSet,e)
    {
        if(!e.refreshParam)
        {
            e.refreshParam = {};
        }
        Xjs.apply(e.refreshParam,this.refreshParams);
    }
});
snsoft.ft.cps.fio.invoker.CpsFlowInOutManualMatchInvoker=function(parameter){
    snsoft.ft.cps.fio.invoker.CpsFlowInOutManualMatchInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.cps.fio.invoker.CpsFlowInOutManualMatchInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.cps.fio.invoker.CpsFlowInOutManualMatchInvoker",
    /*snsoft.ft.cps.fio.invoker.CpsFlowInOutManualMatchInvoker.beforeCheck*/
    beforeCheck:function(event)
    {
        var balfcy = this.mainDataSet.getValue("balfcy");
        this.mainTableIndex = this.mainDataSet.rowAt;
        if(balfcy <= 0)
        {
            throw new Error(Xjs.ResBundle.getResVal("FT-CPS.00000008"));
        }
        return null;
    },
    /*snsoft.ft.cps.fio.invoker.CpsFlowInOutManualMatchInvoker.check*/
    check:function(event)
    {
        var initParams = {},
            readonlyParams = [];
        initParams.bcode = this.mainDataSet.getValue("bcode");
        readonlyParams.push("bcode");
        initParams.corpbcode = this.mainDataSet.getValue("corpbcode");
        readonlyParams.push("corpbcode");
        initParams.fcode = this.mainDataSet.getValue("fcode");
        readonlyParams.push("fcode");
        this.readonlyParams = readonlyParams;
        this.initParams = initParams;
        return this.newDialogWithFunc(event,(e)=>{
            var dialogPane = Xjs.ui.UIUtil.loadDialog("FT-CPS.CpsUsePayFlowRstWorkBenchDlg",0,null,null,null);
            dialogPane.setWidth("auto");
            dialogPane.setHeight("80%");
            dialogPane.title = this.cfg.title;
            var dlgtbl = Xjs.util.TableUtils.getAllTablesFromComponent(dialogPane,false)[0];
            dialogPane.addListener("onShowing",new Xjs.FuncCall(this.resetOnShowing,this,[dlgtbl]));
            if(!dlgtbl.getListener(snsoft.ft.cps.fio.invoker.CpsFlowInOutManualMatchInvoker$DialogTableRefreshListener))
            {
                var reflistener = new snsoft.ft.cps.fio.invoker.CpsFlowInOutManualMatchInvoker$DialogTableRefreshListener(dlgtbl,this.refreshParams);
                dlgtbl.addListener(reflistener);
            }
            var queryPane = dialogPane.getItemByName("query");
            for(var name in initParams)
            {
                queryPane.getItemByName(name).setValue(initParams[name]);
            }
            for(var i=0;i < readonlyParams.length;i++)
            {
                queryPane.getItemByName(readonlyParams[i]).setReadonly(true);
            }
            dialogPane.showModal();
            return dialogPane;
        },(e,d)=>{
            var srcTable = d.getItemByName("ft_cps_fiodlg"),
                dialgRows = srcTable.getSelectedRowNumbers();
            if(dialgRows.length == 0)
            {
                throw new Error(Xjs.ResBundle.getResVal("FT.00000048"));
            }
            var cbDataSet = srcTable.getDataSet();
            cbDataSet.postRow();
            var param = {},
                ifioicodelist = [];
            for(var i=0;i < dialgRows.length;i++)
            {
                ifioicodelist.push(cbDataSet.getValue("fioicode",dialgRows[i]) + "-" + cbDataSet.getValue("thismfcy",dialgRows[i]));
            }
            param.ofioicode = this.mainDataSet.getValue("fioicode");
            param.ifioicodelist = ifioicodelist;
            event.checkData.param = param;
        });
    },
    /*snsoft.ft.cps.fio.invoker.CpsFlowInOutManualMatchInvoker.invoke*/
    invoke:function(event)
    {
        var parameter = {};
        parameter.param = event.checkData.param;
        return parameter;
    },
    /*snsoft.ft.cps.fio.invoker.CpsFlowInOutManualMatchInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        this.mainTable.refreshTableIfOK();
    },
    /*snsoft.ft.cps.fio.invoker.CpsFlowInOutManualMatchInvoker.resetOnShowing*/
    resetOnShowing:function(dlg,dlgtbl)
    {
        dlgtbl.refreshTableIfOK();
    }
});
/*snsoft/ft/cps/fio/lis/CpsFlowInOutManualMatchJSListener.java*/
Xjs.namespace("snsoft.ft.cps.fio.lis");
snsoft.ft.cps.fio.lis.CpsFlowInOutManualMatchJSListener=function(params){
    snsoft.ft.cps.fio.lis.CpsFlowInOutManualMatchJSListener.superclass.constructor.call(this,params);
};
Xjs.extend(snsoft.ft.cps.fio.lis.CpsFlowInOutManualMatchJSListener,snsoft.plat.bas.busi.SystemFunctionListener,{
  _js$className_:"snsoft.ft.cps.fio.lis.CpsFlowInOutManualMatchJSListener",
    /*snsoft.ft.cps.fio.lis.CpsFlowInOutManualMatchJSListener.initComponent*/
    initComponent:function(table,values)
    {
        snsoft.ft.cps.fio.lis.CpsFlowInOutManualMatchJSListener.superclass.initComponent.call(this,table,values);
        if(Xjs.table.Table.getWUIRootTable().name == "ft_cps_upa")
        {
            this.mainDataSet = Xjs.table.Table.getWUIRootTable().dataSet;
        } else 
        {
            this.mainDataSet = Xjs.util.TableUtils.getTable_$Panel_$String(Xjs.table.Table.getWUIRootTable(),"ft_cps_fio").dataSet;
        }
    },
    /*snsoft.ft.cps.fio.lis.CpsFlowInOutManualMatchJSListener.dataSetRowNavigated*/
    dataSetRowNavigated:function(dataSet,e)
    {
        var table = dataSet.getTables()[0],
            rows = table.getSelectedRowNumbers(),
            thismfcy = table.getColumn("thismfcy");
        thismfcy.ignoreTblRdonly = true;
        if(table.isRowSelected(e.row))
        {
            thismfcy.readOnly = false;
            thismfcy.nonBlank = true;
            this.gettFcy(dataSet,rows,e.row);
        } else 
        {
            thismfcy.readOnly = true;
            thismfcy.nonBlank = false;
            dataSet.setValue("thismfcy","");
        }
    },
    /*snsoft.ft.cps.fio.lis.CpsFlowInOutManualMatchJSListener.dataSetRowSelected*/
    dataSetRowSelected:function(dataSet,e)
    {
        dataSet.gotoRow(e.row);
        var table = dataSet.getTables()[0],
            thismfcy = table.getColumn("thismfcy"),
            rows = table.getSelectedRowNumbers();
        thismfcy.ignoreTblRdonly = true;
        if(table.isRowSelected(e.row))
        {
            thismfcy.readOnly = false;
            thismfcy.nonBlank = true;
            this.gettFcy(dataSet,rows,e.row);
        } else 
        {
            thismfcy.readOnly = true;
            thismfcy.nonBlank = false;
            dataSet.setValue("thismfcy","");
        }
    },
    /*snsoft.ft.cps.fio.lis.CpsFlowInOutManualMatchJSListener.gettFcy*/
    gettFcy:function(dataSet,rows,row)
    {
        var table = dataSet.getTables()[0],
            tFcy = 0,
            currowfcy = table.getValue("balfcy",row);
        for(var i=0;i < rows.length;i++)
        {
            if(rows[i] == row)
            {
                continue;
            }
            tFcy += dataSet.getValue("thismfcy",rows[i]);
        }
        var leftfcy;
        if(this.mainDataSet.name == "ft_cps_upa")
        {
            var upaicoder = this.mainDataSet.getValue("srcicode");
            if(!upaicoder)
            {
                return;
            }
            leftfcy = Xjs.RInvoke.rmInvoke("snsoft.ft.cps.upa.service.CpsUsePayAppUIService.getFcying",upaicoder);
        } else 
        {
            leftfcy = this.mainDataSet.getValue("balfcy");
        }
        var restmatchablefcy = leftfcy - tFcy;
        if(restmatchablefcy > 0 && currowfcy > 0)
        {
            dataSet.setValue("thismfcy",restmatchablefcy > currowfcy ? currowfcy : restmatchablefcy);
        }
    }
});
