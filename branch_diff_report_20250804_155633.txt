分支代码差异报告
生成时间: Mon Aug  4 15:56:33 CST 2025
对比分支: origin/phase2 vs origin/master
========================================

=== 分支基本信息 ===
origin/phase2 最新提交:
2777746cd - 孟玉飞, 2025-07-30 19:21:44 : #279386 事务dblistener日志
origin/master 最新提交:
e915a58f8 - 孟玉飞, 2025-07-30 19:21:44 : #279386 事务dblistener日志
=== 整体差异统计 ===
 .../main/resources/snsoft/schema/SheetConfig.xsd   | 12 ------
 .../red/service/impl/SheetRedServiceImpl.java      |  4 +-
 .../cfg/ui/res/SN-PLAT/Doc/SheetDocPrint.xml       |  4 +-
 .../ext-plat/plat/web/xjslib/snsoft/plat/busi.js   |  2 +-
 .../plat/web/xjslib_debug/snsoft/plat/busi.js      | 44 ++++++++++------------
 5 <USER> <GROUP>, 24 insertions(+), 42 deletions(-)

=== 有差异的文件列表 ===
snadk-core/snadk-utils/src/main/resources/snsoft/schema/SheetConfig.xsd
snadk-ext/ext-plat/plat-client/src/main/java/snsoft/plat/sheet/red/service/impl/SheetRedServiceImpl.java
snadk-ext/ext-plat/plat/src/main/resources/cfg/ui/res/SN-PLAT/Doc/SheetDocPrint.xml
snadk-ext/ext-plat/plat/web/xjslib/snsoft/plat/busi.js
snadk-ext/ext-plat/plat/web/xjslib_debug/snsoft/plat/busi.js

=== 分支独有的提交 ===
origin/phase2 独有的提交:
2777746cd - 孟玉飞, 2025-07-30 19:21:44 : #279386 事务dblistener日志
250a973f9 - 罗民民, 2025-07-29 21:09:15 : Revert "refact:日志耗时机制"
253c9c4ed - 罗民民, 2025-07-29 21:09:05 : Revert "refact:日志耗时机制"
779db729d - 罗民民, 2025-07-29 21:09:00 : Revert "279949 【定时任务】session 批次 内容完善"
6ab4aac0b - jixl, 2025-07-29 17:53:12 : 279949 【定时任务】session 批次 内容完善
2c58e276f - jixl, 2025-07-12 01:36:08 : refact:日志耗时机制
0a54419d5 - jxl, 2025-07-12 01:03:11 : refact:日志耗时机制
413e62d57 - 罗民民, 2025-07-29 10:19:56 : #277827 【开发】智能助手嵌入
afcb48a19 - jixl, 2025-07-25 15:07:49 : 270616 【开发】一票业务(取消红冲调用存盘消息)
fec9a2045 - jixl, 2025-07-22 17:01:45 : refact:SheetConfig.xsd
b4a116321 - caoyuwu@snsoft, 2025-07-18 16:24:32 : 多选下拉框, 按 粘贴 触发下拉, 粘贴 的内容 不能为  getValue() 的返回值
08a3e2ed1 - 陈锦祥, 2025-07-18 10:38:20 : #276937 【底层改动】前端CTRL+F1返回sql优化
09dd173d0 - lijun, 2025-06-17 17:08:55 : B#15907 审批工具-审批意见-支持直接粘贴图片操作
3d8778b72 - jixl, 2025-07-16 19:56:17 : 276253 撤回至|可修改按钮的相关优化处理
e5ba9d115 - 王立鹏, 2025-07-11 17:22:39 : 270875 【单点登录】单据版本修改时，单点登录打开异常处理 修改单点登录, 使得生成的LOGINAUTH中不含有需要二次编码处理的字符
54a7b23fb - jixl, 2025-07-10 18:48:42 : 274719 单据类型增加列“商品子表表名”
a2ead5cd3 - lijun, 2025-06-30 17:46:43 : 270941 【底层】防止多次点击
dcc26a421 - lijun, 2025-06-30 17:09:14 : 270941 【底层】防止多次点击
6ec406089 - jixl, 2025-07-10 09:53:36 : 274462 【开发】分布式事务报错回滚机制的优化
b0c884a41 - 方文亮, 2025-07-10 14:27:53 : T#11841 【二批次】中国进关(进区)清关跟踪工作台-开发
52da7a052 - 方文亮, 2025-07-10 10:05:46 : T#11033 海关缴款书-开发-打印及附件处理
27f32182d - jixl, 2025-07-08 15:36:54 : refact:开发环境打开页面性能问题优化
254d6b04c - 付壮志, 2025-07-03 09:31:25 : T#3034 采销意向单据(重画页面的标题行getLayoutRootDOM 存在为空的情况)
origin/master 独有的提交:
e915a58f8 - 孟玉飞, 2025-07-30 19:21:44 : #279386 事务dblistener日志
1f5289402 - 罗民民, 2025-07-29 21:09:15 : Revert "refact:日志耗时机制"
d985e2f18 - 罗民民, 2025-07-29 21:09:05 : Revert "refact:日志耗时机制"
3ba2461e4 - 罗民民, 2025-07-29 21:09:00 : Revert "279949 【定时任务】session 批次 内容完善"
6f9cfd417 - jixl, 2025-07-29 17:53:12 : 279949 【定时任务】session 批次 内容完善
5e01d0d5c - jixl, 2025-07-12 01:36:08 : refact:日志耗时机制
973f8ee32 - jxl, 2025-07-12 01:03:11 : refact:日志耗时机制
764517e12 - 罗民民, 2025-07-29 10:19:56 : #277827 【开发】智能助手嵌入
fa6610d95 - caoyuwu@snsoft, 2025-07-18 16:24:32 : 多选下拉框, 按 粘贴 触发下拉, 粘贴 的内容 不能为  getValue() 的返回值
ac05ef372 - 陈锦祥, 2025-07-18 10:38:20 : #276937 【底层改动】前端CTRL+F1返回sql优化
6e1d57852 - lijun, 2025-06-17 17:08:55 : B#15907 审批工具-审批意见-支持直接粘贴图片操作
39c6c1f39 - jixl, 2025-07-16 19:56:17 : 276253 撤回至|可修改按钮的相关优化处理
ff376e8ef - 王立鹏, 2025-07-11 17:22:39 : 270875 【单点登录】单据版本修改时，单点登录打开异常处理 修改单点登录, 使得生成的LOGINAUTH中不含有需要二次编码处理的字符
c9627e4c1 - jixl, 2025-07-10 18:48:42 : 274719 单据类型增加列“商品子表表名”
71ecf0c4a - lijun, 2025-06-30 17:46:43 : 270941 【底层】防止多次点击
152bc905d - lijun, 2025-06-30 17:09:14 : 270941 【底层】防止多次点击
82a4c69ec - jixl, 2025-07-10 09:53:36 : 274462 【开发】分布式事务报错回滚机制的优化
