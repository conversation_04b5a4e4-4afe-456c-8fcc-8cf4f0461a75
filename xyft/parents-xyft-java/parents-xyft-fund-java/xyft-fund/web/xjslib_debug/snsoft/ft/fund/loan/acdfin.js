Xjs.loadedXjs.push("snsoft/ft/fund/loan/acdfin");
/*snsoft/ft/loan/acdfin/app/invoker/AccOrdFinaAppLIRedVoucherInvoker.java*/
Xjs.namespace("snsoft.ft.loan.acdfin.app.invoker");
snsoft.ft.loan.acdfin.app.invoker.AccOrdFinaAppLIRedVoucherInvoker=function(parameter){
    snsoft.ft.loan.acdfin.app.invoker.AccOrdFinaAppLIRedVoucherInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.loan.acdfin.app.invoker.AccOrdFinaAppLIRedVoucherInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.loan.acdfin.app.invoker.AccOrdFinaAppLIRedVoucherInvoker",
    /*snsoft.ft.loan.acdfin.app.invoker.AccOrdFinaAppLIRedVoucherInvoker.invoke*/
    invoke:function(event)
    {
        var rows = this.mainTable.getSelectedRowNumbers();
        if(rows == null || rows.length == 0)
        {
            throw Xjs.ResBundle.getResVal("FT.00000048");
        }
        for(var i=0;i < rows.length;i++)
        {
            var redflag = this.dataSet.getValue("redflag",rows[i]);
            if(redflag != 0)
            {
                throw Xjs.ResBundle.getResVal("SNA-ACC.00000087");
            }
            var vidflag = this.dataSet.getValue("vidflag",rows[i]);
            if(vidflag != 1)
            {
                throw Xjs.ResBundle.getResVal("SNA-ACC.00000096");
            }
        }
        var vmarkicodes = Xjs.util.DataSetUtils.getColumnStrValues_DataSet_int(this.mainDataSet,"vmarkicode",rows),
            innercodes = Xjs.util.DataSetUtils.getColumnStrValues_DataSet_int(this.mainDataSet,"acdfaliicode",rows);
        return {innercodes:innercodes};
    },
    /*snsoft.ft.loan.acdfin.app.invoker.AccOrdFinaAppLIRedVoucherInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        this.table.refreshTable();
    }
});
/*snsoft/ft/loan/acdfin/app/invoker/AccOrdFinaAppRIRedVoucherInvoker.java*/
snsoft.ft.loan.acdfin.app.invoker.AccOrdFinaAppRIRedVoucherInvoker=function(parameter){
    snsoft.ft.loan.acdfin.app.invoker.AccOrdFinaAppRIRedVoucherInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.loan.acdfin.app.invoker.AccOrdFinaAppRIRedVoucherInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.loan.acdfin.app.invoker.AccOrdFinaAppRIRedVoucherInvoker",
    /*snsoft.ft.loan.acdfin.app.invoker.AccOrdFinaAppRIRedVoucherInvoker.invoke*/
    invoke:function(event)
    {
        var rows = this.mainTable.getSelectedRowNumbers();
        if(rows == null || rows.length == 0)
        {
            throw Xjs.ResBundle.getResVal("FT.00000048");
        }
        for(var i=0;i < rows.length;i++)
        {
            var vidflag = this.dataSet.getValue("vidflag",rows[i]);
            if(vidflag != 1)
            {
                throw Xjs.ResBundle.getResVal("FT-LOAN.00000050");
            }
        }
        var innercodes = Xjs.util.DataSetUtils.getColumnStrValues_DataSet_int(this.mainDataSet,"acdfariicode",rows);
        return {innercodes:innercodes};
    },
    /*snsoft.ft.loan.acdfin.app.invoker.AccOrdFinaAppRIRedVoucherInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        this.table.refreshTable();
    }
});
/*snsoft/ft/loan/acdfin/app/invoker/AccOrdFinaAppSendBackInvoker.java*/
snsoft.ft.loan.acdfin.app.invoker.AccOrdFinaAppSendBackInvoker=function(parameter){
    snsoft.ft.loan.acdfin.app.invoker.AccOrdFinaAppSendBackInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.loan.acdfin.app.invoker.AccOrdFinaAppSendBackInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.loan.acdfin.app.invoker.AccOrdFinaAppSendBackInvoker",
    /*snsoft.ft.loan.acdfin.app.invoker.AccOrdFinaAppSendBackInvoker.beforeInvoke*/
    beforeInvoke:function(event)
    {
        var complstatus = this.mainDataSet.getValue("complstatus");
        if(complstatus == "30")
        {
            var lendInfoTable = Xjs.util.TableUtils.getTable_$Panel_$String(this.mainTable.getRootTable(),"ft_loan_acdfali"),
                lendInfoDataSet = lendInfoTable.getDataSet();
            if(lendInfoDataSet.getRowCount() != 0)
            {
                throw new Error(Xjs.ResBundle.getResVal("FT-LOAN.00000004"));
            }
        }
        return {innercode:this.mainDataSet.get("acdfaicode"),sheetcode:this.mainDataSet.get("sheetcode")};
    },
    /*snsoft.ft.loan.acdfin.app.invoker.AccOrdFinaAppSendBackInvoker.invoke*/
    invoke:function(event)
    {
        var returnCode = event.beforeInvokeRtnVal.returnCode;
        if(returnCode == "E")
        {
            throw {dummy:true};
        }
        return null;
    }
});
/*snsoft/ft/loan/acdfin/app/invoker/AccOrdFinaExtendAppSendBackInvoker.java*/
snsoft.ft.loan.acdfin.app.invoker.AccOrdFinaExtendAppSendBackInvoker=function(parameter){
    snsoft.ft.loan.acdfin.app.invoker.AccOrdFinaExtendAppSendBackInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.loan.acdfin.app.invoker.AccOrdFinaExtendAppSendBackInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.loan.acdfin.app.invoker.AccOrdFinaExtendAppSendBackInvoker",
    /*snsoft.ft.loan.acdfin.app.invoker.AccOrdFinaExtendAppSendBackInvoker.beforeCheck*/
    beforeCheck:function(event)
    {
        window.console.log("beforeCheck");
        return {innercode:this.mainDataSet.get("acdfaeicode"),sheetcode:this.mainDataSet.get("sheetcode"),acdfaicode:this.mainDataSet.get("acdfaicode")};
    },
    /*snsoft.ft.loan.acdfin.app.invoker.AccOrdFinaExtendAppSendBackInvoker.check*/
    check:function(event)
    {
        this.table.refreshTable();
        return null;
    }
});
/*snsoft/ft/loan/acdfin/app/lis/AccOrdFinaAppJSListener.java*/
Xjs.namespace("snsoft.ft.loan.acdfin.app.lis");
snsoft.ft.loan.acdfin.app.lis.AccOrdFinaAppJSListener=function(params){
    snsoft.ft.loan.acdfin.app.lis.AccOrdFinaAppJSListener.superclass.constructor.call(this,params);
};
Xjs.extend(snsoft.ft.loan.acdfin.app.lis.AccOrdFinaAppJSListener,snsoft.plat.bas.busi.SystemFunctionListener,{
  _js$className_:"snsoft.ft.loan.acdfin.app.lis.AccOrdFinaAppJSListener"
});
/*snsoft/ft/loan/acdfin/back/invoker/AccOrdFinaBackChangeIsreferableInvoker.java*/
Xjs.namespace("snsoft.ft.loan.acdfin.back.invoker");
snsoft.ft.loan.acdfin.back.invoker.AccOrdFinaBackChangeIsreferableInvoker=function(parameter){
    snsoft.ft.loan.acdfin.back.invoker.AccOrdFinaBackChangeIsreferableInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.loan.acdfin.back.invoker.AccOrdFinaBackChangeIsreferableInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.loan.acdfin.back.invoker.AccOrdFinaBackChangeIsreferableInvoker",
    /*snsoft.ft.loan.acdfin.back.invoker.AccOrdFinaBackChangeIsreferableInvoker.invoke*/
    invoke:function(event)
    {
        var isreferable = this.dataSet.getValue("isreferable");
        if("N"==isreferable)
        {
            this.mainTable.checkNonBlankForSubmit();
        }
        return this.buildInvokeParam(null);
    },
    /*snsoft.ft.loan.acdfin.back.invoker.AccOrdFinaBackChangeIsreferableInvoker.afterInvoke*/
    afterInvoke:function(event)
    {
        this.mainTable.refreshTableIfOK();
    }
});
/*snsoft/ft/loan/acdfin/back/invoker/AccOrdFinaBackFundSrcDeleteInvoker.java*/
snsoft.ft.loan.acdfin.back.invoker.AccOrdFinaBackFundSrcDeleteInvoker=function(parameter){
    snsoft.ft.loan.acdfin.back.invoker.AccOrdFinaBackFundSrcDeleteInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.loan.acdfin.back.invoker.AccOrdFinaBackFundSrcDeleteInvoker,snsoft.ext.cmd.CommandInvoker,{
  _js$className_:"snsoft.ft.loan.acdfin.back.invoker.AccOrdFinaBackFundSrcDeleteInvoker",
    /*snsoft.ft.loan.acdfin.back.invoker.AccOrdFinaBackFundSrcDeleteInvoker.beforeCheck*/
    beforeCheck:function(event)
    {
        if(this.dataSet.getValue("srcismanual") == "N" && this.dataSet.getValue("srccode"))
        {
            throw Xjs.ResBundle.getResVal("FT-LOAN.00000040");
        }
        return null;
    }
});
/*snsoft/ft/loan/acdfin/back/invoker/AccOrdFinaBackSendSerCancelInvoker.java*/
snsoft.ft.loan.acdfin.back.invoker.AccOrdFinaBackSendSerCancelInvoker=function(parameter){
    snsoft.ft.loan.acdfin.back.invoker.AccOrdFinaBackSendSerCancelInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.loan.acdfin.back.invoker.AccOrdFinaBackSendSerCancelInvoker,snsoft.plat.bas.sheet.cmd.CommandSheet,{
  _js$className_:"snsoft.ft.loan.acdfin.back.invoker.AccOrdFinaBackSendSerCancelInvoker",
    /*snsoft.ft.loan.acdfin.back.invoker.AccOrdFinaBackSendSerCancelInvoker.beforeInvoke*/
    beforeInvoke:function(event)
    {
        var innercode = this.getInnercode();
        if(innercode == null)
            return null;
        var parameter = this.buildSheetRecordParameter();
        return parameter;
    },
    /*snsoft.ft.loan.acdfin.back.invoker.AccOrdFinaBackSendSerCancelInvoker.invoke*/
    invoke:function(event)
    {
        var returnCode = event.beforeInvokeRtnVal.returnCode;
        if(returnCode == "E")
        {
            throw {dummy:true};
        }
        return null;
    }
});
/*snsoft/ft/loan/acdfin/back/invoker/DeleteAccOrdFinaBackAppInvoker.java*/
snsoft.ft.loan.acdfin.back.invoker.DeleteAccOrdFinaBackAppInvoker=function(parameter){
    snsoft.ft.loan.acdfin.back.invoker.DeleteAccOrdFinaBackAppInvoker.superclass.constructor.call(this,parameter);
};
Xjs.extend(snsoft.ft.loan.acdfin.back.invoker.DeleteAccOrdFinaBackAppInvoker,snsoft.ext.cmd.com.biz.CommandDelete,{
  _js$className_:"snsoft.ft.loan.acdfin.back.invoker.DeleteAccOrdFinaBackAppInvoker",
    /*snsoft.ft.loan.acdfin.back.invoker.DeleteAccOrdFinaBackAppInvoker.beforeInvoke*/
    beforeInvoke:function(event)
    {
        var ds = this.mainDataSet;
        return ds.getKeyValues();
    }
});
/*snsoft/ft/loan/acdfin/back/lis/AccOrdFinaBackControlJSListener.java*/
Xjs.namespace("snsoft.ft.loan.acdfin.back.lis");
snsoft.ft.loan.acdfin.back.lis.AccOrdFinaBackControlJSListener=function(params){
    snsoft.ft.loan.acdfin.back.lis.AccOrdFinaBackControlJSListener.superclass.constructor.call(this,params);
};
Xjs.extend(snsoft.ft.loan.acdfin.back.lis.AccOrdFinaBackControlJSListener,snsoft.plat.bas.busi.SystemFunctionListener,{
  _js$className_:"snsoft.ft.loan.acdfin.back.lis.AccOrdFinaBackControlJSListener",
    /*snsoft.ft.loan.acdfin.back.lis.AccOrdFinaBackControlJSListener.initComponent*/
    initComponent:function(table,values)
    {
        snsoft.ft.loan.acdfin.back.lis.AccOrdFinaBackControlJSListener.superclass.initComponent.call(this,table,values);
        this.mainTable = this.getTable(table,"ft_loan_acdfar");
        this.mainDataSet = this.mainTable.getDataSet();
        this.acdfarsrcTable = this.getTable(table,"ft_fund_fsrc");
        this.acdfarsrcDataSet = this.acdfarsrcTable.getDataSet();
    },
    /*snsoft.ft.loan.acdfin.back.lis.AccOrdFinaBackControlJSListener.dataSetFieldPosted*/
    dataSetFieldPosted:function(dataSet,event)
    {
        this.controlSrc(event);
    },
    /*snsoft.ft.loan.acdfin.back.lis.AccOrdFinaBackControlJSListener.controlSrc*/
    controlSrc:function(event)
    {
        if(String.isStrIn("fcode,sfcode",event.columnName))
        {
            var fcode = this.mainDataSet.getValue("fcode"),
                sfcode = this.mainDataSet.getValue("sfcode");
            if(fcode == sfcode)
            {
                this.acdfarsrcDataSet.deleteAllRows();
            }
        }
    }
});
/*snsoft/ft/loan/acdfin/extend/lis/AccOrdFinaExtendAppJSListener.java*/
Xjs.namespace("snsoft.ft.loan.acdfin.extend.lis");
snsoft.ft.loan.acdfin.extend.lis.AccOrdFinaExtendAppJSListener=function(params){
    snsoft.ft.loan.acdfin.extend.lis.AccOrdFinaExtendAppJSListener.superclass.constructor.call(this,params);
};
Xjs.extend(snsoft.ft.loan.acdfin.extend.lis.AccOrdFinaExtendAppJSListener,snsoft.plat.bas.busi.SystemFunctionListener,{
  _js$className_:"snsoft.ft.loan.acdfin.extend.lis.AccOrdFinaExtendAppJSListener",
    /*snsoft.ft.loan.acdfin.extend.lis.AccOrdFinaExtendAppJSListener.initComponent*/
    initComponent:function(table,values)
    {
        snsoft.ft.loan.acdfin.extend.lis.AccOrdFinaExtendAppJSListener.superclass.initComponent.call(this,table,values);
        this.mainTable = this.getTable(table,"ft_loan_acdfae_bas");
        this.mainDataSet = this.mainTable.getDataSet();
    },
    /*snsoft.ft.loan.acdfin.extend.lis.AccOrdFinaExtendAppJSListener.dataSetFieldPosted*/
    dataSetFieldPosted:function(dataSet,event)
    {
        window.console.log("dataSetFieldPosted");
        snsoft.ft.loan.acdfin.extend.lis.AccOrdFinaExtendAppJSListener.superclass.dataSetFieldPosted.call(this,dataSet,event);
        if(event.columnName == "exdays")
        {
            var duedate = this.mainDataSet.getValue("duedate"),
                exdays = this.mainDataSet.getValue("exdays"),
                exdate = Xjs.util.DateUtils.incDate(duedate,exdays);
            this.mainDataSet.setValue("exduedate",exdate);
        }
    }
});
