<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>cn.snsoft.xyft</groupId>
		<artifactId>parents-xyft-fund-java</artifactId>
		<version>5.0.0-SNAPSHOT</version>
	</parent>

	<artifactId>xyft-fund</artifactId>
	<name>xyft-fund-${xyft.fund.version}.jar</name>
	<dependencies>
		<dependency>
			<groupId>cn.snsoft.xyft</groupId>
			<artifactId>xyft-fund-client</artifactId>
		</dependency>
		<dependency>
			<groupId>cn.snsoft.xyft</groupId>
			<artifactId>xyft-bcc-client</artifactId>
		</dependency>
		<dependency>
			<groupId>cn.snsoft.xyft</groupId>
			<artifactId>xyft-bmc-client</artifactId>
		</dependency>
		<dependency>
			<groupId>cn.snsoft.xyft</groupId>
			<artifactId>xyft-code-client</artifactId>
		</dependency>
		<dependency>
			<groupId>cn.snsoft.xyft</groupId>
			<artifactId>xyft-ord-client</artifactId>
		</dependency>
		<dependency>
			<groupId>cn.snsoft.xyft</groupId>
			<artifactId>xyft-ship-client</artifactId>
		</dependency>
		<dependency>
			<groupId>cn.snsoft.xyft</groupId>
			<artifactId>xyft-inv-client</artifactId>
		</dependency>
		<dependency>
			<groupId>cn.snsoft.xyft</groupId>
			<artifactId>xyft-tst-client</artifactId>
		</dependency>
		<dependency>
			<groupId>cn.snsoft.xysna</groupId>
			<artifactId>xysna-acc-client</artifactId>
		</dependency>
		<dependency>
			<groupId>cn.snsoft.xysna</groupId>
			<artifactId>xysna-vm-adapter</artifactId>
		</dependency>
		<dependency>
			<groupId>cn.snsoft.xysna</groupId>
			<artifactId>xysna-irt-client</artifactId>
		</dependency>
	</dependencies>
</project>