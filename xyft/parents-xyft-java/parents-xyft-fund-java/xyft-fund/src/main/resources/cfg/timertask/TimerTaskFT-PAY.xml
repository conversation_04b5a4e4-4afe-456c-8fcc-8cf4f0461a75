<?xml version="1.0" encoding="UTF-8"?>
<timertask-list xmlns="http://www.snsoft.com.cn/schema/TimerTask"
                xsi:schemaLocation="http://www.snsoft.com.cn/schema/TimerTask http://www.snsoft.com.cn/schema/TimerTask.xsd"
                xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <timertask id="FT-PAY.001" name="未提交保税企业监管材料的付款申请单发送Elink" work-time="CRON:0 10 0 * * ?" interval="0" auto-start="true" thread="true" task-type="bean">
        <task>FT-PAY.PayAppConfirTimer</task>
        <remark><![CDATA[
            未提交保税企业监管材料的付款申请单发送Elink,每天0点10分执行
        ]]></remark>
    </timertask>

    <timertask id="FT-PAY.002" name="定时清空票据登记信息" work-time="CRON:0 0 0 * * ?" interval="0" auto-start="true" thread="true" task-type="bean">
        <task>FT-PAY.PayAppClearTimer</task>
        <remark><![CDATA[
            定时清空票据登记信息,每天24:00执行
        ]]></remark>
    </timertask>
    <timertask id="FT-PAY.003" name="【应收票据背书付款到期凭证】定时任务" work-time="CRON:0 0 0 * * ?" interval="0" auto-start="true" thread="true" task-type="bean">
        <task>FT-PAY.RdocEndorseDueTimer</task>
        <remark><![CDATA[
            【应收票据背书付款到期凭证】,每天24:00执行
        ]]></remark>
    </timertask>
</timertask-list>