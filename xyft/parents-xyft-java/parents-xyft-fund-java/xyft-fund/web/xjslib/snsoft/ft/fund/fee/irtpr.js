Xjs.loadedXjs.push("snsoft/ft/fund/fee/irtpr");
Xjs.namespace("snsoft.ft.fee.irtpr.invoker");snsoft.ft.fee.irtpr.invoker.FeeRedBtnBmpCheckInvoker=function(parameter){snsoft.ft.fee.irtpr.invoker.FeeRedBtnBmpCheckInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.fee.irtpr.invoker.FeeRedBtnBmpCheckInvoker,snsoft.ext.cmd.com.biz.SaveInvoker,{getInnercode:function(){var rows=this.table.getSelectedRowNumbers(),innercodes=Xjs.util.DataSetUtils.getColumnStrValues_DataSet_int_String(this.dataSet,this.innercodeName,rows,",");return innercodes;},beforeCheck:function(event){var datas=this.buildInvokeParam(null);datas.sheetcode=this.sheetcode;datas.innercodes=this.getInnercode();return datas;},check:function(event){var bmpcodes=event.beforeCheckRtnVal.bmpcodes,nobmpdatas=event.beforeCheckRtnVal.nobmpdatas,tip=event.beforeCheckRtnVal.tip;if(bmpcodes||nobmpdatas){var muiid="SNA-ACC.RelaBmpChkPop",initVals={param:{bmpcodes:bmpcodes,nobmpdatas:nobmpdatas,tip:tip}},dlg=Xjs.ui.UIUtil.loadDialog(muiid,0,Xjs.ui.Panel.newCloseButton(),null,null,initVals);dlg.title=this.cfg.title;dlg.showModal();throw {dummy:true};}return null;}});snsoft.ft.fee.irtpr.invoker.IrtProvConfirmBtnInvoker=function(parameter){snsoft.ft.fee.irtpr.invoker.IrtProvConfirmBtnInvoker.superclass.constructor.call(this,parameter);this.operateType=parameter.operateType;};Xjs.extend(snsoft.ft.fee.irtpr.invoker.IrtProvConfirmBtnInvoker,snsoft.ext.cmd.CommandInvoker,{beforeCheck:function(event){var rows=this.table.getSelectedRowNumbers(),param={},irtpricodes=[];if(!rows||rows.length==0){throw new Error(Xjs.ResBundle.getResVal("FT.00000048"));}for(var i=0;i<rows.length;i++){var status=this.dataSet.getValue("status",rows[i]);if(this.operateType=="confirm"&&!("10"==status)){throw new Error(Xjs.ResBundle.getResVal("FT-FEE.00000012"));}else if(this.operateType=="cancel"&&!("70"==status)){throw new Error(Xjs.ResBundle.getResVal("FT-FEE.00000013"));}irtpricodes.push(this.dataSet.getValue("irtpricode",rows[i]));}param.irtpricodes=irtpricodes;param.operatetype=this.operateType;return param;},check:function(event){this.mainDataSet.refresh(this.mainDataSet._refreshParameter);return null;}});