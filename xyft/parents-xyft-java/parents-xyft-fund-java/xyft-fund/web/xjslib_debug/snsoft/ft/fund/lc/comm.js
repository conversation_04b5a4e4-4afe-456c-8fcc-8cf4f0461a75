Xjs.loadedXjs.push("snsoft/ft/fund/lc/comm");
/*snsoft/ft/lc/comm/match/LcVsntypeMatchValue.java*/
Xjs.namespace("snsoft.ft.lc.comm.match");
snsoft.ft.lc.comm.match.LcVsntypeMatchValue=function(config){
    snsoft.ft.lc.comm.match.LcVsntypeMatchValue.superclass.constructor.call(this,config);
};
Xjs.extend(snsoft.ft.lc.comm.match.LcVsntypeMatchValue,Xjs.table.sample.TableOptsCtrlListener$MatchValue,{
  _js$className_:"snsoft.ft.lc.comm.match.LcVsntypeMatchValue",
    /*snsoft.ft.lc.comm.match.LcVsntypeMatchValue.match*/
    match:function(value,row)
    {
        var data = window.EnvParameter._VBTYPES_;
        if(data == null)
        {
            return false;
        }
        var vsntypes = data[this.values[0]];
        if(vsntypes == null)
        {
            return false;
        }
        if(value == null)
        {
            return this.negmatch ? true : false;
        }
        var isHas = vsntypes.indexOf(value) >= 0;
        return this.negmatch ? !isHas : isHas;
    }
});
