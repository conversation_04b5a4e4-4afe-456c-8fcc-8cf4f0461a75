<soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/">
	<soapenv:Header>
		<wsse:Security soap:mustUnderstand=" 1" xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd">
			<wsse:UsernameToken xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd">
				<wsse:Username>${macro.SecretUsercode}</wsse:Username>
				<wsse:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText">${macro.SecretPassword}</wsse:Password>
			</wsse:UsernameToken>
		</wsse:Security>
	</soapenv:Header>
	<soap:Body>
		<ns2:outService xmlns:ns2="http://web.externalserv.ats.fingard.com/">
			<ns2:InputParameters>
				<commonParameters>
					<source>${macro.data.commonParameters.source}</source>
					<target>${macro.data.commonParameters.target}</target>
					<docType>${macro.data.commonParameters.docType}</docType>
					<docCode>${macro.data.commonParameters.docCode}</docCode>
					<pageTotal>${macro.data.commonParameters.pageTotal}</pageTotal>
					<pageNo>${macro.data.commonParameters.pageNo}</pageNo>
					<property>${macro.data.commonParameters.property}</property>
				</commonParameters>
				<message><![CDATA[
					${macro.data.message}
				]]></message>
			</ns2:InputParameters>
		</ns2:outService>
	</soap:Body>
</soap:Envelope>