# 付款申请

###付款主表
ft_pay_app=付款申请主表
ft_pay_app.paicode=付款申请内码
ft_pay_app.nstctranno=NSTC流水号
ft_pay_app.pacode=${pacode}
ft_pay_app.pacoder=付款申请外码红冲码
ft_pay_app.nstype=单据创建方式
ft_pay_app.redflag=红冲标记
ft_pay_app.redflag#5=${bered}
ft_pay_app.redflag#2=${red}
ft_pay_app.pacodeo=付款申请原单号
ft_pay_app.sheetcodeo=付款申请原单据号
ft_pay_app.bcode=${b_wcode}
ft_pay_app.wcode=${wcode}
ft_pay_app.corpbcode=${corpbcode}
ft_pay_app.ccode=${recccode}
ft_pay_app.paymode=${apppaymode}
ft_pay_app.fcode=${apayfcode}
ft_pay_app.sfcode=${sfcode}
ft_pay_app.fcy=申请支付金额
ft_pay_app.sfcy=已支付金额
ft_pay_app.sfcode=${sfcode}
ft_pay_app.scy=${scy}
ft_pay_app.zcny=${zcny}
ft_pay_app.zusd=${zusd}
ft_pay_app.fserate=${fserate}
ft_pay_app.scerate=${scerate}
ft_pay_app.suerate=${suerate}
ft_pay_app.bankaccount=${recbankandacc}
ft_pay_app.bankname=${recbankcode}
ft_pay_app.bankaccountname=${bankaccname}
ft_pay_app.isoverpay=是否跨境支付
ft_pay_app.feeproperty=费用性质
ft_pay_app.rdate=预计付款日期
ft_pay_app.ishasinv=是否已开具票据
ft_pay_app.isreferable=未生效时可引用
ft_pay_app.isvid=是否入账
ft_pay_app.isadddoc=是否补录票据
ft_pay_app.fundplanitem=${fundplanitem}
ft_pay_app.ispaid=是否付讫
ft_pay_app.isneedpfund=是否需要推资金
ft_pay_app.ismodifautopfund=是否修改自动推资金
ft_pay_app.isautopfund=是否自动推资金
ft_pay_app.issimulate=是否模拟
ft_pay_app.isfspats=是否资金来源发送ATS
ft_pay_app.issuspend=是否资金挂起
ft_pay_app.isreappr=是否取消重批
ft_pay_app.relapacode=关联取消单据
ft_pay_app.relapaicode=关联取消单据内码
ft_pay_app.postscript=付款说明
ft_pay_app.fundtype=款项类别
ft_pay_app.suspendreason=挂起原因
ft_pay_app.funplan=资金计划详情
ft_pay_app.docnum=附件张数
ft_pay_app.busiapprdesc=业务审批描述
ft_pay_app.bacompcode=保税区监管企业
ft_pay_app.combinpayno=合并付款编码
ft_pay_app.sheetcode=单据类型
ft_pay_app.ratifydate=${ratifydate}
ft_pay_app.submitdate=${submitdate}
ft_pay_app.performdate=${performdate}
ft_pay_app.wfcode=${wfcode}
ft_pay_app.wfuid=审批节点
ft_pay_app.rdatefm=预计付款日期从
ft_pay_app.rdateto=到
ft_pay_app.ptcodelist=协议号
ft_pay_app.purprjcode=采购业务编号
ft_pay_app.purordcode=采购合同号
ft_pay_app.purshipcoder=到货单号
ft_pay_app.salprjcode=销售业务编号
ft_pay_app.salordcode=销售合同号
ft_pay_app.invcoder=发票登记号
ft_pay_app.tordcode=运输委托单号
ft_pay_app.ptcode=协议号
ft_pay_app.purprjcodelist=采购业务编号
ft_pay_app.purordcodelist=采购合同号
ft_pay_app.purshipcoderlist=到货单号
ft_pay_app.salprjcodelist=销售业务编号
ft_pay_app.salordcodelist=销售合同号
ft_pay_app.invcoderlist=发票登记号
ft_pay_app.logitocodelist=运输委托单号
ft_pay_app.eclcodelist=报关单号
ft_pay_app.pticodelist=协议号内码
ft_pay_app.purprjicodelist=采购业务编号内码
ft_pay_app.purordicodelist=采购合同号内码
ft_pay_app.purshipicoderlist=到货单号内码
ft_pay_app.salprjicodelist=销售业务编号内码
ft_pay_app.salordicodelist=销售合同号内码
ft_pay_app.invicoderlist=发票登记号内码
ft_pay_app.logitoicodelist=运输委托单号内码
ft_pay_app.ismidrec=是否中间行收款
ft_pay_app.midswiftcode=中间行SWIFTCODE
ft_pay_app.midbankname=中间行
ft_pay_app.midsystem=中间行清算系统
ft_pay_app.feebearer=国内外费用承担方
ft_pay_app.isbondedpay=是否保税货物项下付款
ft_pay_app.transcode=交易编码
ft_pay_app.fundproperty=款项性质
ft_pay_app.payproperty=付汇性质
ft_pay_app.recswiftcode=收款方SWIFTCODE
ft_pay_app.swiftname=SWIFT NAME
ft_pay_app.bankncode=开户行贸易国别（地区）
ft_pay_app.recbankcode=收款方开户行编码
ft_pay_app.recaccename=收款方英文户名
ft_pay_app.recncode=收款方常驻贸易国别（地区）
ft_pay_app.recmobile=收款方手机号
ft_pay_app.recaddress=收款方地址
ft_pay_app.recmaddr=收款方邮件地址
ft_pay_app.payscript=交易附言
ft_pay_app.confirmdatefm=${confirmdatefm}
ft_pay_app.issimulate=是否模拟
ft_pay_app.funplan=资金计划详情
ft_pay_app.vpdatetype=${docvpdatetype}
ft_pay_app.duedate=${docduedate}
ft_pay_app.appdocnum=${appdocnum}
ft_pay_app.paystatus=支付状态
ft_pay_app.eclcode=报关单号
ft_pay_app.paytaxmode=付税方式
ft_pay_app.fstype=资金来源
ft_pay_app.deptbcode=${bubcode}
ft_pay_app.paicoder=付款申请红冲码
ft_pay_app.paicodeo=付款申请原单据主键
ft_pay_app.fpicode=财务实付内码
ft_pay_app.checkaidmodify=单据检查选择码表修改时间
ft_pay_app.baicode=收款方账号内码

###付款子表
ft_pay_appg=付款申请子表
ft_pay_appg.pagicode=付款申请子表内码
ft_pay_appg.paicode=付款申请主表内码
ft_pay_appg.paicoder=付款申请红冲核销内码
ft_pay_appg.pacoder=付款申请外码红冲码
ft_pay_appg.pagicodeo=付款明细原内码
ft_pay_appg.pagicoder=付款明细核销内码
ft_pay_appg.lrpicodex=往来应收付核销码
ft_pay_appg.fcy=原币金额
ft_pay_appg.payfcy=折申请支付币种金额
ft_pay_appg.invrptype=费用类型
ft_pay_appg.rptype=收付项目
ft_pay_appg.fdc=金额方向
ft_pay_appg.tdc=退款方向
ft_pay_appg.scy=${scy}
ft_pay_appg.zcny=${zcny}
ft_pay_appg.zusd=${zusd}
ft_pay_appg.fserate=${fserate}
ft_pay_appg.scerate=${scerate}
ft_pay_appg.suerate=${suerate}
ft_pay_appg.fzerate=折申请支付币种比率
ft_pay_appg.dc=${rpdc}
ft_pay_appg.cfmode=${cfmode}
ft_pay_appg.costbcode=${costbcode}
ft_pay_appg.ccode=结算对象
ft_pay_appg.ccodetrust=委托方
ft_pay_appg.purprjcode=采购业务编号
ft_pay_appg.purprjicode=采购业务内码
ft_pay_appg.salprjcode=销售业务编号
ft_pay_appg.salprjicode=销售业务内码
ft_pay_appg.purordcode=${purordcode}
ft_pay_appg.purordicode=采购合同号内码
ft_pay_appg.salordcode=销售合同号
ft_pay_appg.salordicode=销售合同号内码
ft_pay_appg.salshipcoder=销售发货单号
ft_pay_appg.salshipicoder=发货单内码
ft_pay_appg.purshipcoder=到货单号
ft_pay_appg.purshipicoder=到货单内码
ft_pay_appg.tsoscoder=出仓回单号
ft_pay_appg.tsosicoder=出仓回单内码
ft_pay_appg.invcoder=发票登记号
ft_pay_appg.invicoder=发票登记内码
ft_pay_appg.invsheetcode=发票单据号
ft_pay_appg.ptcode=协议号
ft_pay_appg.pticode=协议内码
ft_pay_appg.eclcode=报关单号
ft_pay_appg.logitoicode=运输委托单内码
ft_pay_appg.officode=调整/冲销单主表内码
ft_pay_appg.offcode=调整/冲销单号
ft_pay_appg.offgicode=调整/冲销单子表内码
ft_pay_appg.offgsicode=调整/冲销单孙表内码

ft_pay_app_workbench.corflagstr=只查待执行金额>0

## 付款申请票据登记表
ft_pay_appdoc=付款申请票据登记表
ft_pay_appdoc.padocicode=付款申请票据登记内码
ft_pay_appdoc.paicode=申请主表内码
ft_pay_appdoc.paicoder=付款申请红冲核销内码
ft_pay_appdoc.pacoder=付款申请红冲核销外码
ft_pay_appdoc.padocicodeo=付款申请票据登记原内码
ft_pay_appdoc.padocicoder=付款申请票据登记核销内码
ft_pay_appdoc.docno=${docno}
ft_pay_appdoc.doccode=票据号
ft_pay_appdoc.docstatus=${docstatus}
ft_pay_appdoc.doctype=${doctype}
ft_pay_appdoc.banktype=银行大类
ft_pay_appdoc.isdircon=${isdirectconn}
ft_pay_appdoc.docrate=年利率(不含税)
ft_pay_appdoc.fcy=${docfcy}
ft_pay_appdoc.endorselockfcy=背书锁定金额
ft_pay_appdoc.isrecourse=是否有追索权
ft_pay_appdoc.iscansplit=是否可拆分
ft_pay_appdoc.subdocfm=${subdocfm}
ft_pay_appdoc.subdocto=${subdocto}
ft_pay_appdoc.docdate=${docdate}
ft_pay_appdoc.duedate=${docduedate}
ft_pay_appdoc.drawer=${drawer}
ft_pay_appdoc.accepter=${accepter}
ft_pay_appdoc.bcode=${bcode}
ft_pay_appdoc.rdoccode=应收票据认领单号
ft_pay_appdoc.rdocicode=应收票据认领单内码
ft_pay_appdoc.rdocicodeo=应收票据认领单原内码
ft_pay_appdoc.rdoccodeo=应收票据收款认领原单号
ft_pay_appdoc.pdocicode=应付票据池内码
ft_pay_appdoc.doctransflag=票据转移标识

## 实付记录表
ft_pay_fpg.fpgicode=实付记录内码
ft_pay_fpg.fpgcode=资金实付单号
ft_pay_fpg.paydatefm=支付日期从
ft_pay_fpg.paydate=支付日期
ft_pay_fpg.bankcode=银行
ft_pay_fpg.bankacccode=${bankacc}
ft_pay_fpg.paydate=支付日期
ft_pay_fpg.scy=折本位币金额
ft_pay_fpg.fcode=币种
ft_pay_fpg.fcy=支付金额
ft_pay_fpg.redflag#5=${bered}
ft_pay_fpg.redflag#2=${red}
ft_pay_fpg.bankno=网银流水号
ft_pay_fpg.recbankno=匹配收款网银流水号
ft_pay_fpg.fserate=汇率
ft_pay_fpg.docno=票据编号
ft_pay_fpg.paydatefm=支付日期从
ft_pay_fpg.paydateto=到
ft_pay_fpg.bcode=本部部门
ft_pay_fpg.isrecourse=是否有追索权
ft_pay_fpg.subdocfm=子票区间起
ft_pay_fpg.subdocto=子票区间止
ft_pay_fpg.issimulate=是否模拟
ft_pay_fpg.isdircon=是否直联
ft_pay_fpg.corpbcode=支付公司
ft_pay_fpg.rdoccode=应收票据认领单号
ft_pay_fpg.isageacp=${isageacp}
ft_pay_fpg.nstype=单据创建方式
ft_pay_fpg.srcsys=来源系统
ft_pay_fpg.srcsyscode=来源系统唯一ID
ft_pay_fpg.bankno=网银流水号
ft_pay_fpg.headbcode=本部部门
ft_pay_fpg.lrpccodetype=往来公司类别
ft_pay_fpg.finaorgz=融资机构
ft_pay_fpg.absbatchno=应付ABS批次号
ft_pay_fpg.lrpccode=往来公司
ft_pay_fpg.zcny=折人民币金额
ft_pay_fpg.zusd=折美元金额
ft_pay_fpg.bankaccount=银行账户/银行
ft_pay_fpg.paicode=付款申请内码
ft_pay_fpg.rdocicode=应收票据认领单内码
ft_pay_fpg.pdocicode=应付票据池内码
ft_pay_fpg.fpgicoder=实付记录核销内码

## 实付分摊明细
ft_pay_fpsg.pacode=付款申请单号
ft_pay_fpsg.ccode=收款方
ft_pay_fpsg.invrptype=费用类型
ft_pay_fpsg.rptype=收付项目
ft_pay_fpsg.fcy=原币金额
ft_pay_fpsg.payfcy=支付金额
ft_pay_fpsg.ptcode=协议号
ft_pay_fpsg.purprjcode=采购业务编号
ft_pay_fpsg.purordcode=采购合同号
ft_pay_fpsg.invcoder=发票登记号
ft_pay_fpsg.salprjcode=销售业务编号
ft_pay_fpsg.salordcode=销售合同号
ft_pay_fpsg.eclcode=报关单号
ft_pay_fpsg.cfmode=认定方式
ft_pay_fpsg.wcode=业务员
ft_pay_fpsg.bcode=业务员部门
ft_pay_fpsg.corpbcode=公司
ft_pay_fpsg.vprepare=创建人
ft_pay_fpsg.predate=创建时间


## 资金实付单视图
ft_pay_app_view.recbankno=${ft_pay_fpg.recbankno}

## 付款接口相关
PayResultPostBackReq.paydata=表头信息[paydata]
PayResultPostBackPayData.srcsyscode=来源系统唯一ID[srcsyscode]
PayResultPostBackPayData.isdircon=是否直联[isdircon]
PayResultPostBackPayData.fpgcode=资金实付单号[fpgcode]
PayResultPostBackPayData.srcsys=来源系统[srcsys]
PayResultPostBackPayData.paystatus=支付状态[paystatus]
PayResultPostBackPayData.bankcode=银行[bankcode]
PayResultPostBackPayData.bankacccode=银行账号[bankacccode]
PayResultPostBackPayData.paydate=支付日期[paydate]
PayResultPostBackPayData.fcode=币种[fcode]
PayResultPostBackPayData.fcy=支付金额[fcy]
PayResultPostBackPayData.headbcode=本部部门[headbcode]
PayResultPostBackPayData.issimulate=是否模拟[issimulate]
PayResultPostBackPayData.corpbcode=支付公司[corpbcode]
PayResultPostBackPayData.rdocicode=应收票据认领单内码[rdocicode]
PayResultPostBackPayData.docsrcsyscode=来源系统单据唯一流水号[docsrcsyscode]
PayResultPostBackPayData.docid=票据唯一标识ID[docid]
PayResultPostBackPayData.doccode=票据号[doccode]
PayResultPostBackPayData.docno=票据编号[docno]
PayResultPostBackPayData.subdocfm=子票区间起[subdocfm]
PayResultPostBackPayData.subdocto=子票区间止[subdocto]
PayResultPostBackPayData.drawer=出票人[drawer]
PayResultPostBackPayData.drawbankaccount=出票人账号[drawbankaccount]
PayResultPostBackPayData.drawbankcode=出票人银行[drawbankcode]
PayResultPostBackPayData.accepter=承兑人[accepter]
PayResultPostBackPayData.acpbankaccount=承兑人账号[acpbankaccount]
PayResultPostBackPayData.acpbankcode=承兑人银行[acpbankcode]
PayResultPostBackPayData.docdate=出票日期[docdate]
PayResultPostBackPayData.docfcy=票据金额[docfcy]
PayResultPostBackPayData.duedate=票据到期日[duedate]
PayResultPostBackPayData.iscansplit=是否可拆分[iscansplit]
PayResultPostBackPayData.iscantrans=是否可转让[iscantrans]
PayResultPostBackPayData.vprepare=创建人[vprepare]
PayResultPostBackPayData.predate=创建时间[predate]
PayResultPostBackPayData.paydetail=付款申请明细[paydetail]
PayResultPostBackPayDetail.nstctranno=NSTC流水号[nstctranno]
PayResultPostBackPayDetail.paicode=付款申请单内码[paicode]
PayResultPostBackPayDetail.payfcy=折申请支付金额[payfcy]
PayResultRedReq.operatetype=操作类型[operatetype]
PayResultRedReq.modifier=操作人[modifier]
OccupancyData.deptbcode=事业部编码[deptbcode]
OccupancyData.planpayfcy=计划付[planpayfcy]
OccupancyData.planrecfcy=计划收[planrecfcy]
OccupancyData.realpayfcy=实际付[realpayfcy]
OccupancyData.realrecfcy=实际收[realrecfcy]
OccupancyData.bgdate=预算日期[bgdate]


## 付款申请单凭证视图
ft_pay_appvou_view.paydatefm=实付日期从
ft_pay_appvou_view.fpgcode=资金实付单号
ft_pay_appvou_view.paydate=实付日期
ft_pay_appvou_view.sumfcy=支付金额
ft_pay_appvou_view.sumscy=折本位币金额
ft_pay_appvou_view.bankcode=${ft_pay_fpg.bankcode}
ft_pay_appvou_view.bankacccode=银行账号
ft_pay_appvou_view.headbcode=${homebcode}
ft_pay_appvou_view.issimulate=${ft_pay_fpg.issimulate}

































