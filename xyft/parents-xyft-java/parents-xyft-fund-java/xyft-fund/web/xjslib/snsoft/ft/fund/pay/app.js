Xjs.loadedXjs.push("snsoft/ft/fund/pay/app");
Xjs.namespace("snsoft.ft.pay.app.invoker");snsoft.ft.pay.app.invoker.AddPayAppgDocInvoker=function(parameter){snsoft.ft.pay.app.invoker.AddPayAppgDocInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.pay.app.invoker.AddPayAppgDocInvoker,snsoft.ext.cmd.CommandInvoker,{beforeCheck:function(event){var ccode=this.mainDataSet.getValue("ccode");if(!ccode){throw new Error(Xjs.ResBundle.getResVal("FT.00000037",Xjs.ResBundle.getResVal("ft_pay_app.ccode")));}var ishasinv=this.mainDataSet.getValue("ishasinv"),fcode=this.mainDataSet.getValue("fcode");if(!("Y"==ishasinv&&"CNY"==fcode)){throw new Error(Xjs.ResBundle.getResVal("FT-PAY.00000006",Xjs.ResBundle.getResVal("ft_pay_app.ishasinv"),Xjs.ResBundle.getResVal("YES"),Xjs.ResBundle.getResVal("ft_pay_app.fcode"),"CNY"));}return null;}});snsoft.ft.pay.app.invoker.AddRecDocInvoker=function(parameter){snsoft.ft.pay.app.invoker.AddRecDocInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.pay.app.invoker.AddRecDocInvoker,snsoft.ext.cmd.CommandInvoker,{beforeCheck:function(event){var fcode=this.mainDataSet.getValue("fcode");if(!(fcode&&"CNY"==fcode)){throw new Error(Xjs.ResBundle.getResVal("FT-PAY.00000054"));}var paymode=this.mainDataSet.getValue("paymode");if(!String.isStrIn("0080,0085,0090",paymode,",")){throw new Error(Xjs.ResBundle.getResVal("FT-PAY.00000055"));}return null;}});snsoft.ft.pay.app.invoker.DataShowChangeInvoker=function(parameter){snsoft.ft.pay.app.invoker.DataShowChangeInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.pay.app.invoker.DataShowChangeInvoker,snsoft.ext.cmd.CommandInvoker,{beforeCheck:function(event){var sfcy=this.mainDataSet.getValue("sfcy");if(!sfcy||sfcy==0){throw new Error(Xjs.ResBundle.getResVal("FT.00000029",Xjs.ResBundle.getResVal("ft_pay_app.sfcy"),"0"));}return null;}});snsoft.ft.pay.app.invoker.EditIsRecourseInvoker=function(params){snsoft.ft.pay.app.invoker.EditIsRecourseInvoker.superclass.constructor.call(this,params);};Xjs.extend(snsoft.ft.pay.app.invoker.EditIsRecourseInvoker,snsoft.ft.comm.cmdreg.CellsModifyInvoker,{beforeCheck:function(event){var rows=this.table.getSelectedRowNumbers();if(rows==null||rows.length==0){throw new Error(Xjs.ResBundle.getResVal("FT.00000048"));}if(rows.length>1){for(var i=1;i<rows.length;i++){if(!(this.dataSet.getValue("isrecourse",rows[0])==this.dataSet.getValue("isrecourse",rows[i]))){throw new Error(Xjs.ResBundle.getResVal("FT-PAY.00000092"));}}}var fcode=this.mainDataSet.getValue("fcode");if(!(fcode&&"CNY"==fcode)){throw new Error(Xjs.ResBundle.getResVal("FT-PAY.00000054"));}var paymode=this.mainDataSet.getValue("paymode");if(!String.isStrIn("0080,0085,0090",paymode,",")){throw new Error(Xjs.ResBundle.getResVal("FT-PAY.00000055"));}return null;},invoke:function(event){var invokeParam=this.buildInvokeParam(null);invokeParam.mains=[];var keyVals=Xjs.util.DataSetUtils.getValuesTo(this.mainDataSet,0,null);invokeParam.mains.push(keyVals);var padocicode=[],rows=this.table.getSelectedRowNumbers();for(var r=0;r<rows.length;r++){padocicode.push(this.dataSet.getValue("padocicode",rows[r]));}invokeParam.padocicode=padocicode;var defvaluesCol=event.checkData.defvaluesCol;invokeParam.isRecourse=defvaluesCol.isrecourse;;return invokeParam;}});snsoft.ft.pay.app.invoker.EntryMergePayInvoker=function(parameter){snsoft.ft.pay.app.invoker.EntryMergePayInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.pay.app.invoker.EntryMergePayInvoker,snsoft.ext.cmd.CommandInvoker,{beforeCheck:function(event){var rows=this.table.getSelectedRowNumbers();if(rows==null||rows.length==0){throw new Error(Xjs.ResBundle.getResVal("FT.00000048"));}for(var i=0;i<rows.length;i++){var r=rows[i],status=this.dataSet.getValue("status",r),isneedpfund=this.dataSet.getValue("isneedpfund",r),isoverpay=this.dataSet.getValue("isoverpay",r);if(!(status=="60")){throw new Error(Xjs.ResBundle.getResVal("FT-PAY.00000014"));}if(isneedpfund=="N"){throw new Error(Xjs.ResBundle.getResVal("FT-PAY.00000015"));}if(isoverpay=="N"){throw new Error(Xjs.ResBundle.getResVal("FT-PAY.00000060"));}}var paicodes=Xjs.util.TableUtils.getSelectedColumnValues(this.table,"paicode",true);return {paicodes:paicodes};},invoke:function(event){var cmd=event.cfg.cmd;if(cmd=="mergePay")this.oncmd_mergePay(this.table,event);return null;},oncmd_mergePay:function(table,event){var mv={};mv.innercode=this.dataSet.getValue("paicode");mv.sheetcode=this.dataSet.getValue("sheetcode");mv.modifydate=this.dataSet.getValue(String.obj2str(this.dataSet.modifyDateColumn,"modifydate"));var paicodes=Xjs.util.TableUtils.getSelectedColumnValues(table,"paicode",true),service=Xjs.RInvoke.newBean("FT-PAY.PayAppUIService");service.mergePay(paicodes,mv,true);var dlgTitle=snsoft.ft.utils.FTUtils.getBtnTitle(table,event.cfg.cmd);Xjs.ui.UIUtil.showConfirmDialog(dlgTitle,Xjs.ResBundle.getResVal("C001",dlgTitle),new Xjs.FuncCall(this.mergePay,this,[paicodes,mv]),null);},mergePay:function(dialogPane,cmd,paicodes,mv){if("ok"==cmd){var service=Xjs.RInvoke.newBean("FT-PAY.PayAppUIService");service.mergePay(paicodes,mv,false);this.table.refreshTable();}}});snsoft.ft.pay.app.invoker.FinPayBmpCheckInvoker=function(parameter){snsoft.ft.pay.app.invoker.FinPayBmpCheckInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.pay.app.invoker.FinPayBmpCheckInvoker,snsoft.ext.cmd.CommandInvoker,{checkSelRow:function(table){var rows=table.getSelectedRowNumbers();if(rows==null||rows.length==0){throw new Error(Xjs.ResBundle.getResVal("FT.00000048"));}},beforeCheck:function(event){this.checkSelRow(event.table);var invokeParam=this.buildInvokeParam(null),rows=this.table.getSelectedRowNumbers(0),innercodes=Xjs.util.DataSetUtils.getColumnStrValues_DataSet_int_String(this.dataSet,"fpgicode",rows,",");invokeParam.innercodes=innercodes;invokeParam.sheetcode=this.sheetcode;return invokeParam;},check:function(event){var bmpcodes=event.beforeCheckRtnVal.bmpcodes,nobmpdatas=event.beforeCheckRtnVal.nobmpdatas,tip=event.beforeCheckRtnVal.tip;if(bmpcodes||nobmpdatas){var muiid="SNA-ACC.RelaBmpChkPop",initVals={param:{bmpcodes:bmpcodes,nobmpdatas:nobmpdatas,tip:tip}},dlg=Xjs.ui.UIUtil.loadDialog(muiid,0,Xjs.ui.Panel.newCloseButton(),null,null,initVals);dlg.title=this.cfg.title;dlg.showModal();throw {dummy:true};}return null;}});snsoft.ft.pay.app.invoker.FinPayRedInvoker=function(parameter){snsoft.ft.pay.app.invoker.FinPayRedInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.pay.app.invoker.FinPayRedInvoker,snsoft.ext.cmd.CommandInvoker,{check:function(event){;var rows=this.table.getSelectedRowNumbers();if(rows==null||rows.length==0){throw new Error(Xjs.ResBundle.getResVal("FT.00000048"));}var fpgicodeArray=[];for(var i=0;i<rows.length;i++){if(this.table.getValue("nstype",rows[i])!="10"){throw new Error(Xjs.ResBundle.getResVal("FT-PAY.00000028"));}if(this.table.getValue("redflag",rows[i])!=null&&this.table.getValue("redflag",rows[i])!=""){throw new Error(Xjs.ResBundle.getResVal("FT-PAY.00000029"));}fpgicodeArray.push(this.table.getValue("fpgicode",rows[i]));}return this.newDialogWithFunc(event,(e)=>{var dialogPane=Xjs.ui.UIUtil.loadDialog("FT-PAY.FinPayRecordRedDialog",0,null,null,null);dialogPane.setWidth("auto");dialogPane.setHeight("auto");dialogPane.title=this.cfg.title;dialogPane.showModal();return dialogPane;},(e,d)=>{var srcTable=d.getItemByName("ft_pay_fpg"),dlgDataSet=srcTable.getDataSet();srcTable.postPending();if(!srcTable.dataSet.isChanged(false))srcTable.dataSet.setRowChanged(true);srcTable.checkNonBlankForSubmit();var tcaptime=dlgDataSet.getValue("tcaptime"),param={};param.fpgicodelist=fpgicodeArray;param.paicode=this.mainDataSet.getValue("paicode");param.tcaptime=tcaptime;event.checkData.param=param;});},invoke:function(event){var parameter={};parameter.param=event.checkData.param;return parameter;},afterInvoke:function(event){var row=this.mainTable.getDataSet().getRow();this.mainTable.refreshTable();this.mainTable.getDataSet().gotoRow(row);}});snsoft.ft.pay.app.invoker.FinPayWorkBenchInvoker=function(parameter){snsoft.ft.pay.app.invoker.FinPayWorkBenchInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.pay.app.invoker.FinPayWorkBenchInvoker,snsoft.ext.cmd.com.biz.DialogInvokerCreate,{beforeCheck:function(event){;var rows=this.mainTable.getSelectedRowNumbers();if(rows==null||rows.length==0){throw new Error(Xjs.ResBundle.getResVal("FT.00000048"));}if(rows.length!=1){throw new Error(Xjs.ResBundle.getResVal("FT-PAY.00000027"));}var paymode=this.mainDataSet.getValue("paymode",rows[0]);if(paymode!="0105"){throw new Error(Xjs.ResBundle.getResVal("FT-PAY.00000026"));}return null;},getDlgData:function(dlgDataSet){var dlgData={},columns=dlgDataSet.columns;for(var column of columns){dlgData[column.name]=dlgDataSet.getValue(column.name);}return dlgData;},invoke:function(event){snsoft.ft.pay.app.invoker.FinPayWorkBenchInvoker.superclass.invoke.call(this,event);var rows=this.mainTable.getSelectedRowNumbers(),paytype=null,paymodeCodeData=this.mainTable.getColumn("paymode").selectOptions;for(var j=0;j<paymodeCodeData.getData().length;j++){if(paymodeCodeData.getData()[j][0]=="0105")paytype=paymodeCodeData.getData()[j][3];}var dlgDataSet=this.dlg.getMainTable().getDataSet(),unfcy=snsoft.ft.utils.FTUtils.sub(this.mainDataSet.getValue("fcy",rows[0]),this.mainDataSet.getValue("sfcy",rows[0]));dlgDataSet.setValue("paytype",paytype);dlgDataSet.setValue("fcy",unfcy);dlgDataSet.setValue("paicode",this.mainDataSet.getValue("paicode",rows[0]));dlgDataSet.setValue("fcode",this.mainDataSet.getValue("fcode",rows[0]));dlgDataSet.setValue("payappfcy",this.mainDataSet.getValue("fcy",rows[0]));dlgDataSet.setValue("srcsys","CTRM");dlgDataSet.setValue("nstype","10");dlgDataSet.setValue("corpbcode",this.mainDataSet.getValue("corpbcode",rows[0]));dlgDataSet.setValue("fcode",this.mainDataSet.getValue("fcode",rows[0]));dlgDataSet.setValue("wcode",this.mainDataSet.getValue("wcode",rows[0]));dlgDataSet.setValue("bcode",this.mainDataSet.getValue("bcode",rows[0]));var v_corpbcode=this.mainDataSet.getValue("corpbcode",rows[0]),v_fcode=this.mainDataSet.getValue("fcode",rows[0]),v_wcode=this.mainDataSet.getValue("wcode",rows[0]),tables=dlgDataSet.getTables(),tc=tables[0].getColumn("bankacccode"),dialog=tc.getAidInputer();dialog.getDOM();var table=dialog.getTable();table.queryParam.setItemValue("corpbcode",v_corpbcode);table.queryParam.setItemValue("fcode",v_fcode);table.queryParam.setItemValue("wcode",v_wcode);table.refreshTable();var cbankDataSet=table.dataSet;if(cbankDataSet.getRowCount()==1)dlgDataSet.setValue("bankacccode",cbankDataSet.getValue("bankaccount"));return null;},onOk:function(root,event){var afterinvoke=this.getDlgData(root.dataSet),remoteInvoke=this.remoteInvoke("afterInvoke",afterinvoke),row=this.mainTable.getDataSet().getRow();this.mainTable.refreshTable();this.mainTable.getDataSet().gotoRow(row);}});snsoft.ft.pay.app.invoker.IsReferableEditInvoker=function(parameter){snsoft.ft.pay.app.invoker.IsReferableEditInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.pay.app.invoker.IsReferableEditInvoker,snsoft.ext.cmd.CommandInvoker,{invoke:function(event){var isreferable=this.dataSet.getValue("isreferable");if("N"==isreferable)this.mainTable.checkNonBlankForSubmit();return null;}});snsoft.ft.pay.app.invoker.NewForexDevyappInvoker=function(parameter){snsoft.ft.pay.app.invoker.NewForexDevyappInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.pay.app.invoker.NewForexDevyappInvoker,snsoft.ext.cmd.CommandInvoker,{beforeCheck:function(event){var rows=this.table.getSelectedRowNumbers();if(rows==null||rows.length==0){throw new Error(Xjs.ResBundle.getResVal("FT.00000048"));}if(rows.length>1){throw new Error(Xjs.ResBundle.getResVal("FT-PAY.00000051",Xjs.ResBundle.getResVal("title_FT-FOREX.ForexDelyAppDetail")));}this.dataSet.saveChanges();var srcInnerCodes=new Array(rows.length);for(var i=0;i<rows.length;i++){srcInnerCodes[i]=this.dataSet.getValue(this.dataSet.getCuInnerfld(),rows[i]);}return {srcInnerCodes:srcInnerCodes,sheetcode:this.mainDataSet.getValue("sheetcode"),innercode:this.mainDataSet.getValue(this.mainDataSet.getCuInnerfld())};},invoke:Xjs.nullFn});snsoft.ft.pay.app.invoker.NewFundpoolappInvoker=function(parameter){snsoft.ft.pay.app.invoker.NewFundpoolappInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.pay.app.invoker.NewFundpoolappInvoker,snsoft.ext.cmd.CommandInvoker,{beforeCheck:function(event){var rows=this.table.getSelectedRowNumbers();if(rows==null||rows.length==0){throw new Error(Xjs.ResBundle.getResVal("FT.00000048"));}if(rows.length>1){throw new Error(Xjs.ResBundle.getResVal("FT-PAY.00000051",Xjs.ResBundle.getResVal("title_FT-CPS.CpsUsePayAppDetail")));}this.dataSet.saveChanges();var srcInnerCodes=new Array(rows.length);for(var i=0;i<rows.length;i++){srcInnerCodes[i]=this.dataSet.getValue(this.dataSet.getCuInnerfld(),rows[i]);}return {srcInnerCodes:srcInnerCodes,sheetcode:this.mainDataSet.getValue("sheetcode"),innercode:this.mainDataSet.getValue(this.mainDataSet.getCuInnerfld())};},invoke:function(event){return event.beforeCheckParam;},afterInvoke:function(event){this.table.refreshTable();var beforeCheckRtnVal=event.invokeRtnVal,upaicode=beforeCheckRtnVal.upaicode,sheetService=Xjs.RInvoke.newBean("SN-Busi.SheetService"),busiObject=sheetService.getBusiObject("FT-CPS.CpsUsePayApp"),data={sheetcode:busiObject.sheetcode};data[busiObject.innerfld]=upaicode;snsoft.plat.bas.viewdetail.ViewDetailListener.invokeViewDetail(data,busiObject.outerfld);}});snsoft.ft.pay.app.invoker.PayAppConfWorkBenchBtnInvoker=function(parameter){snsoft.ft.pay.app.invoker.PayAppConfWorkBenchBtnInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.pay.app.invoker.PayAppConfWorkBenchBtnInvoker,snsoft.ext.cmd.CommandInvoker,{invoke:function(event){if(event.cfg.cmd=="confed")this.oncmd_confed(this.table);return null;},oncmd_confed:function(table){var rows=table.getSelectedRowNumbers();if(rows==null||rows.length==0){throw new Error(Xjs.ResBundle.getResVal("FT.00000048"));}var mv={};mv.innercode=this.dataSet.getValue("paicode");mv.sheetcode=this.dataSet.getValue("sheetcode");mv.modifydate=this.dataSet.getValue(String.obj2str(this.dataSet.modifyDateColumn,"modifydate"));var paicodes=Xjs.util.TableUtils.getSelectedColumnValues(table,"paicode",false),service=Xjs.RInvoke.newBean("FT-PAY.PayAppConfWorkBenchUIService");service.confed(paicodes,mv,true);var node=table.getMenuNode("confed").node,title="";if(node&&node.attachedDom)title=node.attachedDom.innerText;Xjs.ui.UIUtil.showConfirmDialog(title,Xjs.ResBundle.getResVal("FT-PAY.00000001"),new Xjs.FuncCall(this.confed,this,[paicodes,mv]),null);},confed:function(dialog,command,paicodes,mv){if("ok"==command){var service=Xjs.RInvoke.newBean("FT-PAY.PayAppConfWorkBenchUIService");service.confed(paicodes,mv,false);this.table.refreshTable();}}});snsoft.ft.pay.app.invoker.PayAppDocDeleteInvoker=function(parameter){snsoft.ft.pay.app.invoker.PayAppDocDeleteInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.pay.app.invoker.PayAppDocDeleteInvoker,snsoft.ext.cmd.CommandInvoker,{beforeCheck:function(event){var rdocicodeList=[],rows=event.table.getSelectedRowNumbers();if(rows.length>0){for(var i=0,len=rows.length;i<len;i++){var rdocicode=this.judgeDelete(rows[i]);if(rdocicode)rdocicodeList.push(rdocicode);}}else {var rdocicode=this.judgeDelete(this.dataSet.rowAt);if(rdocicode)rdocicodeList.push(rdocicode);}if(rdocicodeList.length>0){var invokeParam=this.buildInvokeParam(null);invokeParam.rdocicodeList=rdocicodeList;return invokeParam;}return null;},judgeDelete:function(row){var pdocicode=this.dataSet.getValue("pdocicode",row),rdocicode=this.dataSet.getValue("rdocicode",row);if(pdocicode){if(this.mainDataSet.getValue("status")>=20){throw new Error(this.getResVal("FT.00000338"));}}if(rdocicode){if(this.mainDataSet.getValue("status")>=20){if(!String.isStrIn("22,60",this.mainDataSet.getValue("status"),",")||!(this.mainDataSet.getValue("isadddoc")=="Y")){throw new Error(this.getResVal("FT-PAY.00000058"));}}return rdocicode;}return null;}});snsoft.ft.pay.app.invoker.PayAppFundSrcDeleteInvoker=function(parameter){snsoft.ft.pay.app.invoker.PayAppFundSrcDeleteInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.pay.app.invoker.PayAppFundSrcDeleteInvoker,snsoft.ext.cmd.CommandInvoker,{supFundsrcFlag:"N",beforeCheck:function(event){var flag=this.mainDataSet.getValue("status")<20&&this.mainDataSet.getValue("isreferable")=="N"&&this.mainDataSet.getValue("issimulate")=="N";if(!flag){throw new Error(this.getResVal("FT-PAY.00000013"));}if(this.dataSet.getValue("srcismanual")=="N"&&this.dataSet.getValue("srccode")){throw new Error(this.getResVal("FT-PAY.00000013"));}return null;}});snsoft.ft.pay.app.invoker.PayAppGenByPurOrdInvoker=function(parameter){snsoft.ft.pay.app.invoker.PayAppGenByPurOrdInvoker.superclass.constructor.call(this,parameter);this.copyMap=parameter.copyMap;this.paramItemsCtrl=parameter.paramItemsCtrl;};Xjs.extend(snsoft.ft.pay.app.invoker.PayAppGenByPurOrdInvoker,snsoft.ext.cmd.CommandInvoker,{muiid:"FT-POD.PurOrdGPDialog",check:function(event){return this.newDialogWithFunc(event,(e)=>{var uiid=Xjs.ui.UIUtil.getUiid(this.muiid),initVals={param:{status:"26,70"}},dlg=Xjs.ui.UIUtil.loadDialog(uiid,0,null,null,null,initVals);dlg.setWidth("auto");dlg.setHeight("80%");dlg.title=this.getTitle();var dlgtbl=snsoft.ft.utils.FTUtils.getTableFromPanel(dlg);if(dlgtbl)dlgtbl._$fpcopytable=this.table;this.addExtCtrlAttrs(this.table,dlg,event);dlg.beforeShowing=this.resetBeforeShowing.createDelegate(this,[dlg,dlgtbl],true);dlg.addListener("onShowing",new Xjs.FuncCall(this.resetOnShowing,this,[dlgtbl]));dlg.showModal();return dlg;},(e,d)=>{e.checkData.showdlg=d;var dlgTable=snsoft.ft.utils.FTUtils.getTableFromPanel(d),icodeS=snsoft.ft.utils.FTUtils.getSelectedColumnValuesCaseChild(dlgTable,"purordicode",false);if(!icodeS||icodeS.length==0){throw new Error(Xjs.ResBundle.getString("UI","Aid.Dlg.NulValueSelected"));}});},addExtCtrlAttrs:function(table,dialog,event){var param=dialog.getItemByName("param");if(this.copyMap){for(var toName in this.copyMap){var fromName=this.copyMap[toName];param.setItemValue(toName,this.dataSet.getValue(fromName));}}if(this.paramItemsCtrl)this.setDlgParamCtrl(dialog);},setDlgParamCtrl:function(dialog){var qryPane=dialog.getItemByName("param");for(var name in this.paramItemsCtrl){var attrs=this.paramItemsCtrl[name];qryPane.setItemConfigs(name,attrs);}qryPane.relayoutItems(true);},invoke:function(event){var dlg=event.checkData.showdlg,dlgTable=snsoft.ft.utils.FTUtils.getTableFromPanel(dlg),selrow=dlgTable.getSelectedRowNumbers(0),allSelected=[];for(var i of selrow){allSelected.push([dlgTable.getDataSet().getValue("purordicode",i),dlgTable.getDataSet().getValue("prjicode",i)]);}return {allSelected:allSelected};},afterInvoke:function(event){var paicodes=event.invokeRtnVal.paicodes,data={sheetEntry:true,sheetcode:"FT-PAY.PayApp",paicode:paicodes.join(",")};snsoft.plat.bas.viewdetail.ViewDetailListener.invokeViewDetail(data,"pacode");},resetBeforeShowing:function(dlg,dlgTable){snsoft.ft.utils.FTUtils.hiddenPaneCollapseBtns(dlgTable.queryParam);snsoft.ft.utils.FTUtils.hiddenSaveLoadValueBtns(dlgTable.queryParam);},resetOnShowing:function(dlg,dlgtbl){snsoft.ft.utils.FTUtils.resetBackupVal(dlg,dlgtbl);snsoft.ft.utils.FTUtils.addAidsSheetfilter(dlgtbl,"FT-PAY.PayApp",null);dlgtbl.refreshTableIfOK();if(dlg&&dlg.dom&&Xjs.DOM.findById("PaneCollapseBtn2",dlg.dom))snsoft.ft.utils.FTUtils.setWidthfmOffsetWidth(dlg);var listener=this.table.getListener(snsoft.plat.bas.viewdetail.ViewDetailListener);if(listener)listener.onTableDblClick=(t,e)=>{};}});snsoft.ft.pay.app.invoker.PayAppGenPayOffInvoker=function(parameter){snsoft.ft.pay.app.invoker.PayAppGenPayOffInvoker.superclass.constructor.call(this,parameter);this.tgtsheetcode=parameter.tgtsheetcode;this.copycode=parameter.copycode;this.isAdj=this.tgtsheetcode=="FT-RPADJ.AdjPayOffSingle";};Xjs.extend(snsoft.ft.pay.app.invoker.PayAppGenPayOffInvoker,snsoft.ext.cmd.CommandInvoker,{beforeCheck:function(event){var invokeParam=this.getInvokeParam();invokeParam.invokeMethod="beforeCheck";return invokeParam;},check:function(event){var offcodes=event.beforeCheckRtnVal.offcodes;if(offcodes){var val=Xjs.ResBundle.getResVal("FT-PAY.00000046",Xjs.ResBundle.getResVal(this.isAdj?"title_FT-RPADJ.AdjPayOff":"title_FT-RPADJ.AdvPayOffDetail"),offcodes);return {type:0,title:this.getTitle(),prompt:val};}return null;},invoke:function(event){var invokeParam=this.getInvokeParam();invokeParam.invokeMethod="invoke";invokeParam.tgtsheetcode=this.tgtsheetcode;invokeParam.copycode=this.copycode;return invokeParam;},afterInvoke:function(event){snsoft.plat.bas.viewdetail.ViewDetailListener.invokeViewDetail(event.invokeRtnVal,"offcode");},getInvokeParam:function(){var invokeParam=this.buildInvokeParam(null),rows=this.table.getSelectedRowNumbers(0);if(rows.length==0){throw new Error(Xjs.ResBundle.getString("UI","Aid.Dlg.NulValueSelected"));}invokeParam.mains=[];for(var r=0;r<rows.length;r++){var keyVals=Xjs.util.DataSetUtils.getValuesTo(this.dataSet,rows[r],null);invokeParam.mains.push(keyVals);}invokeParam.isAdj=this.isAdj;return invokeParam;}});snsoft.ft.pay.app.invoker.PayAppPushFundBatchInvoker=function(parameter){snsoft.ft.pay.app.invoker.PayAppPushFundBatchInvoker.superclass.constructor.call(this,parameter);this.isEntry=String.obj2bool(parameter.isEntry,false);};Xjs.extend(snsoft.ft.pay.app.invoker.PayAppPushFundBatchInvoker,snsoft.ext.cmd.CommandInvoker,{beforeCheck:function(event){if(this.isEntry){var rows=this.table.getSelectedRowNumbers();if(rows==null||rows.length==0){throw new Error(Xjs.ResBundle.getResVal("FT.00000048"));}for(var i=0;i<rows.length;i++){var r=rows[i],status=this.dataSet.getValue("status",r),isneedpfund=this.dataSet.getValue("isneedpfund",r);if(!(status=="60"||status=="59")){throw new Error(Xjs.ResBundle.getResVal("FT-PAY.00000088"));}if(isneedpfund=="N"){throw new Error(Xjs.ResBundle.getResVal("FT-PAY.00000015"));}}}else {var status=this.dataSet.getValue("status"),isneedpfund=this.dataSet.getValue("isneedpfund");if(!(status=="60"||status=="59")){throw new Error(Xjs.ResBundle.getResVal("FT-PAY.00000089"));}if(isneedpfund=="N"){throw new Error(Xjs.ResBundle.getResVal("FT-PAY.00000090"));}}return null;},invoke:function(event){var cmd=event.cfg.cmd;if(cmd=="pushFund"||cmd=="pushFundBatch")this.oncmd_pushFundBatch(this.table,event);return null;},oncmd_pushFundBatch:function(table,event){var paicodes,mv={},sheetcode=this.dataSet.getValue("sheetcode");mv.innercode=this.dataSet.getValue("paicode");mv.sheetcode=sheetcode;mv.modifydate=this.dataSet.getValue(String.obj2str(this.dataSet.modifyDateColumn,"modifydate"));if(this.isEntry)paicodes=Xjs.util.TableUtils.getSelectedColumnValues(table,"paicode",true);else paicodes=[this.mainDataSet.getValue("paicode")];var service=Xjs.RInvoke.newBean("FT-PAY.PayAppUIService");service.pushFund(paicodes,mv,true,this.isEntry);var dlgTitle=snsoft.ft.utils.FTUtils.getBtnTitle(table,event.cfg.cmd);Xjs.ui.UIUtil.showConfirmDialog(dlgTitle,Xjs.ResBundle.getResVal("C001",dlgTitle),new Xjs.FuncCall(this.pushFundBatch,this,[paicodes,mv]),null);},pushFundBatch:function(dialogPane,cmd,paicodes,mv){if("ok"==cmd){var service=Xjs.RInvoke.newBean("FT-PAY.PayAppUIService");service.pushFund(paicodes,mv,false,this.isEntry);this.table.refreshTable();}}});snsoft.ft.pay.app.invoker.PayAppQueryResultBtnInvoker=function(param){snsoft.ft.pay.app.invoker.PayAppQueryResultBtnInvoker.superclass.constructor.call(this,param);};Xjs.extend(snsoft.ft.pay.app.invoker.PayAppQueryResultBtnInvoker,snsoft.ext.cmd.CommandInvoker,{beforeCheck:function(event){var rows=this.table.getSelectedRowNumbers(),paymode=this.dataSet.getValue("paymode",rows[0]),ishasinv=this.dataSet.getValue("ishasinv",rows[0]);if(!(String.isStrIn("0045,0050,0055",paymode)&&"N"==ishasinv)){throw Xjs.ResBundle.getResVal("FT-PAY.00000061");}return null;}});snsoft.ft.pay.app.invoker.PayAppShareFcyInvoker=function(parameter){snsoft.ft.pay.app.invoker.PayAppShareFcyInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.pay.app.invoker.PayAppShareFcyInvoker,snsoft.ext.cmd.CommandInvoker,{beforeCheck:Xjs.nullFn});snsoft.ft.pay.app.invoker.PayAppSubmitInvoker=function(parameter){snsoft.ft.pay.app.invoker.PayAppSubmitInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.pay.app.invoker.PayAppSubmitInvoker,snsoft.ext.cmd.CommandInvoker,{afterInvoke:function(event){this.table.refreshTable();}});snsoft.ft.pay.app.invoker.PayAppSyncPayInfoJSInvoker=function(parameter){snsoft.ft.pay.app.invoker.PayAppSyncPayInfoJSInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.pay.app.invoker.PayAppSyncPayInfoJSInvoker,snsoft.ext.cmd.CommandInvoker,{beforeCheck:function(event){event.table.dataSet.postRow();this.table.checkNonBlankForSubmit();var invokeParam=this.buildInvokeParam(null);invokeParam.invokeMethod="beforeCheck";invokeParam.paicode=this.dataSet.getValue("paicode");return invokeParam;},check:function(event){var btnTitle=snsoft.ft.utils.FTUtils.getBtnTitle(event.table,event.cfg.cmd),message=Xjs.ResBundle.getResVal("C001",event.cfg.title);return {type:0,title:btnTitle,prompt:message};},invoke:function(event){var invokeParam=this.buildInvokeParam(null);invokeParam.invokeMethod="invoke";invokeParam.paicode=this.dataSet.getValue("paicode");return invokeParam;},afterInvoke:function(event){this.mainTable.refreshTable(true,0,null);}});snsoft.ft.pay.app.invoker.PullOrdDocInvoker=function(parameter){snsoft.ft.pay.app.invoker.PullOrdDocInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.pay.app.invoker.PullOrdDocInvoker,snsoft.ext.cmd.CommandInvoker,{uiid:"FT-PAY.PullOrdDocDialog",beforeCheck:function(event){var invokeParam=this.buildInvokeParam(null),jsObject=invokeParam.mains[0];jsObject.sheetcode=this.mainDataSet.getValue("sheetcode");invokeParam.invokeMethod="beforeCheck";return invokeParam;},check:function(event){return this.newDialogWithFunc(event,(e)=>{var dlg=Xjs.ui.UIUtil.loadDialog(this.uiid);dlg.title=this.getTitle();var table=dlg.getMainComponents(Xjs.ui.GridTable)[0];table.dataSet.setRefreshParameter("cuicode",this.mainDataSet.getValue("cuicode"));table.dataSet.setRefreshParameter("innercode",this.mainDataSet.getValue("paicode"));table.dataSet.setRefreshParameter("sheetcode",this.mainDataSet.getValue("sheetcode"));table.dataSet.setRefreshParameter("dmtblname",this.dmtblname);dlg.showModal();table.refreshTable();return dlg;},(e,d)=>{var table=d.getMainComponents(Xjs.ui.GridTable)[0],dataSet=table.getDataSet(),rows=table.getSelectedRowNumbers(0);if(rows.length==0){throw new Error(Xjs.ResBundle.getString("UI","Aid.Dlg.NulValueSelected"));}var docicodes=[];for(var r=0;r<rows.length;r++){docicodes.push(dataSet.getValue("docicode",rows[r]));}event.checkData.docicodes=docicodes;});},invoke:function(event){var invokeParam=this.buildInvokeParam(null),jsObject=invokeParam.mains[0];jsObject.sheetcode=this.mainDataSet.getValue("sheetcode");invokeParam.invokeMethod="invoke";invokeParam.docicodes=event.checkData.docicodes;return invokeParam;},afterInvoke:function(event){var atplat_sheetdocTable=Xjs.util.TableUtils.getTable_$Panel_$String(this.mainTable,"atplat_sheetdoc");if(atplat_sheetdocTable)atplat_sheetdocTable.refreshTable();}});Xjs.namespace("snsoft.ft.pay.app.lis");snsoft.ft.pay.app.lis.FinPayDetailFrateJSListener=function(params){snsoft.ft.pay.app.lis.FinPayDetailFrateJSListener.superclass.constructor.call(this,params);};Xjs.extend(snsoft.ft.pay.app.lis.FinPayDetailFrateJSListener,snsoft.plat.bas.busi.SystemFunctionListener,{initComponent:function(table,values){snsoft.ft.pay.app.lis.FinPayDetailFrateJSListener.superclass.initComponent.call(this,table,values);this.mainTable=this.getTable(table,"ft_pay_fpg");this.mainDataSet=this.mainTable.getDataSet();this.service=Xjs.RInvoke.newBean("FT-PAY.FinPayUIService");},dataSetFieldPosted:function(dataSet,event){snsoft.ft.pay.app.lis.FinPayDetailFrateJSListener.superclass.dataSetFieldPosted.call(this,dataSet,event);if(event.columnName=="paydate"||event.columnName=="fcy"){var paydate=this.mainDataSet.getValue("paydate"),paicode=this.mainDataSet.getValue("paicode"),fcy=this.mainDataSet.getValue("fcy");if(!paydate||!paicode||!fcy)return;var finPayDetailFrates=this.service.getFinPayDetailFrates(paydate,paicode,fcy);if(finPayDetailFrates!=null){var fserate=finPayDetailFrates.fserate,scerate=finPayDetailFrates.scerate,suerate=finPayDetailFrates.suerate;this.mainDataSet.setValue("fserate",fserate);this.mainDataSet.setValue("scerate",scerate);this.mainDataSet.setValue("suerate",suerate);var scy=finPayDetailFrates.scy,zcny=finPayDetailFrates.zcny,zusd=finPayDetailFrates.zusd;this.mainDataSet.setValue("scy",scy);this.mainDataSet.setValue("zcny",zcny);this.mainDataSet.setValue("zusd",zusd);this.mainDataSet.setValue("tcaptime",this.mainDataSet.getValue("paydate"));}}}});snsoft.ft.pay.app.lis.PayAppCheckFldDefaultJSListener=function(params){snsoft.ft.pay.app.lis.PayAppCheckFldDefaultJSListener.superclass.constructor.call(this,params);};Xjs.extend(snsoft.ft.pay.app.lis.PayAppCheckFldDefaultJSListener,snsoft.plat.bas.busi.SystemFunctionListener,{initComponent:function(table,values){snsoft.ft.pay.app.lis.PayAppCheckFldDefaultJSListener.superclass.initComponent.call(this,table,values);this.basTable=this.getTable(table,"ft_pay_app_bas");this.basDataSet=this.basTable.getDataSet();this.gTable=this.getTable(table,"ft_pay_appg");this.gDataSet=this.gTable.getDataSet();},dataSetFieldPosted:function(dataSet,event){snsoft.ft.pay.app.lis.PayAppCheckFldDefaultJSListener.superclass.dataSetFieldPosted.call(this,dataSet,event);if(event.columnName=="fcode")this.setGDataSetFieldValue(dataSet.getValue(event.columnIndex),"fcode");},setGDataSetFieldValue:function(value,fieldName){for(var i=0;i<this.gDataSet.getRowCount();i++){if(!this.gDataSet.getValue(fieldName,i)){this.gDataSet.gotoRow(i);this.gDataSet.setValue(fieldName,value);}}}});snsoft.ft.pay.app.lis.PayAppColumnControlJSListener=function(params){snsoft.ft.pay.app.lis.PayAppColumnControlJSListener.superclass.constructor.call(this,params);};Xjs.extend(snsoft.ft.pay.app.lis.PayAppColumnControlJSListener,snsoft.plat.bas.busi.SystemFunctionListener,{initComponent:function(table,values){snsoft.ft.pay.app.lis.PayAppColumnControlJSListener.superclass.initComponent.call(this,table,values);this.mainTable=this.getTable(table,"ft_pay_app");this.mainDataSet=this.mainTable.getDataSet();this.payDocTable=this.getTable(table,"ft_pay_appdoc");this.payDocDataSet=this.payDocTable.getDataSet();this.basTable=this.getTable(table,"ft_pay_app_bas");this.basDataSet=this.basTable.getDataSet();this.gTable=this.getTable(table,"ft_pay_appg");this.gDataSet=this.gTable.getDataSet();this.srcTable=this.getTable(table,"ft_pay_appsrc");this.srcDataSet=this.srcTable.getDataSet();this.appOverTable=this.getTable(table,"ft_pay_app_over");this.appOverDataSet=this.appOverTable.getDataSet();},dataLoaded:function(dataSet,e){snsoft.ft.pay.app.lis.PayAppColumnControlJSListener.superclass.dataLoaded.call(this,dataSet,e);this.controlDocRate();},dataSetRowNavigated:function(dataSet,e){snsoft.ft.pay.app.lis.PayAppColumnControlJSListener.superclass.dataSetRowNavigated.call(this,dataSet,e);this.controlDocRate();},dataSetFieldPosted:function(dataSet,event){snsoft.ft.pay.app.lis.PayAppColumnControlJSListener.superclass.dataSetFieldPosted.call(this,dataSet,event);if(event.columnName=="bcode")this.controlDocRate();this.controlPayDoc(event);if("ccode"==event.columnName)this.gDataSet.deleteAllRows();if("ccode"==event.columnName||"fcode"==event.columnName)this.bankAccountValue(event);this.controlSrc(event);if(event.columnName=="paymode")this.isneedpfundCtrl(event);this.isOverPayCtrl(event);},isOverPayCtrl:function(event){if(String.isStrIn("ccode,paymode,corpbcode,fcode",event.columnName,",")){var paymode=this.mainDataSet.getValue("paymode"),ccode=this.mainDataSet.getValue("ccode"),corpbcode=this.mainDataSet.getValue("corpbcode"),fcode=this.mainDataSet.getValue("fcode"),a_flag=String.isStrIn("0005,0020,0035",paymode,","),c_flag=false;if(ccode){var service=Xjs.RInvoke.newBean("FT-PAY.PayAppUIService");c_flag=service.checkOrCondition(ccode,corpbcode);}var b_flag=!(fcode=="CNY")||c_flag;if(a_flag&&b_flag)this.mainDataSet.setValue("isoverpay","Y");else this.mainDataSet.setValue("isoverpay","N");}},isneedpfundCtrl:function(event){var paymode=this.mainDataSet.getValue("paymode");if(String.isStrIn("0105,0110,0115,0025,0030,0060,0065,0070,0075",paymode,","))this.basDataSet.setValue("isneedpfund","N");else this.basDataSet.setValue("isneedpfund","Y");},bankAccountValue:function(event){this.mainDataSet.setValue("bankaccount",null);this.mainDataSet.setValue("baicode",null);this.mainDataSet.setValue("bankname",null);this.mainDataSet.setValue("bankaccountname",null);this.mainDataSet.setValue("recswiftcode",null);this.mainDataSet.setValue("swiftname",null);this.mainDataSet.setValue("recbankcode",null);this.mainDataSet.setValue("bankncode",null);var v_ccode=this.mainDataSet.getValue("ccode"),v_fcode=this.mainDataSet.getValue("fcode"),v_bcode=this.mainDataSet.getValue("bcode"),v_wcode=this.mainDataSet.getValue("wcode");if(v_ccode&&v_fcode){var tc=this.mainTable.getColumn("bankaccount"),dialog=tc.getAidInputer();dialog.getDOM();var table=dialog.getTable();table.queryParam.setItemValue("ccode",v_ccode);table.queryParam.setItemValue("fcode",v_fcode);table.queryParam.setItemValue("wcode",v_wcode);table.queryParam.setItemValue("bcode",v_bcode);table.refreshTable();var cbankDataSet=table.dataSet,rowDefault=-1;if(cbankDataSet.getRowCount()==1)rowDefault=0;else {for(var i=0;i<cbankDataSet.getRowCount();i++){var ispriority=cbankDataSet.getValue("ispriority",i);if(ispriority=="Y"){if(rowDefault==-1)rowDefault=i;else {rowDefault=-1;break;}}}}if(rowDefault!=-1){this.mainDataSet.setValue("baicode",cbankDataSet.getValue("ccbaicode",rowDefault));this.mainDataSet.setValue("bankaccount",cbankDataSet.getValue("bankaccount",rowDefault));var cnapsname=cbankDataSet.getValue("cnapsname",rowDefault),swiftname=cbankDataSet.getValue("swiftname",rowDefault);this.mainDataSet.setValue("bankname",cnapsname?cnapsname:swiftname);this.mainDataSet.setValue("bankaccountname",cbankDataSet.getValue("bankaccountname",rowDefault));this.mainDataSet.setValue("swiftname",swiftname);}}},controlDocRate:function(){var bcodeFlag=this.payDocDataSet.getValue("bcode")==this.mainDataSet.getValue("bcode"),statusFlag=this.mainDataSet.getValue("status")<26||this.mainDataSet.getValue("status")==60;this.payDocTable.getColumn("docrate").nonBlankOnSubmit=!bcodeFlag;this.payDocTable.getColumn("docrate").readOnly=bcodeFlag||!statusFlag;this.payDocTable.getColumn("docrate").ignoreTblRdonly=!this.payDocTable.getColumn("docrate").readOnly;if(bcodeFlag){this.payDocDataSet.setValue("docrate",null);this.mainDataSet.saveChanges();}},controlPayDoc:function(event){if(String.isStrIn("ccode,fcode,paymode,ishasinv,bankaccount",event.columnName,",")){var fcode=this.mainDataSet.getValue("fcode"),ishasinv=this.mainDataSet.getValue("ishasinv"),ishasinvOld=event.columnName=="ishasinv"?event.value:ishasinv,paymode=this.mainDataSet.getValue("paymode");if(this.payDocDataSet.rows.length>0){if("Y"==ishasinvOld){if(String.isStrIn("ccode,paymode,bankaccount",event.columnName,",")){this.payDocDataSet.deleteAllRows();return;}if("fcode"==event.columnName&&!("CNY"==fcode)){this.payDocDataSet.deleteAllRows();return;}if("ishasinv"==event.columnName&&!("Y"==ishasinv)){this.payDocDataSet.deleteAllRows();return;}}if(null==ishasinvOld){if("fcode"==event.columnName&&!("CNY"==fcode)){this.payDocDataSet.deleteAllRows();return;}if("paymode"==event.columnName){this.payDocDataSet.deleteAllRows();return;}}if("N"==ishasinv&&!String.isStrIn("0080,0085,0090",paymode,","))this.payDocDataSet.deleteAllRows();}}},controlSrc:function(event){if(String.isStrIn("fcode,sfcode",event.columnName)){var fcode=this.mainDataSet.getValue("fcode"),sfcode=this.mainDataSet.getValue("sfcode");if(fcode==sfcode)this.srcDataSet.deleteAllRows();}},dataSetSaved:function(dataSet,e){snsoft.ft.pay.app.lis.PayAppColumnControlJSListener.superclass.dataSetSaved.call(this,dataSet,e);var tableRowValuesArray=snsoft.ft.utils.FTUtils.buildTableRowValues(e.saveInfo);if(tableRowValuesArray&&tableRowValuesArray.length>0)snsoft.ft.utils.FTUtils.replaceValueAndAutoRefresh(dataSet.getTables()[0],tableRowValuesArray.filter((tr)=>{return tr.replaceValues.some((r)=>{return "$op$" in r;});}));}});snsoft.ft.pay.app.lis.PayAppGFcyRateCalcListener=function(params){snsoft.ft.pay.app.lis.PayAppGFcyRateCalcListener.superclass.constructor.call(this,params);this.fcyColumn=String.obj2str(params.fcyColumn,"fcy");this.payfcyColumn=String.obj2str(params.payfcyColumn,"payfcy");this.fzerateColumn=String.obj2str(params.fzerateColumn,"fzerate");};Xjs.extend(snsoft.ft.pay.app.lis.PayAppGFcyRateCalcListener,snsoft.plat.bas.busi.SystemFunctionListener,{initComponent:function(table,values){snsoft.ft.pay.app.lis.PayAppGFcyRateCalcListener.superclass.initComponent.call(this,table,values);this.mainTable=this.getTable(table,"ft_pay_app");this.mainDataSet=this.mainTable.getDataSet();this.gTable=this.getTable(table,"ft_pay_appg");this.gDataSet=this.gTable.getDataSet();},dataSetFieldPosted:function(dataSet,event){var call,g_flag=dataSet.name=="ft_pay_appg",n_col=event.columnName;if(g_flag&&(this.payfcyColumn==n_col||this.fcyColumn==n_col)){call=new Xjs.FuncCall(this.setFzerateValue,this,[dataSet]);snsoft.plat.bas.busi.SystemFunctionListener.lockInvoke(call);}var m_fcode=this.mainDataSet.getValue("fcode"),g_fcode=this.gDataSet.getValue("fcode"),eq_flag=m_fcode==g_fcode;if(g_flag&&(n_col==this.fcyColumn||n_col=="fcode")){if(eq_flag){var v_fcy=this.gDataSet.getValue(this.fcyColumn);this.gDataSet.setValue(this.payfcyColumn,v_fcy);}}if(!g_flag&&n_col=="fcode"){for(var i=0;i<this.gDataSet.getRowCount();i++){var v_fcy=this.gDataSet.getValue(this.fcyColumn,i);this.gDataSet.gotoRow(i);this.gDataSet.setValue(this.payfcyColumn,v_fcy);}}if(n_col=="fcode"){this.gTable.getColumn("payfcy").readOnly=eq_flag;this.gTable.updateReadonlyStatus();this.gTable.render(2);}if(n_col=="rdate"){var financialTable=Xjs.util.TableUtils.getTable_$Panel_$String(this.mainTable,"ft_fund_financial");if(financialTable)financialTable.refreshTable();}},dataSetRowNavigated:function(dataSet,e){if(dataSet.name=="ft_pay_appg"){var m_fcode=this.mainDataSet.getValue("fcode"),g_fcode=this.gDataSet.getValue("fcode");this.gTable.getColumn("payfcy").readOnly=m_fcode==g_fcode;this.gTable.updateReadonlyStatus();}},setFzerateValue:function(dataSet){var fcy=dataSet.getValue(this.fcyColumn),payfcy=dataSet.getValue(this.payfcyColumn);if(fcy&&payfcy&&fcy!=0){var table=dataSet.getListener(Xjs.table.Table),scale=table.getColumn(this.fzerateColumn).maxDecimals;dataSet.setValue(this.fzerateColumn,this.round(payfcy/fcy,scale));}}});Xjs.RInvoke.beansDef["FT-PAY.FinPayUIService"]={generateFinPayDetailShare:{},getFinPayDetailFrates:{}};Xjs.RInvoke.beansDef["FT-PAY.PayAppConfWorkBenchUIService"]={confed:{}};Xjs.RInvoke.beansDef["FT-PAY.PayAppUIService"]={mergePay:{},pushFund:{},checkOrCondition:{},queryBankncodeBySwift:{}};Xjs.namespace("snsoft.ft.pay.imptax.lis");snsoft.ft.pay.imptax.lis.ImpTaxAppColumnControlJSListener=function(params){snsoft.ft.pay.imptax.lis.ImpTaxAppColumnControlJSListener.superclass.constructor.call(this,params);};Xjs.extend(snsoft.ft.pay.imptax.lis.ImpTaxAppColumnControlJSListener,snsoft.plat.bas.busi.SystemFunctionListener,{initComponent:function(table,values){snsoft.ft.pay.imptax.lis.ImpTaxAppColumnControlJSListener.superclass.initComponent.call(this,table,values);this.mainTable=this.getTable(table,"ft_pay_app");this.mainDataSet=this.mainTable.getDataSet();this.basTable=this.getTable(table,"ft_pay_app_bas");this.basDataSet=this.basTable.getDataSet();this.gTable=this.getTable(table,"ft_pay_appg");this.gDataSet=this.gTable.getDataSet();},dataLoaded:function(dataSet,e){snsoft.ft.pay.imptax.lis.ImpTaxAppColumnControlJSListener.superclass.dataLoaded.call(this,dataSet,e);},dataSetFieldPosted:function(dataSet,event){snsoft.ft.pay.imptax.lis.ImpTaxAppColumnControlJSListener.superclass.dataSetFieldPosted.call(this,dataSet,event);if("ccode"==event.columnName)this.gDataSet.deleteAllRows();if("ccode"==event.columnName||"fcode"==event.columnName)this.bankAccountValue(event);if(event.columnName=="paymode")this.isneedpfundCtrl(event);if(event.columnName=="paytaxmode")this.isPaidCtrl(event);},isPaidCtrl:function(event){var paytaxmode=this.mainDataSet.getValue("paytaxmode");if(String.isStrIn("10,20,40",paytaxmode,","))this.basDataSet.setValue("ispaid","Y");else this.basDataSet.setValue("ispaid","N");},isneedpfundCtrl:function(event){var paymode=this.mainDataSet.getValue("paymode");if(String.isStrIn("0105,0110,0115,0025,0030,0060,0065,0070,0075",paymode,","))this.basDataSet.setValue("isneedpfund","N");else this.basDataSet.setValue("isneedpfund","Y");},bankAccountValue:function(event){this.mainDataSet.setValue("bankaccount",null);this.mainDataSet.setValue("baicode",null);this.mainDataSet.setValue("bankname",null);this.mainDataSet.setValue("bankaccountname",null);var v_ccode=this.mainDataSet.getValue("ccode"),v_fcode=this.mainDataSet.getValue("fcode"),v_bcode=this.mainDataSet.getValue("bcode"),v_wcode=this.mainDataSet.getValue("wcode");if(v_ccode&&v_fcode){var tc=this.mainTable.getColumn("bankaccount"),dialog=tc.getAidInputer();dialog.getDOM();var table=dialog.getTable();table.queryParam.setItemValue("ccode",v_ccode);table.queryParam.setItemValue("fcode",v_fcode);table.queryParam.setItemValue("wcode",v_wcode);table.queryParam.setItemValue("bcode",v_bcode);table.refreshTable();var cbankDataSet=table.dataSet,rowDefault=-1;if(cbankDataSet.getRowCount()==1)rowDefault=0;else {for(var i=0;i<cbankDataSet.getRowCount();i++){var ispriority=cbankDataSet.getValue("ispriority",i);if(ispriority=="Y"){if(rowDefault==-1)rowDefault=i;else {rowDefault=-1;break;}}}}if(rowDefault!=-1){this.mainDataSet.setValue("baicode",cbankDataSet.getValue("ccbaicode",rowDefault));this.mainDataSet.setValue("bankaccount",cbankDataSet.getValue("bankaccount",rowDefault));var cnapsname=cbankDataSet.getValue("cnapsname",rowDefault),swiftname=cbankDataSet.getValue("swiftname",rowDefault);this.mainDataSet.setValue("bankname",cnapsname?cnapsname:swiftname);this.mainDataSet.setValue("bankaccountname",cbankDataSet.getValue("bankaccountname",rowDefault));}}}});