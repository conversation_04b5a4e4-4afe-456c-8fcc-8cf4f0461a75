Xjs.loadedXjs.push("snsoft/ft/fund/loan/dlydfin");
Xjs.namespace("snsoft.ft.loan.dlydfin.app.invoker");snsoft.ft.loan.dlydfin.app.invoker.CreateDocuDeliveryFinaRedVoucherInvoker=function(parameter){snsoft.ft.loan.dlydfin.app.invoker.CreateDocuDeliveryFinaRedVoucherInvoker.superclass.constructor.call(this,parameter);this.redType=parameter.redType;};Xjs.extend(snsoft.ft.loan.dlydfin.app.invoker.CreateDocuDeliveryFinaRedVoucherInvoker,snsoft.ext.cmd.CommandInvoker,{invoke:function(event){var rows=this.mainTable.getSelectedRowNumbers();if(rows==null||rows.length==0){throw Xjs.ResBundle.getResVal("FT.00000048");}for(var i=0;i<rows.length;i++){var redflag=this.dataSet.getValue("redflag",rows[i]);if(redflag!=0){throw Xjs.ResBundle.getResVal("SNA-ACC.00000087");}var vidflag=this.dataSet.getValue("vidflag",rows[i]);if(vidflag!=1){throw Xjs.ResBundle.getResVal("SNA-ACC.00000096");}}var vmarkicodes=Xjs.util.DataSetUtils.getColumnStrValues_DataSet_int(this.mainDataSet,"vmarkicode",rows),param={tcaptime:this.mainDataSet.get("tcaptime"),vmarkicodes:vmarkicodes,redType:this.redType},dlydfaicodes=Xjs.util.DataSetUtils.getColumnStrValues_DataSet_int(this.mainDataSet,"dlydfaicode",rows);param.dlydfaicodes=dlydfaicodes;if("redLiVoucher"==this.redType){var dlydfaliicodes=Xjs.util.DataSetUtils.getColumnStrValues_DataSet_int(this.mainDataSet,"dlydfaliicode",rows);param.dlydfaliicodes=dlydfaliicodes;}else {var dlydfariicodes=Xjs.util.DataSetUtils.getColumnStrValues_DataSet_int(this.mainDataSet,"dlydfariicode",rows);param.dlydfariicodes=dlydfariicodes;}return param;},afterInvoke:function(event){this.mainTable.refreshTable();}});snsoft.ft.loan.dlydfin.app.invoker.CreateVoucherCheckInvoker=function(parameter){snsoft.ft.loan.dlydfin.app.invoker.CreateVoucherCheckInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.loan.dlydfin.app.invoker.CreateVoucherCheckInvoker,snsoft.ext.cmd.CommandInvoker,{beforeCheck:function(event){return {dlydfaicode:this.mainDataSet.get("dlydfaicode")};},afterInvoke:function(event){this.mainTable.refreshTable();}});snsoft.ft.loan.dlydfin.app.invoker.DocuDeliveryFinaAppSendBackInvoker=function(parameter){snsoft.ft.loan.dlydfin.app.invoker.DocuDeliveryFinaAppSendBackInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.loan.dlydfin.app.invoker.DocuDeliveryFinaAppSendBackInvoker,snsoft.ext.cmd.CommandInvoker,{beforeCheck:function(event){var faliTable=Xjs.util.TableUtils.getTable_$Panel_$String(this.mainTable,"ft_loan_dlydfali"),faliTableDataSet=faliTable.getDataSet();faliTableDataSet.ensureOpened();var size=faliTableDataSet.getRows().length;if(size!=0){throw new Error(Xjs.ResBundle.getResVal("FT-LOAN.00000016"));}return null;},invoke:function(event){return {dlydfaicode:this.mainDataSet.get("dlydfaicode")};},afterInvoke:function(event){this.mainTable.refreshTable();}});Xjs.namespace("snsoft.ft.loan.dlydfin.app.lis");snsoft.ft.loan.dlydfin.app.lis.DocuDeliveryFinaAppDetailJSListener=function(params){snsoft.ft.loan.dlydfin.app.lis.DocuDeliveryFinaAppDetailJSListener.superclass.constructor.call(this,params);};Xjs.extend(snsoft.ft.loan.dlydfin.app.lis.DocuDeliveryFinaAppDetailJSListener,snsoft.plat.bas.busi.SystemFunctionListener,{initComponent:function(table,values){snsoft.ft.loan.dlydfin.app.lis.DocuDeliveryFinaAppDetailJSListener.superclass.initComponent.call(this,table,values);this.mainTable=table;if(!this.basTable)this.basTable=this.getTable(table,"ft_loan_dlydfa_bas");},dataSetFieldPosting:function(dataSet,event){snsoft.ft.loan.dlydfin.app.lis.DocuDeliveryFinaAppDetailJSListener.superclass.dataSetFieldPosting.call(this,dataSet,event);var dlydfcy=dataSet.getValue("dlydfcy");if(event.columnName=="fcy"){var fcy=event.value;if(fcy>dlydfcy){throw new Error(Xjs.ResBundle.getResVal("FT-LOAN.00000017"));}this.mainTable.getDataSet().setValue("isfullfin",fcy==dlydfcy?"Y":"N");}},dataSetFieldPosted:function(dataSet,event){snsoft.ft.loan.dlydfin.app.lis.DocuDeliveryFinaAppDetailJSListener.superclass.dataSetFieldPosted.call(this,dataSet,event);}});