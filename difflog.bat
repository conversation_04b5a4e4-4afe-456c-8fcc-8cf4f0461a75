@echo off
setlocal

:: 设置作者名称
set GIT_AUTHOR_NAME="池启苹"

echo.
echo =======================================================================
echo.
echo  正在查找提交人 [%GIT_AUTHOR_NAME%] 在分支间的代码差异...
echo.
echo =======================================================================

:: 第一步：确保你的本地分支信息是和远程最新的
echo.
echo --- 正在从远程仓库获取最新信息 (git fetch)...
:: git fetch origin
echo.

:: 第二步：查找在 origin/master 中存在，但在 origin/phase2 中不存在的提交
echo.
echo --- 1. 存在于 origin/master 但不存在于 origin/phase2 的提交:
echo -------------------------------------------------------------------
git log origin/phase2..origin/master --author="池启苹" --pretty=format:"%%h - %%an, %%ar : %%s"

echo.
echo.

:: 第三步：查找在 origin/phase2 中存在，但在 origin/master 中不存在的提交
echo.
:: echo --- 2. 存在于 origin/phase2 但不存在于 origin/master 的提交:
:: echo -------------------------------------------------------------------
:: git log origin/master..origin/phase2 --author=%GIT_AUTHOR_NAME% --pretty=format:"%%h - %%an, %%ar : %%s"

echo.
echo.
echo =======================================================================
echo.
echo  查询完成。
echo.
echo =======================================================================

pause
endlocal