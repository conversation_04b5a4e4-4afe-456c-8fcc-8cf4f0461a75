Xjs.loadedXjs.push("snsoft/ft/fund/loan/absfabr");
Xjs.namespace("snsoft.ft.loan.absfabr.lis");snsoft.ft.loan.absfabr.lis.ABSFinaBatchRegPopJSListener=function(params){snsoft.ft.loan.absfabr.lis.ABSFinaBatchRegPopJSListener.superclass.constructor.call(this,params);};Xjs.extend(snsoft.ft.loan.absfabr.lis.ABSFinaBatchRegPopJSListener,snsoft.plat.bas.busi.SystemFunctionListener,{itemAidInputing:function(table,e){snsoft.ft.loan.absfabr.lis.ABSFinaBatchRegPopJSListener.superclass.itemAidInputing.call(this,table,e);if(!("faprod"==e.item.name))return;var tc=table.getColumn("fadicode");table.startAidInput(tc);var err=new Error();err.dummy=true;throw err;}});