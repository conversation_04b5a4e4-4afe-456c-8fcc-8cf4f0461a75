Xjs.loadedXjs.push("snsoft/ft/fund/rdoc/sign");
Xjs.namespace("snsoft.ft.rdoc.sign.invoker");snsoft.ft.rdoc.sign.invoker.RdocSignBusiOpentgbJSInvoker=function(parameter){snsoft.ft.rdoc.sign.invoker.RdocSignBusiOpentgbJSInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.rdoc.sign.invoker.RdocSignBusiOpentgbJSInvoker,snsoft.ext.cmd.CommandInvoker,{beforeCheck:function(event){var rows=this.table.getSelectedRowNumbers();if(!rows||rows.length==0){throw new Error(Xjs.ResBundle.getResVal("FT.00000048"));}var tgbicodes=[];for(var i=0;i<rows.length;i++){var tgbicode=this.dataSet.getValue("tgbicode",rows[i]);if(tgbicode)tgbicodes.push(tgbicode);}if(!tgbicodes||tgbicodes.length==0){throw new Error(Xjs.ResBundle.getResVal("FT-RDOC.00000063"));}return null;},invoke:function(event){var rows=this.table.getSelectedRowNumbers(),tgbicodes=[];for(var i=0;i<rows.length;i++){var tgbicode=this.dataSet.getValue("tgbicode",rows[i]);if(tgbicode)tgbicodes.push(tgbicode);}var tgbicode=tgbicodes.join(","),sheetService=Xjs.RInvoke.newBean("SN-Busi.SheetService"),busiObject=sheetService.getBusiObject("FT-APPR.Tgb"),data={sheetcode:busiObject.sheetcode};data[busiObject.innerfld]=tgbicode;data.sheetEntry=true;snsoft.plat.bas.viewdetail.ViewDetailListener.invokeViewDetail(data,busiObject.outerfld);return null;}});snsoft.ft.rdoc.sign.invoker.RdocSignCreateTgbSubmitJSInvoker=function(parameter){snsoft.ft.rdoc.sign.invoker.RdocSignCreateTgbSubmitJSInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.rdoc.sign.invoker.RdocSignCreateTgbSubmitJSInvoker,snsoft.ext.cmd.CommandInvoker,{beforeCheck:function(event){var rows=this.table.getSelectedRowNumbers();if(!rows||rows.length==0){throw new Error(Xjs.ResBundle.getResVal("FT.00000048"));}var invokeParam=this.buildInvokeParam(null);invokeParam.invokeMethod="beforeCheck";return invokeParam;},invoke:function(event){var invokeParam=this.buildInvokeParam(null);invokeParam.invokeMethod="invoke";return invokeParam;},afterInvoke:function(event){this.mainTable.refreshTable(true,0,null);var invokeRtnVal=event.invokeRtnVal;if(invokeRtnVal){var tgbicode=invokeRtnVal.tgbicode,sheetService=Xjs.RInvoke.newBean("SN-Busi.SheetService"),busiObject=sheetService.getBusiObject("FT-APPR.Tgb"),data={sheetcode:busiObject.sheetcode};data[busiObject.innerfld]=tgbicode;data.sheetEntry=true;snsoft.plat.bas.viewdetail.ViewDetailListener.invokeViewDetail(data,busiObject.outerfld);}}});snsoft.ft.rdoc.sign.invoker.RdocSignSubmitBeforeJSInvoker=function(parameter){snsoft.ft.rdoc.sign.invoker.RdocSignSubmitBeforeJSInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.rdoc.sign.invoker.RdocSignSubmitBeforeJSInvoker,snsoft.ext.cmd.CommandInvoker,{beforeInvoke:function(event){var param={},mains=[],main={};main.rdocsignicode=this.dataSet.getValue("rdocsignicode");main.sheetcode=this.dataSet.getValue("sheetcode");main.modifydate=this.dataSet.getValue("modifydate");mains.push(main);param.mains=mains;param.rdocsignicode=this.dataSet.getValue("rdocsignicode");return param;},invoke:function(event){var invokeRtnVal=event.beforeInvokeRtnVal;if(invokeRtnVal){this.mainTable.setResetKeyFilterOnRefresh(2);this.mainTable.refreshTable(true);var innercode=invokeRtnVal.rdocsignicode,innerfld=this.mainDataSet.getCuInnerfld();for(var i=0;i<this.mainDataSet.getRowCount();i++){if(this.mainDataSet.getValue(innerfld,i)==innercode)this.mainDataSet.gotoRow(i);}var showErr=invokeRtnVal.showErr;if(showErr){var err={message:showErr};Xjs.alertErr(err);throw err;}}return null;}});snsoft.ft.rdoc.sign.invoker.RdocSignSubmitsJSInvoker=function(parameter){snsoft.ft.rdoc.sign.invoker.RdocSignSubmitsJSInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.rdoc.sign.invoker.RdocSignSubmitsJSInvoker,snsoft.ext.cmd.CommandInvoker,{beforeCheck:function(event){var rows=this.table.getSelectedRowNumbers();if(!rows||rows.length==0){throw new Error(Xjs.ResBundle.getResVal("FT.00000048"));}var invokeParam=this.buildInvokeParam(null);invokeParam.invokeMethod="beforeCheck";return invokeParam;},check:function(event){var rdocbacktypeInfo=event.beforeCheckRtnVal.rdocbacktypeInfo;if(rdocbacktypeInfo){var btnTitle=snsoft.ft.utils.FTUtils.getBtnTitle(event.table,event.cfg.cmd);return {type:0,title:btnTitle,prompt:rdocbacktypeInfo};}return null;},invoke:function(event){var invokeParam=this.buildInvokeParam(null);invokeParam.invokeMethod="invoke";return invokeParam;},afterInvoke:function(event){this.mainTable.refreshTable(true,0,null);var invokeRtnVal=event.invokeRtnVal;if(invokeRtnVal){var showErr=invokeRtnVal.showErr;if(showErr){var err={message:showErr};Xjs.alertErr(err);throw err;}}}});snsoft.ft.rdoc.sign.invoker.RecDocSigngEditJSInvoker=function(parameter){snsoft.ft.rdoc.sign.invoker.RecDocSigngEditJSInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.rdoc.sign.invoker.RecDocSigngEditJSInvoker,snsoft.ext.cmd.CommandInvoker,{beforeCheck:function(event){var rows=this.table.getSelectedRowNumbers(),param={},gicodes=[];if(Xjs.ResBundle.getResVal("FT.cmd_delete")==event.cfg.title&&rows.length>0){for(var i=0;i<rows.length;i++){var gicode={};gicode.rdocsigngicode=this.dataSet.getValue("rdocsigngicode",rows[i]);gicodes.push(gicode);}}else {var gicode={};gicode.rdocsigngicode=this.dataSet.getValue("rdocsigngicode");gicodes.push(gicode);}param.gicodes=gicodes;var mains=[],main={};main.rdocsignicode=this.mainDataSet.getValue("rdocsignicode");main.sheetcode=this.mainDataSet.getValue("sheetcode");main.modifydate=this.mainDataSet.getValue("modifydate");mains.push(main);param.mains=mains;return param;}});snsoft.ft.rdoc.sign.invoker.RecDocSigngRedJSInvoker=function(parameter){snsoft.ft.rdoc.sign.invoker.RecDocSigngRedJSInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.rdoc.sign.invoker.RecDocSigngRedJSInvoker,snsoft.ext.cmd.CommandInvoker,{beforeCheck:function(event){var rows=this.table.getSelectedRowNumbers();if(!rows||rows.length==0){throw new Error(Xjs.ResBundle.getResVal("FT.00000048"));}for(var i=0;i<rows.length;i++){var redflag=this.dataSet.getValue("redflag",rows[i]);if(redflag!=0){throw new Error(Xjs.ResBundle.getResVal("FT-RDOC.00000006"));}var claimstatus=this.dataSet.getValue("claimstatus",rows[i]);if("Y"==claimstatus){throw new Error(Xjs.ResBundle.getResVal("FT-RDOC.00000007"));}}var param={},gicodes=[];for(var i=0;i<rows.length;i++){var gicode={};gicode.rdocsigngicode=this.dataSet.getValue("rdocsigngicode",rows[i]);gicodes.push(gicode);}param.method="red";param.gicodes=gicodes;return param;},check:function(event){var rows=this.table.getSelectedRowNumbers(),param={},gicodes=[];for(var i=0;i<rows.length;i++){var gicode={};gicode.rdocsigngicode=this.dataSet.getValue("rdocsigngicode",rows[i]);gicodes.push(gicode);}param.gicodes=gicodes;var mains=[],main={};main.rdocsignicode=this.mainDataSet.getValue("rdocsignicode");main.sheetcode=this.mainDataSet.getValue("sheetcode");main.modifydate=this.mainDataSet.getValue("modifydate");mains.push(main);param.mains=mains;event.checkData.param=param;return null;},invoke:function(event){return event.checkData.param;},afterInvoke:function(event){this.table.refreshTable();var refreshRows=new Xjs.util.DataSetUtils$RefreshRows(this.mainDataSet,this.mainTable.getSelectedRowNumbers(),null,null);Xjs.util.DataSetUtils.refreshRow_prefix(refreshRows,null,true);}});snsoft.ft.rdoc.sign.invoker.RecDocSignJSInvoker=function(parameter){snsoft.ft.rdoc.sign.invoker.RecDocSignJSInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.rdoc.sign.invoker.RecDocSignJSInvoker,snsoft.ext.cmd.CommandInvoker,{beforeCheck:function(event){var rows=this.table.getSelectedRowNumbers();if(!rows||rows.length==0){throw new Error(Xjs.ResBundle.getResVal("FT.00000048"));}for(var i=0;i<rows.length;i++){var status=this.dataSet.getValue("status",rows[i]);if(!(status<20)){throw new Error(Xjs.ResBundle.getResVal("FT-RDOC.00000012"));}var signstatus=this.dataSet.getValue("signstatus",rows[i]);if(signstatus=="30"){throw new Error(Xjs.ResBundle.getResVal("FT-RDOC.00000047"));}}var errArgs=snsoft.ft.utils.FTUtils.initErrCheckArgs(this.table,["corpbcode"]);errArgs.selrow=rows;errArgs.title=this.getTitle();errArgs.showExtCols=["docno"];snsoft.ft.utils.FTUtils.checkfixmodeGridErrRows(errArgs);return null;},check:function(event){var rows=this.table.getSelectedRowNumbers();return this.newDialogWithFunc(event,(e)=>{var dialogPane=Xjs.ui.UIUtil.loadDialog("FT-RDOC.RdocSignDialog");dialogPane.title=this.cfg.title;dialogPane.showModal();dialogPane.getItemByName("number").setValue(rows.length);var docfcy=0,sumfcying=0;for(var i=0;i<rows.length;i++){docfcy+=this.dataSet.getValue("fcy",rows[i]);sumfcying+=this.dataSet.getValue("fcying",rows[i]);}dialogPane.getItemByName("docfcy").setValue(docfcy);dialogPane.getItemByName("sumfcying").setValue(sumfcying);dialogPane.getItemByName("corpbcode").setValue(this.dataSet.getValue("corpbcode"));dialogPane.getItemByName("isallsign").setValue("1");return dialogPane;},(e,d)=>{var dlgTable=d.getItemByName("ft_rdoc_sign");dlgTable.postPending();if(!dlgTable.dataSet.isChanged(false))dlgTable.dataSet.setRowChanged(true);dlgTable.checkNonBlankForSubmit();var param={},mains=[];for(var i=0;i<rows.length;i++){var main={};main.rdocsignicode=this.dataSet.getValue("rdocsignicode",rows[i]);main.sheetcode=this.dataSet.getValue("sheetcode",rows[i]);main.modifydate=this.dataSet.getValue("modifydate",rows[i]);mains.push(main);}param.mains=mains;param.signstatus=dlgTable.getDataSet().getValue("signstatus");param.isallsign=dlgTable.getDataSet().getValue("isallsign");param.fcy=dlgTable.getDataSet().getValue("fcy");param.bcode=dlgTable.getDataSet().getValue("bcode");param.wcode=dlgTable.getDataSet().getValue("wcode");param.remark=dlgTable.getDataSet().getValue("remark");event.checkData.param=param;this.remoteInvoke("beforeInvoke",param);});},invoke:function(event){return event.checkData.param;},afterInvoke:function(event){var refreshRows=new Xjs.util.DataSetUtils$RefreshRows(this.dataSet,this.table.getSelectedRowNumbers(),null,null);Xjs.util.DataSetUtils.refreshRow_prefix(refreshRows,null,true);}});Xjs.namespace("snsoft.ft.rdoc.sign.lis");snsoft.ft.rdoc.sign.lis.RdocSignDialogCtrlJSListener=function(params){snsoft.ft.rdoc.sign.lis.RdocSignDialogCtrlJSListener.superclass.constructor.call(this,params);};Xjs.extend(snsoft.ft.rdoc.sign.lis.RdocSignDialogCtrlJSListener,snsoft.plat.bas.busi.SystemFunctionListener,{dataSetFieldPosted:function(dataSet,event){snsoft.ft.rdoc.sign.lis.RdocSignDialogCtrlJSListener.superclass.dataSetFieldPosted.call(this,dataSet,event);var isallsign=dataSet.getValue("isallsign");if(event.columnName=="isallsign"&&"1"==isallsign){dataSet.setValue("fcy",null);dataSet.setValue("sumfcy",dataSet.getValue("sumfcying"));}if(event.columnName=="isallsign"&&!isallsign||event.columnName=="fcy"){var number=dataSet.getValue("number"),fcy=dataSet.getValue("fcy");if(fcy!=0)dataSet.setValue("sumfcy",number*fcy);else dataSet.setValue("sumfcy",null);}}});snsoft.ft.rdoc.sign.lis.RdocSignOpenRecClaimJSListener=function(parameter){snsoft.ft.rdoc.sign.lis.RdocSignOpenRecClaimJSListener.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.rdoc.sign.lis.RdocSignOpenRecClaimJSListener,snsoft.plat.bas.sheet.cmd.CommandSheet,{invoke:function(event){var rows=this.table.getSelectedRowNumbers(0);if(!rows||rows.length==0){throw new Error(Xjs.ResBundle.getResVal("FT-RDOC.00000023"));}for(var row of rows){if("0"!=this.dataSet.getValue("redflag",row)){throw new Error(Xjs.ResBundle.getResVal("FT-RDOC.00000024"));}}var rdocicodes=null,rdoccodes=null;for(var i=0;i<rows.length;i++){if(i==0){rdocicodes="";rdoccodes="";}else {rdocicodes+=",";rdoccodes+=",";}rdocicodes+=this.table.getValue("rdocicode",rows[i]);rdoccodes+=this.table.getValue("rdoccode",rows[i]);}if(rdocicodes&&rdoccodes){var data={};data.rdocicode=rdocicodes;data.rdoccode=rdoccodes;snsoft.plat.bas.viewdetail.ViewDetailListener.invokeViewDetail(data,"rdoccode");}return null;}});snsoft.ft.rdoc.sign.lis.RdocSignOpenRecClaimSignJSListener=function(parameter){snsoft.ft.rdoc.sign.lis.RdocSignOpenRecClaimSignJSListener.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.rdoc.sign.lis.RdocSignOpenRecClaimSignJSListener,snsoft.plat.bas.sheet.cmd.CommandSheet,{invoke:function(event){var rows=this.table.getSelectedRowNumbers(0);if(!rows||rows.length==0){throw new Error(Xjs.ResBundle.getResVal("FT.00000048"));}for(var row of rows){if(!("70"==this.dataSet.getValue("status",row)&&"10"==this.dataSet.getValue("signstatus",row))){throw new Error(Xjs.ResBundle.getResVal("FT-RDOC.00000035"));}}var pm={},docno=null;for(var i=0;i<rows.length;i++){if(i==0)docno="";else docno+=",";docno+=this.table.getValue("docno",rows[i]);}pm["InitValue.docno"]=docno;pm.AutoRefresh=1;Xjs.ui.UIUtil.wopenUI(null,"FT-RDOC.RdocClaimWorkBench",pm);return null;}});