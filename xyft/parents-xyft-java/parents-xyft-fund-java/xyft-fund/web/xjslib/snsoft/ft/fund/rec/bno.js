Xjs.loadedXjs.push("snsoft/ft/fund/rec/bno");
Xjs.namespace("snsoft.ft.rec.bno.invoker");snsoft.ft.rec.bno.invoker.RecBanknoCancelLeLogInvoker=function(parameter){snsoft.ft.rec.bno.invoker.RecBanknoCancelLeLogInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.rec.bno.invoker.RecBanknoCancelLeLogInvoker,snsoft.ft.rec.comm.invoker.GenRecClaimInvoker,{onDlgShow:function(e){var bnoicodes=this.getSelRowInnercodes(e.table),p={bnoicode:"@"+bnoicodes};if(!this.refreshParams)this.refreshParams={};Xjs.apply(this.refreshParams,p);return snsoft.ft.rec.bno.invoker.RecBanknoCancelLeLogInvoker.superclass.onDlgShow.call(this,e);},doCheckBeforeOk:function(e,dlg){var dlgtbl=snsoft.ft.utils.FTUtils.getTableFromPanel(dlg);this.checkSelRow(dlgtbl);return snsoft.ft.rec.bno.invoker.RecBanknoCancelLeLogInvoker.superclass.doCheckBeforeOk.call(this,e,dlg);},buildGenRclmParams:function(genRclmParams,e,dlg){var dlgtbl=snsoft.ft.utils.FTUtils.getTableFromPanel(dlg);genRclmParams.reficodes=this.getSelRowInnercodes(dlgtbl);genRclmParams.extParams={leDatas:this.getSelRowValues(dlgtbl)};}});snsoft.ft.rec.bno.invoker.RecBanknoDyldRclmInvoker=function(parameter){snsoft.ft.rec.bno.invoker.RecBanknoDyldRclmInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.rec.bno.invoker.RecBanknoDyldRclmInvoker,snsoft.ft.rec.comm.invoker.GenRecClaimInvoker,{beforeCheck:function(event){this.checkSelRow(event.table);this.checkSelectOne(Xjs.ResBundle.getResVal("ft_docu_dlyd"));return null;},doCheckBeforeOk:function(e,dlg){var dlgTable=snsoft.ft.utils.FTUtils.getTableFromPanel(dlg);this.postRow(dlgTable);this.verifySelectErr(dlgTable);var selectRows=dlgTable.getSelectedRowNumbers(dlgTable.rowMutiSelectable?0:1),allSelected=snsoft.ft.utils.FTUtils.getAllDataSetRow(dlgTable,selectRows,dlgTable.getDataSet().columnAt(this.matchCol));this.checkSelectSameCol(dlgTable,allSelected,"srccode");this.checkRefMatchCol(e.table,dlgTable,"curfcy",Xjs.ResBundle.getResVal("ft_rec_rclm.rclmfcy"),true);this.checkRclmFcyingEqCurfcy(e,allSelected,dlgTable);return snsoft.ft.rec.bno.invoker.RecBanknoDyldRclmInvoker.superclass.doCheckBeforeOk.call(this,e,dlg);},buildGenRclmParams:function(genRclmParams,e,dlg){genRclmParams.matchCol=this.matchCol;var dlgTable=snsoft.ft.utils.FTUtils.getTableFromPanel(dlg),selectRows=dlgTable.getSelectedRowNumbers(dlgTable.rowMutiSelectable?0:1),allSelected=snsoft.ft.utils.FTUtils.getAllDataSetRow(dlgTable,selectRows,dlgTable.getDataSet().columnAt(this.matchCol)),srcvals=snsoft.ft.utils.FTUtils.getSrcMatchCodes(dlgTable,allSelected,this.matchCol,this.extSrcValueCols);genRclmParams.matchCodesMap=srcvals;},checkSelectSameCol:function(dlgTable,allSelected,col){var codes=[];allSelected.forEach((dataSetRow)=>{var v=dataSetRow[dlgTable.getDataSet().columnAt(col)];if(codes.indexOf(v)<0)codes.push(v);});if(codes.length>1){throw new Error(Xjs.ResBundle.getResVal("FT-REC.********",Xjs.ResBundle.getResVal("ft_docu_dlyd")));}},checkRclmFcyingEqCurfcy:function(e,allSelected,dlgTable){var dataSetRow=allSelected[0],curfcy=dataSetRow[dlgTable.getDataSet().columnAt("curfcy")],fcying=dataSetRow[dlgTable.getDataSet().columnAt("fcying")];if(curfcy!=fcying){throw new Error(Xjs.ResBundle.getResVal("FT-REC.********",e.cfg.title));}},doCheckBeforeShow:function(event){this.checkSelRowValid();this.checkSelRowFundType10();this.checkSelRowRclmfcyingGtZero();this.checkSelRowSameCol(event.table);}});snsoft.ft.rec.bno.invoker.RecBanknoEditBankcnameInvoker=function(parameter){snsoft.ft.rec.bno.invoker.RecBanknoEditBankcnameInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.rec.bno.invoker.RecBanknoEditBankcnameInvoker,snsoft.ft.rec.comm.invoker.GenRecClaimInvoker,{doCheckBeforeShow:function(event){this.checkSelectOne2(this.table,this.cfg.title);},onDlgShow:function(e){var dlg=this.newEditDialog(e),dlgtbl=snsoft.ft.utils.FTUtils.getTableFromPanel(dlg);this.oldDlgValues={};this.oldDlgValues.isgencfg=dlgtbl.dataSet.get("isgencfg");return dlg;},doCheckBeforeOk:function(e,dlg){var dlgtbl=snsoft.ft.utils.FTUtils.getTableFromPanel(dlg);this.checkNonBlank(dlgtbl,0);if(!(dlgtbl.dataSet.get("isgencfg")==this.oldDlgValues.isgencfg))return true;if(!this.dataChanged(dlgtbl,"isgencfg"))return false;return true;},buildGenRclmParams:function(genRclmParams,e,dlg){var dlgtbl=snsoft.ft.utils.FTUtils.getTableFromPanel(dlg);genRclmParams.extParams={ccode:dlgtbl.dataSet.get("ccode"),isgencfg:dlgtbl.dataSet.get("isgencfg")};}});snsoft.ft.rec.bno.invoker.RecBanknoEditFeefcyInvoker=function(parameter){snsoft.ft.rec.bno.invoker.RecBanknoEditFeefcyInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.rec.bno.invoker.RecBanknoEditFeefcyInvoker,snsoft.ft.rec.comm.invoker.GenRecClaimInvoker,{doCheckBeforeShow:function(event){this.checkSelectOne2(this.table,this.cfg.title);this.checkSelRowValid();},onDlgShow:function(e){var dlg=this.newEditDialog(e),dlgtbl=snsoft.ft.utils.FTUtils.getTableFromPanel(dlg);dlgtbl.dataSet.setValue("editfeefcy",dlgtbl.dataSet.getValue("feefcy"));var editfeefcyTc=dlgtbl.getColumn("editfeefcy");editfeefcyTc.v_rg_i=0;editfeefcyTc.v_rg_mic=true;var rclmTable=Xjs.util.TableUtils.getTable_$Panel_$String(e.table,"ft_rec_rclm"),feefcyTc=rclmTable.getColumn("feefcy");editfeefcyTc.v_dg_i=feefcyTc.v_dg_i;editfeefcyTc.v_dg_f=feefcyTc.v_dg_f;dlgtbl.getValidChecks(editfeefcyTc);return dlg;},doCheckBeforeOk:function(e,dlg){var dlgtbl=snsoft.ft.utils.FTUtils.getTableFromPanel(dlg);this.checkNonBlank(dlgtbl,0);var rclmfcying=this.getSelOneRowValue(e.table,"rclmfcying"),recfcy=this.getSelOneRowValue(e.table,"recfcy"),editfeefcy=dlgtbl.dataSet.getValue("editfeefcy");if(rclmfcying!=recfcy){var subFeefcy=0;for(var j=0;j<e.table.dataSet.getRowCount();j++){subFeefcy=snsoft.ft.utils.FTUtils.add(subFeefcy,e.table.dataSet.getValue("feefcy",j));}if(editfeefcy<subFeefcy){var a=dlgtbl.getColumn("editfeefcy").title,b=Xjs.ResBundle.getResVal("title.F.tab.rclmg"),c=e.table.getColumn("feefcy").title;throw new Error(Xjs.ResBundle.getResVal("FT-REC.********",a,b,c));}}var feefcy=dlgtbl.dataSet.getValue("feefcy");if(feefcy==editfeefcy)return false;return true;},buildGenRclmParams:function(genRclmParams,e,dlg){var dlgtbl=snsoft.ft.utils.FTUtils.getTableFromPanel(dlg);genRclmParams.extParams={feefcy:dlgtbl.dataSet.get("editfeefcy")};}});snsoft.ft.rec.bno.invoker.RecBanknoEditFundTypeInvoker=function(parameter){snsoft.ft.rec.bno.invoker.RecBanknoEditFundTypeInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.rec.bno.invoker.RecBanknoEditFundTypeInvoker,snsoft.ft.rec.comm.invoker.GenRecClaimInvoker,{doCheckBeforeShow:function(event){if(this.recFundType=="10")this.checkSelRowFundType20();else if(this.recFundType=="20")this.checkSelRowFundType10();var rows=this.table.getSelectedRowNumbers(),no_rclmfcyed_rows=rows.filter((r)=>{var rclmfcyed=this.dataSet.getValue("rclmfcyed",r);return rclmfcyed&&rclmfcyed!==0;});if(no_rclmfcyed_rows!=null&&no_rclmfcyed_rows.length>0){throw new Error(Xjs.ResBundle.getResVal("FT-REC.********"));}},getConfirmMessageMacro:function(event){var colname="recfundtype";if(this.recFundType=="10")return "将"+this.getCodeDataName(colname,"20")+"置为"+this.getCodeDataName(colname,"10");else if(this.recFundType=="20")return "将"+this.getCodeDataName(colname,"10")+"置为"+this.getCodeDataName(colname,"20");return snsoft.ft.rec.bno.invoker.RecBanknoEditFundTypeInvoker.superclass.getConfirmMessageMacro.call(this,event);},buildGenRclmParams:function(genRclmParams,e,dlg){genRclmParams.extParams={recFundType:this.recFundType};}});snsoft.ft.rec.bno.invoker.RecBanknoRecRegInvoker=function(parameter){snsoft.ft.rec.bno.invoker.RecBanknoRecRegInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.rec.bno.invoker.RecBanknoRecRegInvoker,snsoft.ft.rec.comm.invoker.GenRecClaimInvoker,{doCheckBeforeShow:function(event){this.checkSelRowFundType10();this.checkSelRowBnostatusNot90();this.checkSelRowRclmfcyingGtZero2("FT-REC.********",event.cfg.title);snsoft.ft.rec.bno.invoker.RecBanknoRecRegInvoker.superclass.doCheckBeforeShow.call(this,event);},afterInvoke:function(event){snsoft.ft.rec.bno.invoker.RecBanknoRecRegInvoker.superclass.afterInvoke.call(this,event);var tsInfo=Xjs.ResBundle.getResVal("Dlg.Info"),msg=Xjs.ResBundle.getResVal("FT-REC.msg.success");Xjs.ui.UIUtil.showSuccessInfoDialog(tsInfo,msg);}});snsoft.ft.rec.bno.invoker.RecBanknoSearchLeLogInvoker=function(parameter){snsoft.ft.rec.bno.invoker.RecBanknoSearchLeLogInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.rec.bno.invoker.RecBanknoSearchLeLogInvoker,snsoft.ft.rec.comm.invoker.GenRecClaimInvoker,{onDlgShow:function(e){var bnoicodes=this.getSelRowInnercodes(e.table),p={bnoicode:"@"+bnoicodes};if(!this.refreshParams)this.refreshParams={};Xjs.apply(this.refreshParams,p);return snsoft.ft.rec.bno.invoker.RecBanknoSearchLeLogInvoker.superclass.onDlgShow.call(this,e);}});snsoft.ft.rec.bno.invoker.RecBanknoSelPayLrpInvoker=function(parameter){snsoft.ft.rec.bno.invoker.RecBanknoSelPayLrpInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.rec.bno.invoker.RecBanknoSelPayLrpInvoker,snsoft.ft.rec.comm.invoker.GenRecClaimInvoker,{beforeCheck:function(event){this.checkSelRow(event.table);this.checkSelectOne("该按钮");return null;},doCheckBeforeOk:function(e,dlg){var dlgTable=snsoft.ft.utils.FTUtils.getTableFromPanel(dlg);this.postRow(dlgTable);this.verifySelectErr(dlgTable);{var selectRows=dlgTable.getSelectedRowNumbers(dlgTable.rowMutiSelectable?0:1),allSelected=snsoft.ft.utils.FTUtils.getAllDataSetRow(dlgTable,selectRows,dlgTable.getDataSet().columnAt(this.matchCol)),srcvals=snsoft.ft.utils.FTUtils.getSrcMatchCodes(dlgTable,allSelected,this.matchCol,this.extSrcValueCols);for(var k1 in srcvals){var srcValObj=srcvals[k1];for(var k2 in srcValObj){var v=srcValObj[k2];if(v==0)throw new Error(Xjs.ResBundle.getResVal("FT-REC.********",dlgTable.getColumn("curfcy").title));}}}this.checkSelRowSameCol2(dlgTable,"srccode");return snsoft.ft.rec.bno.invoker.RecBanknoSelPayLrpInvoker.superclass.doCheckBeforeOk.call(this,e,dlg);},buildGenRclmParams:function(genRclmParams,e,dlg){genRclmParams.matchCol=this.matchCol;var dlgTable=snsoft.ft.utils.FTUtils.getTableFromPanel(dlg),selectRows=dlgTable.getSelectedRowNumbers(dlgTable.rowMutiSelectable?0:1),allSelected=snsoft.ft.utils.FTUtils.getAllDataSetRow(dlgTable,selectRows,dlgTable.getDataSet().columnAt(this.matchCol)),srcvals=snsoft.ft.utils.FTUtils.getSrcMatchCodes(dlgTable,allSelected,this.matchCol,this.extSrcValueCols);genRclmParams.matchCodesMap=srcvals;},doCheckBeforeShow:function(event){this.checkSelRowValid();this.checkSelRowFundType10();this.checkSelRowRclmfcyingGtZero();this.checkSelRowSameCol(event.table);}});snsoft.ft.rec.bno.invoker.RecBanknoSettLeLogInvoker=function(parameter){snsoft.ft.rec.bno.invoker.RecBanknoSettLeLogInvoker.superclass.constructor.call(this,parameter);};Xjs.extend(snsoft.ft.rec.bno.invoker.RecBanknoSettLeLogInvoker,snsoft.ft.rec.comm.invoker.GenRecClaimInvoker,{doCheckBeforeShow:function(event){this.checkSelRowBnostatusNot90();this.checkSelRowRclmfcyingGtZero2("FT-REC.********",event.cfg.title);},onDlgShow:function(e){var bnoicodes=this.getSelRowInnercodes(e.table),p={bnoicode:"@"+bnoicodes};if(!this.refreshParams)this.refreshParams={};Xjs.apply(this.refreshParams,p);var dlg=snsoft.ft.rec.bno.invoker.RecBanknoSettLeLogInvoker.superclass.onDlgShow.call(this,e),dlgtbl=snsoft.ft.utils.FTUtils.getTableFromPanel(dlg),lefcyTc=dlgtbl.getColumn("lefcy");lefcyTc.v_rg_i=0;lefcyTc.v_rg_mic=false;var rclmTable=Xjs.util.TableUtils.getTable_$Panel_$String(e.table,"ft_rec_rclm"),feefcyTc=rclmTable.getColumn("feefcy");lefcyTc.v_dg_i=feefcyTc.v_dg_i;lefcyTc.v_dg_f=feefcyTc.v_dg_f;dlgtbl.getValidChecks(lefcyTc);return dlg;},doCheckBeforeOk:function(e,dlg){var dlgtbl=snsoft.ft.utils.FTUtils.getTableFromPanel(dlg);this.checkSelRow(dlgtbl);this.checkNonBlank(dlgtbl,8);var rows=dlgtbl.getSelectedRowNumbers(),no_valid_rows=rows.filter((r)=>{var lefcy=dlgtbl.dataSet.getValue("lefcy",r),rclmfcying=dlgtbl.dataSet.getValue("rclmfcying",r);return lefcy>rclmfcying;});if(no_valid_rows!=null&&no_valid_rows.length>0){throw new Error(Xjs.ResBundle.getResVal("FT-REC.********",dlgtbl.getColumn("lefcy").title,dlgtbl.getColumn("rclmfcying").title));}return snsoft.ft.rec.bno.invoker.RecBanknoSettLeLogInvoker.superclass.doCheckBeforeOk.call(this,e,dlg);},buildGenRclmParams:function(genRclmParams,e,dlg){var dlgtbl=snsoft.ft.utils.FTUtils.getTableFromPanel(dlg);genRclmParams.reficodes=this.getSelRowInnercodes(dlgtbl);genRclmParams.extParams={leDatas:this.getSelRowValues(dlgtbl)};}});