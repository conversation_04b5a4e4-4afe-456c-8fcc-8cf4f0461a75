Xjs.loadedXjs.push("snsoft/ft/fund/forex/exa");
/*snsoft/ft/forex/exa/lis/ForexExcAppCtrlJSListener.java*/
Xjs.namespace("snsoft.ft.forex.exa.lis");
snsoft.ft.forex.exa.lis.ForexExcAppCtrlJSListener=function(params){
    snsoft.ft.forex.exa.lis.ForexExcAppCtrlJSListener.superclass.constructor.call(this,params);
};
Xjs.extend(snsoft.ft.forex.exa.lis.ForexExcAppCtrlJSListener,snsoft.plat.bas.busi.SystemFunctionListener,{
  _js$className_:"snsoft.ft.forex.exa.lis.ForexExcAppCtrlJSListener",
    /*snsoft.ft.forex.exa.lis.ForexExcAppCtrlJSListener.initComponent*/
    initComponent:function(table,values)
    {
        snsoft.ft.forex.exa.lis.ForexExcAppCtrlJSListener.superclass.initComponent.call(this,table,values);
        if(!this.mTable)
        {
            this.mTable = table;
            this.gTable = this.getTable(this.mTable,"ft_forex_exag");
        }
    },
    /*snsoft.ft.forex.exa.lis.ForexExcAppCtrlJSListener.dataSetFieldPosted*/
    dataSetFieldPosted:function(dataSet,event)
    {
        snsoft.ft.forex.exa.lis.ForexExcAppCtrlJSListener.superclass.dataSetFieldPosted.call(this,dataSet,event);
        var isassignbank = dataSet.getValue("isassignbank"),
            fxprodtype = dataSet.getValue("fxprodtype");
        if(event.columnName=="isassignbank" && (!isassignbank || "N"==isassignbank) || "8"==fxprodtype)
        {
            dataSet.setValue("abankcode",null);
        }
        if(event.columnName=="isassignbank" && (!isassignbank || "N"==isassignbank) || "8"!=fxprodtype)
        {
            dataSet.setValue("optccode",null);
        }
        var ispo = dataSet.getValue("ispo");
        if(event.columnName=="ispo" && (!ispo || "N"==ispo))
        {
            dataSet.setValue("polockrate",null);
            dataSet.setValue("istrans",null);
            dataSet.setValue("nearpolockrate",null);
            dataSet.setValue("farpolockrate",null);
            dataSet.setValue("sppoint",null);
            dataSet.setValue("istrans",null);
        }
        var istrans = dataSet.getValue("istrans");
        if(event.columnName=="ispo" || event.columnName=="istrans")
        {
            if(!ispo || "N"==ispo || (!istrans || "Y"==istrans))
            {
                dataSet.setValue("poduedate",null);
            }
        }
        if(event.columnName=="nearpolockrate" || event.columnName=="farpolockrate")
        {
            var nearpolockrate = dataSet.getValue("nearpolockrate"),
                farpolockrate = dataSet.getValue("farpolockrate");
            if(nearpolockrate != null && farpolockrate != null)
            {
                dataSet.setValue("sppoint",dataSet.getValue("farpolockrate") - dataSet.getValue("nearpolockrate"));
            }
        }
        if(event.columnName=="fxprodtype" && (!fxprodtype || "3"!=fxprodtype))
        {
            var rows = this.gTable.getDataSet().rows;
            for(var i=0;i < rows.length;i++)
            {
                this.gTable.getDataSet().setRowValues(rows[i],{swapextype:null});
            }
        }
    }
});
